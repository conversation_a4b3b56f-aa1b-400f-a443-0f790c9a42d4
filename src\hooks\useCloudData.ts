import { useState, useEffect } from 'react';
import { cmsService } from '../services/cmsService';
import { cloudCMSService } from '../services/cloudCMSService';
import { CMSData } from '../data/cmsData';

// Custom hook for loading data from cloud with local fallback and real-time updates
export const useCloudData = () => {
  const [data, setData] = useState<CMSData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const loadData = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);

      // Always try to get fresh data from cloud for real-time updates
      if (forceRefresh) {
        cloudCMSService.clearCache();

        // Add cache busting for browser cache
        const cacheBustParam = `?cb=${Date.now()}`;
        console.log('🔄 Cache busting enabled:', cacheBustParam);
      }

      const cmsData = await cloudCMSService.fetchCloudData();
      setData(cmsData);
      setLastUpdated(new Date());

      console.log('✅ Real-time data loaded successfully');
    } catch (err) {
      console.error('Error loading cloud data:', err);
      setError('Failed to load cloud data, using local data');

      // Fallback to local CMS service
      const localData = cmsService.getAllData();
      setData(localData);
      setLastUpdated(new Date());
    } finally {
      setLoading(false);
    }
  };

  // Listen for website rebuild notifications
  useEffect(() => {
    const handleRebuildTriggered = () => {
      console.log('🔄 Website rebuild triggered, refreshing data...');
      setTimeout(() => {
        loadData(true); // Force refresh after rebuild
      }, 2000); // Wait 2 seconds for rebuild to start
    };

    window.addEventListener('websiteRebuildTriggered', handleRebuildTriggered);

    return () => {
      window.removeEventListener('websiteRebuildTriggered', handleRebuildTriggered);
    };
  }, []);

  // Auto-refresh data every 30 seconds for real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      loadData(true);
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    loadData();
  }, []);

  return {
    data,
    loading,
    error,
    lastUpdated,
    reload: loadData,
    forceRefresh: () => loadData(true)
  };
};

// Hook for projects specifically
export const useCloudProjects = () => {
  const { data, loading, error, lastUpdated, reload, forceRefresh } = useCloudData();

  return {
    projects: data?.projects || [],
    loading,
    error,
    lastUpdated,
    reload,
    forceRefresh
  };
};

// Hook for blog posts specifically
export const useCloudBlogPosts = () => {
  const { data, loading, error, lastUpdated, reload, forceRefresh } = useCloudData();

  return {
    blogPosts: data?.blogPosts || [],
    loading,
    error,
    lastUpdated,
    reload,
    forceRefresh
  };
};

// Hook for personal info specifically
export const useCloudPersonalInfo = () => {
  const { data, loading, error, lastUpdated, reload, forceRefresh } = useCloudData();

  return {
    personalInfo: data?.personalInfo || null,
    loading,
    error,
    lastUpdated,
    reload,
    forceRefresh
  };
};
