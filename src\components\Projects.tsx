import { ArrowRight, Calendar, Code2, ExternalLink, Eye, Github, Star, Users, X } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { useCloudProjects } from '../hooks/useCloudData';
import { ImprovedScrollAnimation, ImprovedStaggerAnimation } from './ImprovedScrollAnimations';

const Projects = () => {
  const { t } = useLanguage();
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState('All');
  const [currentPage, setCurrentPage] = useState(1);
  const [showAll, setShowAll] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  // Use cloud data hook
  const { projects } = useCloudProjects();

  const PROJECTS_PER_PAGE = 6; // Show 6 projects per page initially

  const staticProjects = [
    {
      id: '1',
      title: 'Advanced Portfolio Website',
      description: 'Modern portfolio with 3D animations, particle systems, and multi-language support.',
      longDescription: 'A cutting-edge portfolio website featuring advanced 3D animations, interactive particle systems, multi-language support for 7 languages, dark theme, responsive design, and optimized performance. Built with modern React ecosystem and advanced animation libraries.',
      image: 'https://images.pexels.com/photos/196644/pexels-photo-196644.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['React', 'TypeScript', 'Framer Motion', 'Tailwind CSS', 'Vite', 'EmailJS'],
      github: 'https://github.com/NuralBhardwaj/',
      live: 'https://nuralbhardwaj.me',
      category: 'Full Stack',
      featured: true,
      stats: { stars: 145, forks: 28, contributors: 1 },
      timeline: '3 months',
      status: 'Completed',
      createdAt: '2024-01-15',
      updatedAt: '2024-12-15'
    },
    {
      id: '2',
      title: 'Real-Time Collaboration Suite',
      description: 'Advanced collaboration platform with video conferencing and document sharing.',
      longDescription: 'A comprehensive collaboration platform featuring real-time document editing, video conferencing, screen sharing, and project management tools. Supports thousands of concurrent users with optimized WebRTC implementation.',
      image: 'https://images.pexels.com/photos/3183150/pexels-photo-3183150.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['Next.js', 'Socket.io', 'WebRTC', 'MongoDB', 'Docker', 'Kubernetes'],
      github: 'https://github.com/NuralBhardwaj/',
      live: '#',
      category: 'Web App',
      featured: true,
      stats: { stars: 189, forks: 45, contributors: 12 },
      timeline: '8 months',
      status: 'In Progress',
      createdAt: '2024-02-01',
      updatedAt: '2024-12-10'
    },
    {
      id: '3',
      title: 'Blockchain DeFi Dashboard',
      description: 'Decentralized finance dashboard with portfolio tracking and yield farming.',
      longDescription: 'A sophisticated DeFi dashboard providing real-time portfolio tracking, yield farming opportunities, liquidity pool analytics, and automated trading strategies. Integrates with multiple blockchain networks.',
      image: 'https://images.pexels.com/photos/267350/pexels-photo-267350.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['React', 'Web3.js', 'Solidity', 'Ethereum', 'IPFS', 'Chart.js'],
      github: 'https://github.com/NuralBhardwaj/',
      live: '#',
      category: 'Blockchain',
      featured: false,
      stats: { stars: 156, forks: 34, contributors: 5 },
      timeline: '4 months',
      status: 'Completed',
      createdAt: '2024-03-01',
      updatedAt: '2024-07-01'
    },
    {
      id: '4',
      title: 'ML-Powered Analytics Platform',
      description: 'Advanced analytics platform with machine learning insights and predictions.',
      longDescription: 'An enterprise-grade analytics platform featuring machine learning models for predictive analytics, automated report generation, and real-time data visualization. Processes millions of data points daily.',
      image: 'https://images.pexels.com/photos/186461/pexels-photo-186461.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['Python', 'TensorFlow', 'React', 'FastAPI', 'PostgreSQL', 'Docker'],
      github: 'https://github.com/NuralBhardwaj/',
      live: '#',
      category: 'AI/ML',
      featured: true,
      stats: { stars: 298, forks: 89, contributors: 15 },
      timeline: '10 months',
      status: 'Completed',
      createdAt: '2023-12-01',
      updatedAt: '2024-10-01'
    },
    {
      id: '5',
      title: 'Mobile-First Social Platform',
      description: 'Modern social platform with AR filters and real-time messaging.',
      longDescription: 'A cutting-edge social platform featuring AR filters, real-time messaging, story sharing, and advanced privacy controls. Built with React Native for cross-platform compatibility.',
      image: 'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['React Native', 'Node.js', 'MongoDB', 'WebRTC', 'AR.js', 'Firebase'],
      github: 'https://github.com/NuralBhardwaj/',
      live: '#',
      category: 'Mobile',
      featured: false,
      stats: { stars: 167, forks: 42, contributors: 7 },
      timeline: '5 months',
      status: 'In Progress',
      createdAt: '2024-06-01',
      updatedAt: '2024-11-01'
    },
    {
      id: '6',
      title: 'Cloud Infrastructure Manager',
      description: 'Comprehensive cloud infrastructure management and monitoring solution.',
      longDescription: 'A powerful cloud infrastructure management platform providing automated deployment, monitoring, scaling, and cost optimization across multiple cloud providers. Features advanced security and compliance tools.',
      image: 'https://images.pexels.com/photos/267371/pexels-photo-267371.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['Go', 'Kubernetes', 'Terraform', 'Prometheus', 'Grafana', 'AWS'],
      github: 'https://github.com/NuralBhardwaj/',
      live: '#',
      category: 'DevOps',
      featured: false,
      stats: { stars: 203, forks: 56, contributors: 11 },
      timeline: '7 months',
      status: 'Completed',
      createdAt: '2024-04-01',
      updatedAt: '2024-11-01'
    }
  ];

  // Use CMS data if available, otherwise fall back to static data
  const allProjects = projects.length > 0 ? projects : staticProjects;

  const categories = ['All', ...Array.from(new Set(allProjects.map(p => p.category)))];
  const filteredProjects = activeFilter === 'All'
    ? allProjects
    : allProjects.filter(p => p.category === activeFilter);

  const featuredProjects = allProjects.filter(p => p.featured);

  // Pagination logic
  const displayedProjects = showAll
    ? filteredProjects
    : filteredProjects.slice(0, currentPage * PROJECTS_PER_PAGE);

  const hasMoreProjects = filteredProjects.length > PROJECTS_PER_PAGE && !showAll;

  // Reset pagination when filter changes
  useEffect(() => {
    setCurrentPage(1);
    setShowAll(false);
  }, [activeFilter]);

  return (
    <section id="projects" ref={sectionRef} className="py-32 relative overflow-hidden">
      {/* Ultra Advanced Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
      
      {/* Enhanced Dynamic Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-full blur-3xl animate-float-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-cyan-600/20 to-blue-600/20 rounded-full blur-3xl animate-float-reverse"></div>

        {/* Additional Floating Elements */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-green-600/10 to-emerald-600/10 rounded-full blur-2xl animate-pulse-slow"></div>

        {/* Interactive Code Symbols */}
        {['<', '>', '{', '}', '/', '*'].map((symbol, index) => (
          <div
            key={symbol}
            className="absolute text-purple-400/10 text-6xl font-mono animate-float-gentle"
            style={{
              left: `${10 + Math.random() * 80}%`,
              top: `${10 + Math.random() * 80}%`,
              animationDelay: `${index * 0.5}s`,
              animationDuration: `${4 + Math.random() * 2}s`
            }}
          >
            {symbol}
          </div>
        ))}
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header with Improved Scroll Animation */}
        <ImprovedScrollAnimation animation="fadeInUp" delay={0.2} className="text-center mb-20">
          <ImprovedScrollAnimation animation="scaleIn" delay={0.4}>
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 backdrop-blur-sm border border-purple-500/30 rounded-full px-6 py-3 mb-8">
              <Code2 className="w-5 h-5 text-purple-400" />
              <span className="text-purple-300 font-medium">{t('projects.featured.title')}</span>
            </div>
          </ImprovedScrollAnimation>

          <ImprovedScrollAnimation animation="scaleIn" delay={0.6}>
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 leading-tight">
              <span className="relative inline-block">
                <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-cyan-400 bg-clip-text text-transparent">
                  {t('projects.title')}
                </span>
                <div className="absolute -inset-2 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 blur-2xl animate-pulse"></div>
              </span>
            </h2>
          </ImprovedScrollAnimation>

          <ImprovedScrollAnimation animation="fadeInLeft" delay={0.8}>
            <div className="w-32 h-1 bg-gradient-to-r from-purple-500 via-pink-500 to-cyan-500 mx-auto rounded-full mb-8"></div>
          </ImprovedScrollAnimation>

          <ImprovedScrollAnimation animation="fadeInRight" delay={1.0}>
            <p className="text-lg sm:text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
              {t('projects.subtitle')}
            </p>
          </ImprovedScrollAnimation>
        </ImprovedScrollAnimation>

        {/* Featured Projects Carousel with Improved Scroll Animation */}
        <ImprovedScrollAnimation animation="fadeInUp" delay={0.4} className="mb-20">
          <ImprovedScrollAnimation animation="scaleIn" delay={0.6}>
            <h3 className="text-2xl sm:text-3xl font-bold text-white mb-8 text-center">
              <span className="bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
                {t('projects.featured.subtitle')}
              </span>
            </h3>
          </ImprovedScrollAnimation>

          <ImprovedStaggerAnimation className="grid lg:grid-cols-2 gap-6 sm:gap-8">
            {featuredProjects.slice(0, 2).map((project) => (
              <div
                key={project.id}
                className="project-card group relative bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm rounded-3xl overflow-hidden border border-white/10 hover:border-purple-500/30 transition-all duration-700 transform hover:scale-105"
              >
                {/* Featured Badge */}
                <div className="absolute top-4 left-4 z-20 bg-gradient-to-r from-yellow-500 to-orange-500 text-black px-3 py-1 rounded-full text-xs font-bold flex items-center space-x-1">
                  <Star className="w-3 h-3" />
                  <span>{t('projects.featured.badge')}</span>
                </div>

                {/* Project Image */}
                <div className="relative h-64 overflow-hidden">
                  <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>
                  
                  {/* Overlay Actions */}
                  <div className="absolute inset-0 flex items-center justify-center space-x-4 opacity-0 group-hover:opacity-100 transition-all duration-500">
                    <button
                      onClick={() => setSelectedProject(project.id)}
                      className="p-4 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors duration-300 transform hover:scale-110"
                    >
                      <Eye className="w-6 h-6" />
                    </button>
                    <a
                      href={project.github}
                      className="p-4 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors duration-300 transform hover:scale-110"
                    >
                      <Github className="w-6 h-6" />
                    </a>
                    <a
                      href={project.live}
                      className="p-4 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors duration-300 transform hover:scale-110"
                    >
                      <ExternalLink className="w-6 h-6" />
                    </a>
                  </div>
                </div>

                {/* Project Info */}
                <div className="p-8">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-2xl font-bold text-white group-hover:text-purple-400 transition-colors duration-300 text-spacing-fix text-overlay-fix">
                      {project.title}
                    </h3>
                    <span className={`px-3 py-1 rounded-full text-xs font-bold text-spacing-fix ${
                      project.status === 'Completed'
                        ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                        : 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                    }`}>
                      {project.status === 'Completed' ? t('projects.status.completed') : t('projects.status.progress')}
                    </span>
                  </div>

                  <p className="text-gray-400 leading-relaxed mb-6 text-spacing-fix card-content" style={{ wordSpacing: 'normal', letterSpacing: 'normal' }}>
                    {project.description}
                  </p>
                  
                  {/* Stats */}
                  <div className="flex items-center space-x-6 mb-6 text-sm text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-400" />
                      <span>{project.stats.stars}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4 text-blue-400" />
                      <span>{project.stats.contributors}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4 text-purple-400" />
                      <span>{project.timeline}</span>
                    </div>
                  </div>
                  
                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.slice(0, 4).map((tech) => (
                      <span
                        key={tech}
                        className="px-3 py-1 bg-gradient-to-r from-purple-500/20 to-cyan-500/20 text-purple-300 rounded-full text-xs font-medium border border-purple-500/30"
                      >
                        {tech}
                      </span>
                    ))}
                    {project.technologies.length > 4 && (
                      <span className="px-3 py-1 bg-gray-700/50 text-gray-300 rounded-full text-xs font-medium">
                        +{project.technologies.length - 4}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </ImprovedStaggerAnimation>
        </ImprovedScrollAnimation>

        {/* Category Filter with Improved Scroll Animation */}
        <ImprovedStaggerAnimation className="flex flex-wrap justify-center gap-3 sm:gap-4 mb-16">
          {categories.map((category) => (
            <div
              key={category}
              onClick={() => setActiveFilter(category)}
              className={`px-4 sm:px-6 md:px-8 py-3 sm:py-4 rounded-2xl font-bold text-sm sm:text-base md:text-lg transition-all duration-500 transform hover:scale-105 cursor-pointer ${
                activeFilter === category
                  ? 'bg-gradient-to-r from-purple-600 to-cyan-600 text-white shadow-2xl shadow-purple-500/25'
                  : 'bg-white/5 text-gray-300 hover:bg-white/10 border border-white/10 hover:border-white/20'
              }`}
            >
              {category}
              {activeFilter === category && (
                <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-cyan-600 opacity-30 blur-lg rounded-2xl"></div>
              )}
            </div>
          ))}
        </ImprovedStaggerAnimation>

        {/* Enhanced Projects Grid */}
        <ImprovedStaggerAnimation className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {displayedProjects.map((project) => (
            <div
              key={project.id}
              className="project-card group relative rounded-3xl overflow-hidden transition-all duration-500 transform hover:scale-[1.03] hover:-translate-y-3"
              style={{
                background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.98) 0%, rgba(17, 24, 39, 0.98) 30%, rgba(31, 41, 55, 0.95) 70%, rgba(0, 0, 0, 0.98) 100%)',
                backdropFilter: 'blur(32px) saturate(180%)',
                WebkitBackdropFilter: 'blur(32px) saturate(180%)',
                border: '1px solid rgba(255, 255, 255, 0.15)',
                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8), inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05)',
                isolation: 'isolate',
                zIndex: 20,
                position: 'relative'
              }}
            >
              {/* Additional backdrop layer for better separation */}
              <div
                className="absolute inset-0 rounded-3xl"
                style={{
                  background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(17, 24, 39, 0.6) 50%, rgba(0, 0, 0, 0.4) 100%)',
                  zIndex: -1
                }}
              />

              {/* Project Image with Enhanced Effects */}
              <div className="relative h-56 overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"
                />

                {/* Multi-layer Gradient Overlays */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/30 to-transparent"></div>
                <div className="absolute inset-0 bg-gradient-to-br from-purple-600/20 via-transparent to-cyan-600/20 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

                {/* Status & Category Badges */}
                <div className="absolute top-4 left-4 right-4 flex justify-between items-start">
                  <span className={`px-3 py-1.5 rounded-full text-xs font-bold backdrop-blur-sm border ${
                    project.status === 'Completed'
                      ? 'bg-green-500/20 text-green-300 border-green-400/30'
                      : 'bg-blue-500/20 text-blue-300 border-blue-400/30'
                  }`}>
                    {project.status === 'Completed' ? t('projects.status.completed') : t('projects.status.progress')}
                  </span>

                  <div className="flex items-center space-x-2">
                    {project.featured && (
                      <div className="flex items-center space-x-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-black px-2 py-1 rounded-full text-xs font-bold">
                        <Star className="w-3 h-3" />
                        <span>FEATURED</span>
                      </div>
                    )}
                    <span className="px-3 py-1.5 bg-black/60 backdrop-blur-sm text-white rounded-full text-xs font-medium border border-white/20">
                      {project.category}
                    </span>
                  </div>
                </div>

                {/* Enhanced Action Buttons */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 bg-black/20 backdrop-blur-sm">
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={() => setSelectedProject(project.id)}
                      className="p-4 bg-gradient-to-r from-purple-600 to-purple-700 backdrop-blur-md rounded-2xl text-white hover:from-purple-500 hover:to-purple-600 transition-all duration-300 transform hover:scale-125 hover:-translate-y-2 shadow-xl hover:shadow-purple-500/40 border-2 border-purple-400/40 hover:border-purple-300/60"
                      title="View Details"
                    >
                      <Eye className="w-6 h-6" />
                    </button>
                    <a
                      href={project.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-4 bg-gradient-to-r from-gray-700 to-gray-800 backdrop-blur-md rounded-2xl text-white hover:from-gray-600 hover:to-gray-700 transition-all duration-300 transform hover:scale-125 hover:-translate-y-2 shadow-xl hover:shadow-gray-500/40 border-2 border-gray-500/40 hover:border-gray-400/60"
                      title="View Code"
                    >
                      <Github className="w-6 h-6" />
                    </a>
                    <a
                      href={project.live}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-4 bg-gradient-to-r from-cyan-600 to-cyan-700 backdrop-blur-md rounded-2xl text-white hover:from-cyan-500 hover:to-cyan-600 transition-all duration-300 transform hover:scale-125 hover:-translate-y-2 shadow-xl hover:shadow-cyan-500/40 border-2 border-cyan-400/40 hover:border-cyan-300/60"
                      title="Live Demo"
                    >
                      <ExternalLink className="w-6 h-6" />
                    </a>
                  </div>
                </div>

                {/* Project Stats Overlay */}
                <div className="absolute bottom-4 left-4 right-4 flex justify-between items-center opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-2 group-hover:translate-y-0">
                  <div className="flex items-center space-x-4 text-white text-sm">
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-400" />
                      <span className="font-medium">{project.stats.stars}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4 text-blue-400" />
                      <span className="font-medium">{project.stats.contributors}</span>
                    </div>
                  </div>
                  <div className="text-white text-sm font-medium bg-black/40 backdrop-blur-sm px-2 py-1 rounded-lg">
                    {project.timeline}
                  </div>
                </div>
              </div>

              {/* Enhanced Content Section */}
              <div className="p-6 relative">
                {/* Title */}
                <h3 className="text-xl font-bold text-white mb-3 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-cyan-400 group-hover:bg-clip-text transition-all duration-300 text-spacing-fix line-clamp-2">
                  {project.title}
                </h3>

                {/* Description */}
                <p className="text-gray-400 text-sm leading-relaxed mb-4 group-hover:text-gray-300 transition-colors duration-300 text-spacing-fix card-content line-clamp-3" style={{ wordSpacing: 'normal', letterSpacing: 'normal' }}>
                  {project.description}
                </p>

                {/* Technologies with Enhanced Design */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.technologies.slice(0, 3).map((tech, techIndex) => (
                    <span
                      key={tech}
                      className="px-3 py-1.5 bg-gradient-to-r from-white/10 to-white/5 text-gray-300 text-xs rounded-lg font-medium border border-white/10 hover:border-purple-400/50 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/20 hover:to-cyan-500/20 transition-all duration-300 transform hover:scale-105"
                      style={{
                        animationDelay: `${techIndex * 100}ms`
                      }}
                    >
                      {tech}
                    </span>
                  ))}
                  {project.technologies.length > 3 && (
                    <span className="px-3 py-1.5 bg-gradient-to-r from-purple-600/30 to-cyan-600/30 text-purple-200 text-xs rounded-lg font-medium border border-purple-400/30 hover:border-purple-300/50 transition-all duration-300 transform hover:scale-105">
                      +{project.technologies.length - 3}
                    </span>
                  )}
                </div>

                {/* Quick Action Footer */}
                <div className="flex items-center justify-between pt-4 border-t border-white/10">
                  <button
                    onClick={() => setSelectedProject(project.id)}
                    className="flex items-center space-x-2 text-purple-400 hover:text-purple-300 transition-colors duration-200 text-sm font-medium group/btn"
                  >
                    <span>View Details</span>
                    <ArrowRight className="w-4 h-4 transform group-hover/btn:translate-x-1 transition-transform duration-200" />
                  </button>

                  <div className="flex items-center space-x-2">
                    <a
                      href={project.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                      title="GitHub"
                    >
                      <Github className="w-4 h-4" />
                    </a>
                    <a
                      href={project.live}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                      title="Live Demo"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </a>
                  </div>
                </div>
              </div>

              {/* Enhanced Glow Effects */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-600/20 via-cyan-600/20 to-purple-600/20 opacity-0 group-hover:opacity-100 blur-xl transition-all duration-700 rounded-3xl -z-10"></div>
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-500/10 via-cyan-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 blur-2xl transition-all duration-700 rounded-3xl -z-10"></div>
            </div>
          ))}
        </ImprovedStaggerAnimation>

        {/* Load More Button */}
        {hasMoreProjects && (
          <ImprovedScrollAnimation animation="fadeInUp" delay={0.2} className="text-center mt-12">
            <button
              onClick={() => {
                if (currentPage * PROJECTS_PER_PAGE >= filteredProjects.length) {
                  setShowAll(true);
                } else {
                  setCurrentPage(prev => prev + 1);
                }
              }}
              className="group relative px-8 py-4 bg-gradient-to-r from-purple-600 to-cyan-600 text-white font-bold text-lg rounded-2xl transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25"
            >
              <span className="relative z-10">
                {currentPage * PROJECTS_PER_PAGE >= filteredProjects.length ? 'Show All Projects' : 'Load More Projects'}
              </span>
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-cyan-600 opacity-30 blur-lg rounded-2xl group-hover:opacity-50 transition-opacity duration-500"></div>
            </button>

            <p className="text-gray-400 text-sm mt-4">
              Showing {displayedProjects.length} of {filteredProjects.length} projects
            </p>
          </ImprovedScrollAnimation>
        )}

        {/* Enhanced Project Modal */}
        {selectedProject && (
          <div
            className="fixed inset-0 z-[9999] flex items-center justify-center p-4 modal-backdrop animate-fadeIn"
            style={{
              background: 'rgba(0, 0, 0, 0.98)',
              backdropFilter: 'blur(25px) saturate(200%)',
              WebkitBackdropFilter: 'blur(25px) saturate(200%)'
            }}
            onClick={() => setSelectedProject(null)}
          >
            <div
              className="relative rounded-3xl max-w-6xl w-full max-h-[90vh] overflow-y-auto shadow-2xl transform transition-all duration-300 modal-content animate-slideUp"
              style={{
                background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.98) 0%, rgba(17, 24, 39, 0.96) 30%, rgba(31, 41, 55, 0.94) 70%, rgba(0, 0, 0, 0.98) 100%)',
                backdropFilter: 'blur(50px) saturate(200%)',
                WebkitBackdropFilter: 'blur(50px) saturate(200%)',
                border: '3px solid rgba(147, 51, 234, 0.4)',
                boxShadow: '0 30px 60px -12px rgba(147, 51, 234, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 0 100px rgba(147, 51, 234, 0.2)'
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {(() => {
                const project = allProjects.find(p => p.id === selectedProject);
                if (!project) return null;
                
                return (
                  <>
                    {/* Modal Header */}
                    <div className="relative">
                      <img
                        src={project.image}
                        alt={project.title}
                        className="w-full h-80 object-cover rounded-t-3xl"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent rounded-t-3xl"></div>
                      
                      <button
                        onClick={() => setSelectedProject(null)}
                        className="absolute top-6 right-6 z-[60] p-4 bg-black/90 backdrop-blur-md rounded-full text-white hover:bg-red-600/95 hover:text-white transition-all duration-300 transform hover:scale-125 hover:rotate-90 border-3 border-white/30 hover:border-red-400/70 shadow-2xl hover:shadow-red-500/30"
                        title="Close Modal"
                        style={{
                          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
                        }}
                      >
                        <X className="w-7 h-7" />
                      </button>
                      
                      {project.featured && (
                        <div className="absolute top-6 left-6 bg-gradient-to-r from-yellow-500 to-orange-500 text-black px-4 py-2 rounded-full text-sm font-bold flex items-center space-x-2">
                          <Star className="w-4 h-4" />
                          <span>{t('projects.featured.badge')} PROJECT</span>
                        </div>
                      )}
                    </div>
                    
                    {/* Modal Content */}
                    <div className="p-8">
                      <div className="flex flex-col lg:flex-row lg:items-start lg:space-x-8">
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-6">
                            <h3 className="text-4xl font-black text-white text-spacing-fix modal-content">{project.title}</h3>
                            <span className={`px-4 py-2 rounded-full text-sm font-bold text-spacing-fix ${
                              project.status === 'Completed'
                                ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                                : 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                            }`}>
                              {project.status === 'Completed' ? t('projects.status.completed') : t('projects.status.progress')}
                            </span>
                          </div>

                          <p className="text-gray-400 text-lg leading-relaxed mb-8 text-spacing-fix modal-content">
                            {project.longDescription}
                          </p>
                          
                          {/* Project Stats */}
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-4 border border-white/10">
                              <div className="flex items-center space-x-2 mb-2">
                                <Star className="w-5 h-5 text-yellow-400" />
                                <span className="text-2xl font-bold text-white">{project.stats.stars}</span>
                              </div>
                              <div className="text-gray-400 text-sm">{t('projects.stats.stars')}</div>
                            </div>
                            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-4 border border-white/10">
                              <div className="flex items-center space-x-2 mb-2">
                                <Users className="w-5 h-5 text-blue-400" />
                                <span className="text-2xl font-bold text-white">{project.stats.contributors}</span>
                              </div>
                              <div className="text-gray-400 text-sm">{t('projects.stats.contributors')}</div>
                            </div>
                            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-4 border border-white/10">
                              <div className="flex items-center space-x-2 mb-2">
                                <Calendar className="w-5 h-5 text-purple-400" />
                                <span className="text-lg font-bold text-white">{project.timeline}</span>
                              </div>
                              <div className="text-gray-400 text-sm">{t('projects.timeline')}</div>
                            </div>
                            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-4 border border-white/10">
                              <div className="flex items-center space-x-2 mb-2">
                                <Code2 className="w-5 h-5 text-green-400" />
                                <span className="text-lg font-bold text-white">{project.category}</span>
                              </div>
                              <div className="text-gray-400 text-sm">Category</div>
                            </div>
                          </div>
                        </div>
                        
                        {/* Technologies Sidebar */}
                        <div className="lg:w-80">
                          <h4 className="text-xl font-bold text-white mb-4">{t('projects.technologies')}</h4>
                          <div className="space-y-3 mb-8">
                            {project.technologies.map((tech) => (
                              <div
                                key={tech}
                                className="flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"
                              >
                                <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-cyan-400 rounded-full"></div>
                                <span className="text-white font-medium">{tech}</span>
                              </div>
                            ))}
                          </div>
                          
                          {/* Enhanced Action Buttons */}
                          <div className="space-y-4">
                            <a
                              href={project.live}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center justify-center space-x-3 w-full px-6 py-4 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-500 hover:to-cyan-500 text-white rounded-2xl font-bold transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-purple-500/25"
                            >
                              <ExternalLink className="w-5 h-5" />
                              <span>{t('projects.viewProject')}</span>
                            </a>
                            <a
                              href={project.github}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center justify-center space-x-3 w-full px-6 py-4 bg-white/10 hover:bg-white/20 text-white rounded-2xl font-bold border border-white/20 hover:border-white/40 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-white/10"
                            >
                              <Github className="w-5 h-5" />
                              <span>{t('projects.viewCode')}</span>
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                );
              })()}
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default Projects;