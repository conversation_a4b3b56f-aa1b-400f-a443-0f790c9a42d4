// Advanced Image Optimization Service
// Supports WebP, AVIF, responsive images, and lazy loading

export interface ImageFormat {
  format: 'webp' | 'avif' | 'jpeg' | 'png' | 'svg';
  quality: number;
  size: { width: number; height: number };
}

export interface ResponsiveImageConfig {
  src: string;
  alt: string;
  sizes: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  quality?: number;
  formats?: ('webp' | 'avif' | 'jpeg' | 'png')[];
  lazy?: boolean;
  priority?: boolean;
}

export interface ImageOptimizationMetrics {
  totalImages: number;
  optimizedImages: number;
  bytesOriginal: number;
  bytesOptimized: number;
  formatSupport: {
    webp: boolean;
    avif: boolean;
  };
  loadingTimes: number[];
}

class ImageOptimizationService {
  private static instance: ImageOptimizationService;
  private metrics: ImageOptimizationMetrics;
  private formatSupport: { webp: boolean; avif: boolean };
  private observer: IntersectionObserver | null = null;
  private loadingImages: Set<string> = new Set();

  private constructor() {
    this.metrics = this.initializeMetrics();
    this.formatSupport = this.detectFormatSupport();
    this.initializeLazyLoading();
  }

  public static getInstance(): ImageOptimizationService {
    if (!ImageOptimizationService.instance) {
      ImageOptimizationService.instance = new ImageOptimizationService();
    }
    return ImageOptimizationService.instance;
  }

  // Initialize metrics
  private initializeMetrics(): ImageOptimizationMetrics {
    return {
      totalImages: 0,
      optimizedImages: 0,
      bytesOriginal: 0,
      bytesOptimized: 0,
      formatSupport: { webp: false, avif: false },
      loadingTimes: []
    };
  }

  // Detect browser support for modern image formats
  private detectFormatSupport(): { webp: boolean; avif: boolean } {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;

    const webpSupport = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    
    // AVIF detection is more complex
    const avifSupport = this.checkAVIFSupport();

    this.metrics.formatSupport = { webp: webpSupport, avif: avifSupport };
    
    console.log('🖼️ Image format support:', { webp: webpSupport, avif: avifSupport });
    
    return { webp: webpSupport, avif: avifSupport };
  }

  // Check AVIF support
  private checkAVIFSupport(): boolean {
    try {
      // Create a minimal AVIF data URL
      const avifDataURL = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
      
      const img = new Image();
      img.src = avifDataURL;
      
      return img.complete && img.naturalWidth > 0;
    } catch {
      return false;
    }
  }

  // Initialize lazy loading observer
  private initializeLazyLoading(): void {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              this.loadImage(img);
              this.observer?.unobserve(img);
            }
          });
        },
        {
          rootMargin: '50px 0px',
          threshold: 0.01
        }
      );
    }
  }

  // Generate optimized image sources
  public generateOptimizedSources(config: ResponsiveImageConfig): {
    sources: Array<{ srcset: string; type: string; sizes: string }>;
    fallback: string;
  } {
    const { src, sizes, quality = 85, formats = ['avif', 'webp', 'jpeg'] } = config;
    const sources: Array<{ srcset: string; type: string; sizes: string }> = [];

    // Generate sources for each supported format
    const supportedFormats = formats.filter(format => {
      if (format === 'avif') return this.formatSupport.avif;
      if (format === 'webp') return this.formatSupport.webp;
      return true;
    });

    supportedFormats.forEach(format => {
      const srcset = this.generateSrcSet(src, sizes, format, quality);
      const sizesAttr = this.generateSizesAttribute(sizes);
      
      sources.push({
        srcset,
        type: `image/${format}`,
        sizes: sizesAttr
      });
    });

    // Fallback to original or JPEG
    const fallback = this.optimizeImageUrl(src, sizes.desktop, 'jpeg', quality);

    return { sources, fallback };
  }

  // Generate srcset for responsive images
  private generateSrcSet(
    src: string, 
    sizes: ResponsiveImageConfig['sizes'], 
    format: string, 
    quality: number
  ): string {
    const breakpoints = [
      { width: sizes.mobile, descriptor: '480w' },
      { width: sizes.tablet, descriptor: '768w' },
      { width: sizes.desktop, descriptor: '1200w' },
      { width: sizes.desktop * 1.5, descriptor: '1800w' }, // 1.5x for high-DPI
      { width: sizes.desktop * 2, descriptor: '2400w' }    // 2x for retina
    ];

    return breakpoints
      .map(bp => `${this.optimizeImageUrl(src, bp.width, format, quality)} ${bp.descriptor}`)
      .join(', ');
  }

  // Generate sizes attribute
  private generateSizesAttribute(sizes: ResponsiveImageConfig['sizes']): string {
    return [
      `(max-width: 480px) ${sizes.mobile}px`,
      `(max-width: 768px) ${sizes.tablet}px`,
      `${sizes.desktop}px`
    ].join(', ');
  }

  // Optimize image URL (placeholder for actual CDN integration)
  private optimizeImageUrl(src: string, width: number, format: string, quality: number): string {
    // For local images, return as-is (in production, integrate with CDN)
    if (src.startsWith('/') || src.startsWith('./')) {
      return src;
    }

    // For external images (like Unsplash), add optimization parameters
    if (src.includes('unsplash.com')) {
      const url = new URL(src);
      url.searchParams.set('w', width.toString());
      url.searchParams.set('q', quality.toString());
      url.searchParams.set('fm', format);
      url.searchParams.set('fit', 'crop');
      url.searchParams.set('crop', 'smart');
      return url.toString();
    }

    // For other CDNs, implement specific optimization logic
    return this.applyCDNOptimization(src, width, format, quality);
  }

  // Apply CDN-specific optimizations
  private applyCDNOptimization(src: string, width: number, format: string, quality: number): string {
    // Cloudinary example
    if (src.includes('cloudinary.com')) {
      return src.replace('/upload/', `/upload/w_${width},q_${quality},f_${format}/`);
    }

    // ImageKit example
    if (src.includes('imagekit.io')) {
      const url = new URL(src);
      url.searchParams.set('tr', `w-${width},q-${quality},f-${format}`);
      return url.toString();
    }

    // Default: return original URL
    return src;
  }

  // Create optimized image element
  public createOptimizedImage(config: ResponsiveImageConfig): HTMLPictureElement {
    const { sources, fallback } = this.generateOptimizedSources(config);
    const picture = document.createElement('picture');

    // Add source elements for each format
    sources.forEach(source => {
      const sourceEl = document.createElement('source');
      sourceEl.srcset = source.srcset;
      sourceEl.type = source.type;
      sourceEl.sizes = source.sizes;
      picture.appendChild(sourceEl);
    });

    // Add fallback img element
    const img = document.createElement('img');
    img.src = config.lazy ? 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9InRyYW5zcGFyZW50Ii8+PC9zdmc+' : fallback;
    img.alt = config.alt;
    img.loading = config.priority ? 'eager' : 'lazy';
    img.decoding = 'async';

    if (config.lazy) {
      img.dataset.src = fallback;
      img.classList.add('lazy-image');
    }

    picture.appendChild(img);

    // Track metrics
    this.metrics.totalImages++;

    return picture;
  }

  // Load image with performance tracking
  private async loadImage(img: HTMLImageElement): Promise<void> {
    if (this.loadingImages.has(img.src)) return;
    
    const startTime = performance.now();
    this.loadingImages.add(img.src);

    try {
      if (img.dataset.src) {
        img.src = img.dataset.src;
        delete img.dataset.src;
      }

      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
      });

      const loadTime = performance.now() - startTime;
      this.metrics.loadingTimes.push(loadTime);
      this.metrics.optimizedImages++;

      console.log(`🖼️ Image loaded in ${loadTime.toFixed(2)}ms:`, img.src);

    } catch (error) {
      console.error('🖼️ Image loading failed:', error);
    } finally {
      this.loadingImages.delete(img.src);
    }
  }

  // Enable lazy loading for an image
  public enableLazyLoading(img: HTMLImageElement): void {
    if (this.observer && img.classList.contains('lazy-image')) {
      this.observer.observe(img);
    }
  }

  // Preload critical images
  public preloadCriticalImages(urls: string[]): void {
    urls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = url;
      
      // Use modern formats if supported
      if (this.formatSupport.avif && !url.includes('.svg')) {
        link.href = this.optimizeImageUrl(url, 1200, 'avif', 85);
      } else if (this.formatSupport.webp && !url.includes('.svg')) {
        link.href = this.optimizeImageUrl(url, 1200, 'webp', 85);
      }
      
      document.head.appendChild(link);
    });

    console.log(`🖼️ Preloaded ${urls.length} critical images`);
  }

  // Convert image to modern format
  public async convertToModernFormat(
    imageData: ImageData | HTMLImageElement | HTMLCanvasElement,
    format: 'webp' | 'avif' = 'webp',
    quality: number = 0.85
  ): Promise<Blob | null> {
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) return null;

      if (imageData instanceof ImageData) {
        canvas.width = imageData.width;
        canvas.height = imageData.height;
        ctx.putImageData(imageData, 0, 0);
      } else if (imageData instanceof HTMLImageElement) {
        canvas.width = imageData.naturalWidth;
        canvas.height = imageData.naturalHeight;
        ctx.drawImage(imageData, 0, 0);
      } else if (imageData instanceof HTMLCanvasElement) {
        canvas.width = imageData.width;
        canvas.height = imageData.height;
        ctx.drawImage(imageData, 0, 0);
      }

      return new Promise((resolve) => {
        canvas.toBlob(resolve, `image/${format}`, quality);
      });

    } catch (error) {
      console.error('🖼️ Image conversion failed:', error);
      return null;
    }
  }

  // Get optimization metrics
  public getMetrics(): ImageOptimizationMetrics {
    const avgLoadTime = this.metrics.loadingTimes.length > 0
      ? this.metrics.loadingTimes.reduce((a, b) => a + b, 0) / this.metrics.loadingTimes.length
      : 0;

    return {
      ...this.metrics,
      avgLoadTime
    } as ImageOptimizationMetrics & { avgLoadTime: number };
  }

  // Reset metrics
  public resetMetrics(): void {
    this.metrics = this.initializeMetrics();
    this.metrics.formatSupport = this.formatSupport;
  }

  // Get format support
  public getFormatSupport(): { webp: boolean; avif: boolean } {
    return { ...this.formatSupport };
  }

  // Cleanup
  public cleanup(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    this.loadingImages.clear();
  }
}

// Export singleton instance
export const imageOptimizationService = ImageOptimizationService.getInstance();
