import emailjs from '@emailjs/browser';
import { CheckCircle, Clock, Mail, MapPin, MessageSquare, Phone, Send, User } from 'lucide-react';
import React, { useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { useLanguage } from '../contexts/LanguageContext';
import { ErrorToast, SuccessToast } from './CustomToast';
import { ImprovedScrollAnimation, ImprovedStaggerAnimation } from './ImprovedScrollAnimations';

const Contact = () => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    budget: '',
    timeline: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const sectionRef = useRef<HTMLElement>(null);
  const formRef = useRef<HTMLFormElement>(null);

  // EmailJS Configuration
  const EMAILJS_SERVICE_ID = 'service_cs4dp9v';
  const EMAILJS_TEMPLATE_ID = 'template_bjih7d3';
  const EMAILJS_PUBLIC_KEY = 'N0tsQS6nGR8545j5q';

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.name.trim() || !formData.email.trim() || !formData.subject.trim() || !formData.message.trim()) {
      toast(() => (
        <ErrorToast message="Please fill in all required fields." />
      ));
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast(() => (
        <ErrorToast message="Please enter a valid email address." />
      ));
      return;
    }

    setIsSubmitting(true);

    try {
      // Initialize EmailJS
      emailjs.init(EMAILJS_PUBLIC_KEY);

      // Prepare template parameters
      const templateParams = {
        from_name: formData.name,
        from_email: formData.email,
        subject: formData.subject,
        message: formData.message,
        budget: formData.budget,
        timeline: formData.timeline,
        to_name: 'Nural Bhardwaj',
        reply_to: formData.email,
      };

      // Send email using EmailJS
      const result = await emailjs.send(
        EMAILJS_SERVICE_ID,
        EMAILJS_TEMPLATE_ID,
        templateParams
      );

      console.log('Email sent successfully:', result);

      // Show success toast
      toast(() => (
        <SuccessToast
          message={`Thanks ${formData.name}! Your message has been sent successfully. I'll get back to you within 24 hours.`}
          icon={<CheckCircle className="w-5 h-5 text-green-400" />}
        />
      ));

      // Reset form and show success state
      setSubmitted(true);
      setFormData({ name: '', email: '', subject: '', message: '', budget: '', timeline: '' });

      // Reset success message after 5 seconds
      setTimeout(() => setSubmitted(false), 5000);

    } catch (error) {
      console.error('EmailJS Error:', error);

      // Show error toast
      toast(() => (
        <ErrorToast message="Failed to send message. Please try again or contact me directly." />
      ));
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfo = [
    {
      icon: Mail,
      title: t('contact.info.email.title'),
      value: t('contact.info.email.value'),
      href: 'mailto:<EMAIL>',
      description: t('contact.info.email.desc'),
      color: 'from-blue-500 to-cyan-500',
      stats: t('contact.info.email.stats')
    },
    {
      icon: Phone,
      title: t('contact.info.phone.title'),
      value: t('contact.info.phone.value'),
      href: 'tel:+917404814726',
      description: t('contact.info.phone.desc'),
      color: 'from-green-500 to-emerald-500',
      stats: t('contact.info.phone.stats')
    },
    {
      icon: MapPin,
      title: t('contact.info.location.title'),
      value: t('contact.info.location.value'),
      href: '#',
      description: t('contact.info.location.desc'),
      color: 'from-purple-500 to-pink-500',
      stats: t('contact.info.location.stats')
    }
  ];

  const budgetRanges = [
    'Under $5,000',
    '$5,000 - $15,000',
    '$15,000 - $50,000',
    '$50,000 - $100,000',
    '$100,000+'
  ];

  const timelineOptions = [
    'ASAP',
    '1-2 weeks',
    '1 month',
    '2-3 months',
    '3+ months'
  ];

  return (
    <section id="contact" ref={sectionRef} className="py-32 relative overflow-hidden">
      {/* Ultra Advanced Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
      
      {/* Animated Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-20 left-20 w-80 h-80 bg-gradient-to-r from-purple-600/30 to-pink-600/30 rounded-full blur-3xl animate-float-slow"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-r from-cyan-600/30 to-blue-600/30 rounded-full blur-3xl animate-float-reverse"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-green-600/20 to-emerald-600/20 rounded-full blur-3xl animate-pulse-slow"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header with Improved Scroll Animation */}
        <ImprovedScrollAnimation animation="fadeInUp" delay={0.2} className="text-center mb-20">
          <ImprovedScrollAnimation animation="scaleIn" delay={0.4}>
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 backdrop-blur-sm border border-purple-500/30 rounded-full px-6 py-3 mb-8">
              <MessageSquare className="w-5 h-5 text-purple-400" />
              <span className="text-purple-300 font-medium">{t('contact.subtitle')}</span>
            </div>
          </ImprovedScrollAnimation>

          <ImprovedScrollAnimation animation="scaleIn" delay={0.6}>
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 leading-tight">
              <span className="relative inline-block">
                <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-cyan-400 bg-clip-text text-transparent">
                  {t('contact.title')}
                </span>
                <div className="absolute -inset-2 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 blur-2xl animate-pulse"></div>
              </span>
            </h2>
          </ImprovedScrollAnimation>

          <ImprovedScrollAnimation animation="fadeInLeft" delay={0.8}>
            <div className="w-32 h-1 bg-gradient-to-r from-purple-500 via-pink-500 to-cyan-500 mx-auto rounded-full mb-8"></div>
          </ImprovedScrollAnimation>

          <ImprovedScrollAnimation animation="fadeInRight" delay={1.0}>
            <p className="text-lg sm:text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
              {t('contact.description')}
            </p>
          </ImprovedScrollAnimation>
        </ImprovedScrollAnimation>

        <div className="grid lg:grid-cols-2 gap-12 sm:gap-16">
          {/* Contact Info & CTA with Improved Scroll Animation */}
          <ImprovedScrollAnimation animation="fadeInLeft" delay={0.4} className="space-y-12">
            {/* Intro */}
            <ImprovedScrollAnimation animation="fadeInUp" delay={0.6} className="space-y-6">
              <h3 className="text-2xl sm:text-3xl font-bold text-white">
                <span className="bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
                  {t('contact.intro.title')}
                </span>
              </h3>
              <p className="text-gray-400 text-base sm:text-lg leading-relaxed">
                {t('contact.intro.description')}
              </p>

              {/* Key Points */}
              <ImprovedStaggerAnimation className="space-y-4">
                {[
                  t('contact.benefits.consultation'),
                  t('contact.benefits.pricing'),
                  t('contact.benefits.updates'),
                  t('contact.benefits.support')
                ].map((point) => (
                  <div key={point} className="flex items-center text-spacing-fix space-x-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-cyan-400 rounded-full"></div>
                    <span className="text-gray-300 text-sm sm:text-base text-spacing-fix">{point}</span>
                  </div>
                ))}
              </ImprovedStaggerAnimation>
            </ImprovedScrollAnimation>

            {/* Contact Methods */}
            <ImprovedStaggerAnimation className="space-y-6">
              {contactInfo.map((info) => (
                <div
                  key={info.title}
                  className="group flex items-center space-x-4 sm:space-x-6 p-4 sm:p-6 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm rounded-3xl border border-white/10 hover:border-purple-500/30 transition-all duration-500 transform hover:scale-105"
                >
                  <a href={info.href} className="flex items-center space-x-4 sm:space-x-6 w-full">
                    <div className={`relative w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r ${info.color} rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                      <info.icon className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                      <div className={`absolute -inset-2 bg-gradient-to-r ${info.color} opacity-0 group-hover:opacity-30 blur-xl transition-opacity duration-300 rounded-2xl`}></div>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-lg sm:text-xl font-bold text-white group-hover:text-purple-300 transition-colors duration-300">
                        {info.title}
                      </h4>
                      <p className="text-purple-400 font-semibold text-sm sm:text-base">{info.value}</p>
                      <p className="text-gray-400 text-xs sm:text-sm">{info.description}</p>
                    </div>
                  </a>
                </div>
              ))}
            </ImprovedStaggerAnimation>

            {/* Availability Status */}
            <ImprovedScrollAnimation animation="scaleIn" delay={0.8}>
              <div className="p-4 sm:p-6 bg-gradient-to-r from-green-500/10 to-emerald-500/10 backdrop-blur-sm rounded-3xl border border-green-500/30">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 font-bold text-sm sm:text-base">{t('contact.availability.title')}</span>
                </div>
                <p className="text-gray-300 text-xs sm:text-sm">
                  {t('contact.availability.desc')}
                </p>
              </div>
            </ImprovedScrollAnimation>
          </ImprovedScrollAnimation>

          {/* Enhanced Contact Form with Improved Scroll Animation */}
          <ImprovedScrollAnimation animation="fadeInRight" delay={0.4} className="relative">
            <div className="bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-xl rounded-3xl p-6 sm:p-8 border border-white/10 shadow-2xl shadow-purple-500/10">
              {submitted ? (
                <div className="text-center py-12 sm:py-16">
                  <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <CheckCircle className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
                  </div>
                  <h3 className="text-2xl sm:text-3xl font-bold text-white mb-4">{t('contact.success.title')}</h3>
                  <p className="text-gray-400 text-base sm:text-lg">
                    {t('contact.success.desc')}
                  </p>
                </div>
              ) : (
                <form ref={formRef} onSubmit={handleSubmit} className="space-y-6 sm:space-y-8">
                  <div className="text-center mb-6 sm:mb-8">
                    <h3 className="text-xl sm:text-2xl font-bold text-white mb-2">{t('contact.form.title')}</h3>
                    <p className="text-gray-400 text-sm sm:text-base">{t('contact.form.subtitle')}</p>
                  </div>

                  {/* Name & Email Row */}
                  <div className="grid sm:grid-cols-2 gap-4 sm:gap-6">
                    <div className="relative">
                      <label htmlFor="name" className="flex items-center text-white font-medium mb-2 sm:mb-3 text-sm sm:text-base text-spacing-fix space-x-2">
                        <User className="w-4 h-4 text-purple-400" />
                        <span className="text-spacing-fix">{t('contact.form.name')}</span>
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        onFocus={() => setFocusedField('name')}
                        onBlur={() => setFocusedField(null)}
                        required
                        className={`w-full px-4 sm:px-6 py-3 sm:py-4 bg-white/5 border-2 rounded-2xl text-white placeholder-gray-400 focus:outline-none transition-all duration-300 text-sm sm:text-base ${
                          focusedField === 'name'
                            ? 'border-purple-500 bg-white/10 shadow-lg shadow-purple-500/20'
                            : 'border-white/20 hover:border-white/30'
                        }`}
                        placeholder={t('contact.form.placeholder.name')}
                      />
                    </div>
                    <div className="relative">
                      <label htmlFor="email" className="flex items-center text-white font-medium mb-2 sm:mb-3 text-sm sm:text-base text-spacing-fix space-x-2">
                        <Mail className="w-4 h-4 text-purple-400" />
                        <span className="text-spacing-fix">{t('contact.form.email')}</span>
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        onFocus={() => setFocusedField('email')}
                        onBlur={() => setFocusedField(null)}
                        required
                        className={`w-full px-4 sm:px-6 py-3 sm:py-4 bg-white/5 border-2 rounded-2xl text-white placeholder-gray-400 focus:outline-none transition-all duration-300 text-sm sm:text-base ${
                          focusedField === 'email'
                            ? 'border-purple-500 bg-white/10'
                            : 'border-white/20 hover:border-white/30'
                        }`}
                        placeholder={t('contact.form.placeholder.email')}
                      />
                    </div>
                  </div>

                  {/* Subject */}
                  <div className="relative">
                    <label htmlFor="subject" className="flex items-center text-white font-medium mb-2 sm:mb-3 text-sm sm:text-base text-spacing-fix space-x-2">
                      <MessageSquare className="w-4 h-4 text-purple-400" />
                      <span className="text-spacing-fix">{t('contact.form.subject')}</span>
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      onFocus={() => setFocusedField('subject')}
                      onBlur={() => setFocusedField(null)}
                      required
                      className={`w-full px-4 sm:px-6 py-3 sm:py-4 bg-white/5 border-2 rounded-2xl text-white placeholder-gray-400 focus:outline-none transition-all duration-300 text-sm sm:text-base ${
                        focusedField === 'subject'
                          ? 'border-purple-500 bg-white/10'
                          : 'border-white/20 hover:border-white/30'
                      }`}
                      placeholder={t('contact.form.placeholder.subject')}
                    />
                  </div>

                  {/* Budget & Timeline Row */}
                  <div className="grid sm:grid-cols-2 gap-4 sm:gap-6">
                    <div className="relative">
                      <label htmlFor="budget" className="flex items-center text-white font-medium mb-2 sm:mb-3 text-sm sm:text-base text-spacing-fix space-x-2">
                        <span className="text-purple-400">$</span>
                        <span className="text-spacing-fix">{t('contact.form.budget')}</span>
                      </label>
                      <select
                        id="budget"
                        name="budget"
                        value={formData.budget}
                        onChange={handleChange}
                        onFocus={() => setFocusedField('budget')}
                        onBlur={() => setFocusedField(null)}
                        className={`w-full px-4 sm:px-6 py-3 sm:py-4 bg-white/5 border-2 rounded-2xl text-white focus:outline-none transition-all duration-300 text-sm sm:text-base ${
                          focusedField === 'budget'
                            ? 'border-purple-500 bg-white/10'
                            : 'border-white/20 hover:border-white/30'
                        }`}
                      >
                        <option value="" className="bg-gray-900">{t('contact.form.budget.select')}</option>
                        {budgetRanges.map((range) => (
                          <option key={range} value={range} className="bg-gray-900">{range}</option>
                        ))}
                      </select>
                    </div>
                    <div className="relative">
                      <label htmlFor="timeline" className="flex items-center text-white font-medium mb-2 sm:mb-3 text-sm sm:text-base text-spacing-fix space-x-2">
                        <Clock className="w-4 h-4 text-purple-400" />
                        <span className="text-spacing-fix">{t('contact.form.timeline')}</span>
                      </label>
                      <select
                        id="timeline"
                        name="timeline"
                        value={formData.timeline}
                        onChange={handleChange}
                        onFocus={() => setFocusedField('timeline')}
                        onBlur={() => setFocusedField(null)}
                        className={`w-full px-4 sm:px-6 py-3 sm:py-4 bg-white/5 border-2 rounded-2xl text-white focus:outline-none transition-all duration-300 text-sm sm:text-base ${
                          focusedField === 'timeline'
                            ? 'border-purple-500 bg-white/10'
                            : 'border-white/20 hover:border-white/30'
                        }`}
                      >
                        <option value="" className="bg-gray-900">{t('contact.form.timeline.select')}</option>
                        {timelineOptions.map((option) => (
                          <option key={option} value={option} className="bg-gray-900">{option}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Message */}
                  <div className="relative">
                    <label htmlFor="message" className="flex items-center text-white font-medium mb-2 sm:mb-3 text-sm sm:text-base text-spacing-fix space-x-2">
                      <MessageSquare className="w-4 h-4 text-purple-400" />
                      <span className="text-spacing-fix">{t('contact.form.message')}</span>
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      onFocus={() => setFocusedField('message')}
                      onBlur={() => setFocusedField(null)}
                      required
                      rows={5}
                      className={`w-full px-4 sm:px-6 py-3 sm:py-4 bg-white/5 border-2 rounded-2xl text-white placeholder-gray-400 focus:outline-none transition-all duration-300 resize-none text-sm sm:text-base ${
                        focusedField === 'message'
                          ? 'border-purple-500 bg-white/10'
                          : 'border-white/20 hover:border-white/30'
                      }`}
                      placeholder={t('contact.form.placeholder.message')}
                    ></textarea>
                  </div>

                  {/* Submit Button */}
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="group relative w-full px-6 sm:px-8 py-4 sm:py-6 bg-gradient-to-r from-purple-600 via-pink-600 to-cyan-600 rounded-2xl text-white font-bold text-base sm:text-lg overflow-hidden transform hover:scale-105 transition-all duration-500 disabled:opacity-50 disabled:cursor-not-allowed shadow-2xl shadow-purple-500/25"
                  >
                    <span className="relative z-10 flex items-center justify-center space-x-2 sm:space-x-3">
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 sm:w-6 sm:h-6 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                          <span>{t('contact.form.sending')}</span>
                        </>
                      ) : (
                        <>
                          <Send className="w-5 h-5 sm:w-6 sm:h-6" />
                          <span>{t('contact.form.send')}</span>
                        </>
                      )}
                    </span>
                    <div className="absolute inset-0 bg-gradient-to-r from-cyan-600 via-blue-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-cyan-600 blur-lg opacity-30 group-hover:opacity-60 transition-opacity duration-500"></div>
                  </button>
                </form>
              )}
            </div>
          </ImprovedScrollAnimation>
        </div>
      </div>
    </section>
  );
};

export default Contact;