import React, { useState } from 'react';
import { motion, useMotionValue, useTransform } from 'framer-motion';
import { buttonHover, easing } from '../utils/animations';
import { ButtonLoader } from './Loading';

interface AnimatedButtonProps {
  children: React.ReactNode;
  onClick?: () => void | Promise<void>;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  magnetic?: boolean;
  ripple?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}

const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  magnetic = false,
  ripple = true,
  className = '',
  type = 'button',
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([]);

  // Magnetic effect
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const rotateX = useTransform(y, [-100, 100], [30, -30]);
  const rotateY = useTransform(x, [-100, 100], [-30, 30]);

  const handleMouseMove = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (!magnetic) return;
    
    const rect = event.currentTarget.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    x.set((event.clientX - centerX) * 0.1);
    y.set((event.clientY - centerY) * 0.1);
  };

  const handleMouseLeave = () => {
    if (!magnetic) return;
    x.set(0);
    y.set(0);
  };

  const handleClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading || isLoading) return;

    // Ripple effect
    if (ripple) {
      const rect = event.currentTarget.getBoundingClientRect();
      const rippleX = event.clientX - rect.left;
      const rippleY = event.clientY - rect.top;
      const newRipple = { id: Date.now(), x: rippleX, y: rippleY };
      
      setRipples(prev => [...prev, newRipple]);
      setTimeout(() => {
        setRipples(prev => prev.filter(r => r.id !== newRipple.id));
      }, 600);
    }

    if (onClick) {
      try {
        setIsLoading(true);
        await onClick();
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Variant styles
  const variantStyles = {
    primary: 'bg-gradient-to-r from-purple-600 via-pink-600 to-cyan-600 text-white shadow-lg shadow-purple-500/25',
    secondary: 'bg-gradient-to-r from-gray-700 to-gray-800 text-white shadow-lg shadow-gray-500/25',
    outline: 'border-2 border-purple-500 text-purple-400 hover:bg-purple-500/10',
    ghost: 'text-purple-400 hover:bg-purple-500/10',
  };

  // Size styles
  const sizeStyles = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const baseClasses = `
    relative overflow-hidden rounded-2xl font-bold transition-all duration-300
    focus:outline-none focus:ring-4 focus:ring-purple-500/50
    disabled:opacity-50 disabled:cursor-not-allowed
    ${variantStyles[variant]}
    ${sizeStyles[size]}
    ${className}
  `;

  return (
    <motion.button
      type={type}
      className={baseClasses}
      variants={buttonHover}
      initial="rest"
      whileHover={!disabled && !loading && !isLoading ? "hover" : "rest"}
      whileTap={!disabled && !loading && !isLoading ? "tap" : "rest"}
      onClick={handleClick}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      disabled={disabled || loading || isLoading}
      style={magnetic ? { rotateX, rotateY, transformStyle: "preserve-3d" } : {}}
    >
      {/* Background glow effect */}
      <motion.div
        className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-cyan-600 opacity-0 blur-lg rounded-2xl"
        whileHover={{ opacity: 0.3 }}
        transition={{ duration: 0.3 }}
      />

      {/* Shine effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12"
        initial={{ x: '-100%' }}
        whileHover={{ x: '100%' }}
        transition={{ duration: 0.6, ease: easing.easeOut }}
      />

      {/* Ripple effects */}
      {ripples.map((ripple) => (
        <motion.span
          key={ripple.id}
          className="absolute bg-white/30 rounded-full pointer-events-none"
          style={{
            left: ripple.x - 10,
            top: ripple.y - 10,
            width: 20,
            height: 20,
          }}
          initial={{ scale: 0, opacity: 1 }}
          animate={{ scale: 4, opacity: 0 }}
          transition={{ duration: 0.6, ease: easing.easeOut }}
        />
      ))}

      {/* Button content */}
      <span className="relative z-10 flex items-center justify-center space-x-2">
        {(loading || isLoading) ? (
          <ButtonLoader />
        ) : (
          <>
            {icon && iconPosition === 'left' && (
              <motion.span
                className={iconSizes[size]}
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.3 }}
              >
                {icon}
              </motion.span>
            )}
            <span>{children}</span>
            {icon && iconPosition === 'right' && (
              <motion.span
                className={iconSizes[size]}
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.3 }}
              >
                {icon}
              </motion.span>
            )}
          </>
        )}
      </span>

      {/* Pulse effect for primary variant */}
      {variant === 'primary' && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-2xl opacity-0"
          animate={{
            scale: [1, 1.05, 1],
            opacity: [0, 0.1, 0],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: easing.easeInOut,
          }}
        />
      )}
    </motion.button>
  );
};

// Floating Action Button variant
export const FloatingActionButton: React.FC<{
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
}> = ({ children, onClick, className = '' }) => (
  <motion.button
    className={`
      fixed bottom-8 right-8 w-16 h-16 bg-gradient-to-r from-purple-600 to-cyan-600
      rounded-full shadow-2xl shadow-purple-500/25 flex items-center justify-center
      text-white z-40 ${className}
    `}
    whileHover={{ scale: 1.1, rotate: 90 }}
    whileTap={{ scale: 0.95 }}
    onClick={onClick}
    animate={{
      y: [0, -5, 0],
    }}
    transition={{
      y: {
        duration: 2,
        repeat: Infinity,
        ease: easing.easeInOut,
      },
    }}
  >
    {children}
  </motion.button>
);

export default AnimatedButton;
