// Advanced Code Splitting Service
// Implements intelligent lazy loading and dynamic imports

import React from 'react';

export interface ComponentLoadingMetrics {
  componentName: string;
  loadTime: number;
  chunkSize: number;
  cacheHit: boolean;
  timestamp: number;
}

export interface LazyLoadConfig {
  threshold: number;
  rootMargin: string;
  preloadDelay: number;
  retryAttempts: number;
  fallbackComponent?: React.ComponentType;
}

export interface ChunkMetrics {
  totalChunks: number;
  loadedChunks: number;
  failedChunks: number;
  totalSize: number;
  loadedSize: number;
  avgLoadTime: number;
  cacheHitRate: number;
}

class CodeSplittingService {
  private static instance: CodeSplittingService;
  private loadingMetrics: ComponentLoadingMetrics[] = [];
  private loadedComponents: Map<string, any> = new Map();
  private loadingPromises: Map<string, Promise<any>> = new Map();
  private preloadQueue: Set<string> = new Set();
  private observer: IntersectionObserver | null = null;
  private config: LazyLoadConfig;

  private defaultConfig: LazyLoadConfig = {
    threshold: 0.1,
    rootMargin: '100px',
    preloadDelay: 2000,
    retryAttempts: 3,
    fallbackComponent: undefined
  };

  private constructor() {
    this.config = this.loadConfig();
    this.initializeIntersectionObserver();
    this.setupPreloadScheduler();
  }

  public static getInstance(): CodeSplittingService {
    if (!CodeSplittingService.instance) {
      CodeSplittingService.instance = new CodeSplittingService();
    }
    return CodeSplittingService.instance;
  }

  // Initialize intersection observer for viewport-based loading
  private initializeIntersectionObserver(): void {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const element = entry.target as HTMLElement;
              const componentName = element.dataset.lazyComponent;
              
              if (componentName) {
                this.loadComponent(componentName);
                this.observer?.unobserve(element);
              }
            }
          });
        },
        {
          threshold: this.config.threshold,
          rootMargin: this.config.rootMargin
        }
      );
    }
  }

  // Setup preload scheduler for predictive loading
  private setupPreloadScheduler(): void {
    // Preload components after initial page load
    setTimeout(() => {
      this.preloadQueue.forEach(componentName => {
        this.preloadComponent(componentName);
      });
    }, this.config.preloadDelay);

    // Setup idle time preloading
    if ('requestIdleCallback' in window) {
      const preloadDuringIdle = () => {
        requestIdleCallback(() => {
          if (this.preloadQueue.size > 0) {
            const componentName = this.preloadQueue.values().next().value;
            this.preloadComponent(componentName);
            this.preloadQueue.delete(componentName);
            
            if (this.preloadQueue.size > 0) {
              preloadDuringIdle();
            }
          }
        });
      };
      
      preloadDuringIdle();
    }
  }

  // Create lazy-loaded component with advanced features
  public createLazyComponent<T = any>(
    importFunction: () => Promise<{ default: React.ComponentType<T> }>,
    componentName: string,
    options: {
      preload?: boolean;
      fallback?: React.ComponentType;
      errorBoundary?: boolean;
      retryAttempts?: number;
    } = {}
  ): React.ComponentType<T> {
    const {
      preload = false,
      fallback,
      errorBoundary = true,
      retryAttempts = this.config.retryAttempts
    } = options;

    // Add to preload queue if requested
    if (preload) {
      this.preloadQueue.add(componentName);
    }

    // Create the lazy component
    const LazyComponent = React.lazy(async () => {
      const startTime = performance.now();
      
      try {
        // Check if component is already loaded
        if (this.loadedComponents.has(componentName)) {
          const component = this.loadedComponents.get(componentName);
          this.recordMetrics(componentName, performance.now() - startTime, 0, true);
          return component;
        }

        // Check if component is currently loading
        if (this.loadingPromises.has(componentName)) {
          return await this.loadingPromises.get(componentName);
        }

        // Load the component
        const loadPromise = this.loadComponentWithRetry(
          importFunction,
          componentName,
          retryAttempts,
          startTime
        );
        
        this.loadingPromises.set(componentName, loadPromise);
        const result = await loadPromise;
        this.loadingPromises.delete(componentName);
        
        return result;

      } catch (error) {
        this.loadingPromises.delete(componentName);
        console.error(`Failed to load component ${componentName}:`, error);
        
        // Return fallback component if available
        if (fallback) {
          return { default: fallback };
        }
        
        throw error;
      }
    });

    // Wrap with error boundary if requested
    if (errorBoundary) {
      return this.wrapWithErrorBoundary(LazyComponent, componentName, fallback);
    }

    return LazyComponent;
  }

  // Load component with retry logic
  private async loadComponentWithRetry(
    importFunction: () => Promise<{ default: React.ComponentType<any> }>,
    componentName: string,
    retryAttempts: number,
    startTime: number
  ): Promise<{ default: React.ComponentType<any> }> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= retryAttempts; attempt++) {
      try {
        const module = await importFunction();
        
        // Estimate chunk size (rough approximation)
        const chunkSize = this.estimateChunkSize(module);
        
        // Cache the loaded component
        this.loadedComponents.set(componentName, module);
        
        // Record metrics
        this.recordMetrics(componentName, performance.now() - startTime, chunkSize, false);
        
        console.log(`✅ Component ${componentName} loaded successfully (attempt ${attempt + 1})`);
        return module;

      } catch (error) {
        lastError = error as Error;
        console.warn(`⚠️ Component ${componentName} load failed (attempt ${attempt + 1}):`, error);
        
        // Wait before retry (exponential backoff)
        if (attempt < retryAttempts) {
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    throw lastError || new Error(`Failed to load component ${componentName} after ${retryAttempts + 1} attempts`);
  }

  // Estimate chunk size (rough approximation)
  private estimateChunkSize(module: any): number {
    try {
      const moduleString = JSON.stringify(module);
      return new Blob([moduleString]).size;
    } catch {
      return 0;
    }
  }

  // Wrap component with error boundary
  private wrapWithErrorBoundary<T>(
    Component: React.ComponentType<T>,
    componentName: string,
    fallback?: React.ComponentType
  ): React.ComponentType<T> {
    return class ErrorBoundaryWrapper extends React.Component<T, { hasError: boolean }> {
      constructor(props: T) {
        super(props);
        this.state = { hasError: false };
      }

      static getDerivedStateFromError(): { hasError: boolean } {
        return { hasError: true };
      }

      componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
        console.error(`Error in lazy component ${componentName}:`, error, errorInfo);
      }

      render(): React.ReactNode {
        if (this.state.hasError) {
          if (fallback) {
            const FallbackComponent = fallback;
            return React.createElement(FallbackComponent, this.props);
          }
          
          return React.createElement('div', {
            className: 'error-boundary p-4 bg-red-100 border border-red-400 rounded',
            children: `Error loading ${componentName}`
          });
        }

        return React.createElement(Component, this.props);
      }
    };
  }

  // Preload component without rendering
  public async preloadComponent(componentName: string): Promise<void> {
    if (this.loadedComponents.has(componentName) || this.loadingPromises.has(componentName)) {
      return;
    }

    try {
      console.log(`🔄 Preloading component: ${componentName}`);
      // This would need to be implemented based on your component registry
      // For now, we'll just mark it as preloaded
      
    } catch (error) {
      console.warn(`Failed to preload component ${componentName}:`, error);
    }
  }

  // Load component immediately
  public async loadComponent(componentName: string): Promise<any> {
    if (this.loadedComponents.has(componentName)) {
      return this.loadedComponents.get(componentName);
    }

    if (this.loadingPromises.has(componentName)) {
      return await this.loadingPromises.get(componentName);
    }

    throw new Error(`Component ${componentName} not registered for loading`);
  }

  // Register element for lazy loading
  public observeElement(element: HTMLElement, componentName: string): void {
    if (this.observer) {
      element.dataset.lazyComponent = componentName;
      this.observer.observe(element);
    }
  }

  // Unobserve element
  public unobserveElement(element: HTMLElement): void {
    if (this.observer) {
      this.observer.unobserve(element);
    }
  }

  // Record loading metrics
  private recordMetrics(
    componentName: string,
    loadTime: number,
    chunkSize: number,
    cacheHit: boolean
  ): void {
    const metrics: ComponentLoadingMetrics = {
      componentName,
      loadTime,
      chunkSize,
      cacheHit,
      timestamp: Date.now()
    };

    this.loadingMetrics.push(metrics);

    // Keep only last 100 metrics
    if (this.loadingMetrics.length > 100) {
      this.loadingMetrics = this.loadingMetrics.slice(-100);
    }

    console.log(`📊 Component metrics:`, metrics);
  }

  // Get chunk metrics
  public getChunkMetrics(): ChunkMetrics {
    const totalChunks = this.loadingMetrics.length;
    const loadedChunks = this.loadingMetrics.filter(m => m.loadTime > 0).length;
    const failedChunks = totalChunks - loadedChunks;
    const totalSize = this.loadingMetrics.reduce((sum, m) => sum + m.chunkSize, 0);
    const loadedSize = this.loadingMetrics
      .filter(m => m.loadTime > 0)
      .reduce((sum, m) => sum + m.chunkSize, 0);
    const avgLoadTime = loadedChunks > 0
      ? this.loadingMetrics
          .filter(m => m.loadTime > 0)
          .reduce((sum, m) => sum + m.loadTime, 0) / loadedChunks
      : 0;
    const cacheHits = this.loadingMetrics.filter(m => m.cacheHit).length;
    const cacheHitRate = totalChunks > 0 ? (cacheHits / totalChunks) * 100 : 0;

    return {
      totalChunks,
      loadedChunks,
      failedChunks,
      totalSize,
      loadedSize,
      avgLoadTime,
      cacheHitRate
    };
  }

  // Get component loading metrics
  public getComponentMetrics(): ComponentLoadingMetrics[] {
    return [...this.loadingMetrics];
  }

  // Update configuration
  public updateConfig(newConfig: Partial<LazyLoadConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
    
    // Reinitialize observer with new config
    if (this.observer) {
      this.observer.disconnect();
      this.initializeIntersectionObserver();
    }
  }

  // Get current configuration
  public getConfig(): LazyLoadConfig {
    return { ...this.config };
  }

  // Save configuration
  private saveConfig(): void {
    try {
      localStorage.setItem('code_splitting_config', JSON.stringify(this.config));
    } catch (error) {
      console.error('Failed to save code splitting config:', error);
    }
  }

  // Load configuration
  private loadConfig(): LazyLoadConfig {
    try {
      const saved = localStorage.getItem('code_splitting_config');
      return saved ? { ...this.defaultConfig, ...JSON.parse(saved) } : this.defaultConfig;
    } catch (error) {
      console.error('Failed to load code splitting config:', error);
      return this.defaultConfig;
    }
  }

  // Clear all caches
  public clearCache(): void {
    this.loadedComponents.clear();
    this.loadingPromises.clear();
    this.loadingMetrics.length = 0;
    console.log('🗑️ Code splitting cache cleared');
  }

  // Cleanup
  public cleanup(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    this.clearCache();
    this.preloadQueue.clear();
  }
}

// Export singleton instance
export const codeSplittingService = CodeSplittingService.getInstance();
