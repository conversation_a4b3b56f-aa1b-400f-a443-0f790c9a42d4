import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Mail, 
  Save, 
  Eye, 
  Settings, 
  MessageSquare,
  Phone,
  MapPin,
  Globe,
  Key,
  TestTube,
  CheckCircle,
  XCircle,
  RefreshCw,
  X
} from 'lucide-react';
import { cmsService } from '../../services/cmsService';
import toast from 'react-hot-toast';
import { SuccessToast, ErrorToast } from '../CustomToast';
import emailjs from '@emailjs/browser';

interface ContactManagerProps {
  onDataChange: () => void;
}

interface ContactSettings {
  emailjs: {
    publicKey: string;
    serviceId: string;
    templateId: string;
  };
  contactInfo: {
    email: string;
    phone: string;
    location: string;
    website: string;
  };
  formSettings: {
    enableNotifications: boolean;
    autoReply: boolean;
    requiredFields: string[];
    maxMessageLength: number;
  };
  customMessages: {
    successMessage: string;
    errorMessage: string;
    autoReplySubject: string;
    autoReplyMessage: string;
  };
}

const ContactManager: React.FC<ContactManagerProps> = ({ onDataChange }) => {
  const [contactSettings, setContactSettings] = useState<ContactSettings>({
    emailjs: {
      publicKey: 'N0tsQS6nGR8545j5q',
      serviceId: 'service_cs4dp9v',
      templateId: 'template_bjih7d3'
    },
    contactInfo: {
      email: '<EMAIL>',
      phone: '+91 7404814726',
      location: 'Gurugram, India',
      website: 'https://nuralbhardwaj.me'
    },
    formSettings: {
      enableNotifications: true,
      autoReply: true,
      requiredFields: ['name', 'email', 'message'],
      maxMessageLength: 1000
    },
    customMessages: {
      successMessage: 'Thank you for your message! I\'ll get back to you soon.',
      errorMessage: 'Sorry, there was an error sending your message. Please try again.',
      autoReplySubject: 'Thank you for contacting me!',
      autoReplyMessage: 'Hi {name},\n\nThank you for reaching out! I\'ve received your message and will get back to you within 24 hours.\n\nBest regards,\nNural Bhardwaj'
    }
  });

  const [testEmail, setTestEmail] = useState({
    name: 'Test User',
    email: '<EMAIL>',
    subject: 'Test Message',
    message: 'This is a test message from the admin panel.'
  });

  const [isTestingEmail, setIsTestingEmail] = useState(false);
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null);

  useEffect(() => {
    loadContactSettings();
  }, []);

  const loadContactSettings = () => {
    const saved = localStorage.getItem('contact_settings');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setContactSettings({ ...contactSettings, ...parsed });
      } catch (error) {
        console.error('Error loading contact settings:', error);
      }
    }
  };

  const saveContactSettings = () => {
    try {
      localStorage.setItem('contact_settings', JSON.stringify(contactSettings));
      onDataChange();
      
      toast(() => (
        <SuccessToast
          message="Contact settings saved successfully!"
          icon={<Save className="w-5 h-5 text-green-400" />}
        />
      ));
    } catch (error) {
      toast(() => (
        <ErrorToast
          message="Failed to save contact settings"
          icon={<X className="w-5 h-5 text-red-400" />}
        />
      ));
    }
  };

  const testEmailConfiguration = async () => {
    setIsTestingEmail(true);
    setTestResult(null);

    try {
      // Initialize EmailJS with the current settings
      emailjs.init(contactSettings.emailjs.publicKey);

      const templateParams = {
        from_name: testEmail.name,
        from_email: testEmail.email,
        subject: testEmail.subject,
        message: testEmail.message,
        to_email: contactSettings.contactInfo.email
      };

      await emailjs.send(
        contactSettings.emailjs.serviceId,
        contactSettings.emailjs.templateId,
        templateParams
      );

      setTestResult('success');
      toast(() => (
        <SuccessToast
          message="Test email sent successfully!"
          icon={<CheckCircle className="w-5 h-5 text-green-400" />}
        />
      ));
    } catch (error) {
      console.error('Email test failed:', error);
      setTestResult('error');
      toast(() => (
        <ErrorToast
          message="Test email failed. Please check your EmailJS configuration."
          icon={<XCircle className="w-5 h-5 text-red-400" />}
        />
      ));
    } finally {
      setIsTestingEmail(false);
    }
  };

  const resetToDefaults = () => {
    if (window.confirm('Are you sure you want to reset contact settings to defaults?')) {
      localStorage.removeItem('contact_settings');
      loadContactSettings();
      toast(() => (
        <SuccessToast
          message="Contact settings reset to defaults!"
          icon={<RefreshCw className="w-5 h-5 text-green-400" />}
        />
      ));
    }
  };

  const toggleRequiredField = (field: string) => {
    const updatedFields = contactSettings.formSettings.requiredFields.includes(field)
      ? contactSettings.formSettings.requiredFields.filter(f => f !== field)
      : [...contactSettings.formSettings.requiredFields, field];

    setContactSettings({
      ...contactSettings,
      formSettings: {
        ...contactSettings.formSettings,
        requiredFields: updatedFields
      }
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Contact Form Management</h1>
          <p className="text-gray-400">Configure contact form settings and EmailJS integration</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={resetToDefaults}
            className="flex items-center space-x-2 px-4 py-2 bg-orange-600/20 text-orange-400 rounded-xl hover:bg-orange-600/30 transition-all duration-200"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Reset</span>
          </button>
          <button
            onClick={saveContactSettings}
            className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-xl transition-all duration-200"
          >
            <Save className="w-4 h-4" />
            <span>Save Settings</span>
          </button>
        </div>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* EmailJS Configuration */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
        >
          <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
            <Key className="w-5 h-5 text-blue-400" />
            <span>EmailJS Configuration</span>
          </h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Public Key</label>
              <input
                type="text"
                value={contactSettings.emailjs.publicKey}
                onChange={(e) => setContactSettings({
                  ...contactSettings,
                  emailjs: { ...contactSettings.emailjs, publicKey: e.target.value }
                })}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                placeholder="Your EmailJS public key"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Service ID</label>
              <input
                type="text"
                value={contactSettings.emailjs.serviceId}
                onChange={(e) => setContactSettings({
                  ...contactSettings,
                  emailjs: { ...contactSettings.emailjs, serviceId: e.target.value }
                })}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                placeholder="service_xxxxxxx"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Template ID</label>
              <input
                type="text"
                value={contactSettings.emailjs.templateId}
                onChange={(e) => setContactSettings({
                  ...contactSettings,
                  emailjs: { ...contactSettings.emailjs, templateId: e.target.value }
                })}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                placeholder="template_xxxxxxx"
              />
            </div>

            {/* Test Email Configuration */}
            <div className="mt-6 p-4 bg-blue-600/10 rounded-xl border border-blue-500/20">
              <h3 className="text-white font-semibold mb-3 flex items-center space-x-2">
                <TestTube className="w-4 h-4" />
                <span>Test Email Configuration</span>
              </h3>
              
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <input
                    type="text"
                    value={testEmail.name}
                    onChange={(e) => setTestEmail({ ...testEmail, name: e.target.value })}
                    className="px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white text-sm"
                    placeholder="Test Name"
                  />
                  <input
                    type="email"
                    value={testEmail.email}
                    onChange={(e) => setTestEmail({ ...testEmail, email: e.target.value })}
                    className="px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white text-sm"
                    placeholder="<EMAIL>"
                  />
                </div>
                <input
                  type="text"
                  value={testEmail.subject}
                  onChange={(e) => setTestEmail({ ...testEmail, subject: e.target.value })}
                  className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white text-sm"
                  placeholder="Test Subject"
                />
                <textarea
                  value={testEmail.message}
                  onChange={(e) => setTestEmail({ ...testEmail, message: e.target.value })}
                  className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white text-sm"
                  placeholder="Test message content"
                  rows={3}
                />
                
                <button
                  onClick={testEmailConfiguration}
                  disabled={isTestingEmail}
                  className={`w-full px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2 ${
                    isTestingEmail
                      ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                      : testResult === 'success'
                      ? 'bg-green-600 text-white'
                      : testResult === 'error'
                      ? 'bg-red-600 text-white'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {isTestingEmail ? (
                    <>
                      <RefreshCw className="w-4 h-4 animate-spin" />
                      <span>Testing...</span>
                    </>
                  ) : testResult === 'success' ? (
                    <>
                      <CheckCircle className="w-4 h-4" />
                      <span>Test Successful!</span>
                    </>
                  ) : testResult === 'error' ? (
                    <>
                      <XCircle className="w-4 h-4" />
                      <span>Test Failed</span>
                    </>
                  ) : (
                    <>
                      <TestTube className="w-4 h-4" />
                      <span>Send Test Email</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Contact Information & Form Settings */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          {/* Contact Information */}
          <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
            <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
              <MessageSquare className="w-5 h-5 text-green-400" />
              <span>Contact Information</span>
            </h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                <input
                  type="email"
                  value={contactSettings.contactInfo.email}
                  onChange={(e) => setContactSettings({
                    ...contactSettings,
                    contactInfo: { ...contactSettings.contactInfo, email: e.target.value }
                  })}
                  className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Phone Number</label>
                <input
                  type="tel"
                  value={contactSettings.contactInfo.phone}
                  onChange={(e) => setContactSettings({
                    ...contactSettings,
                    contactInfo: { ...contactSettings.contactInfo, phone: e.target.value }
                  })}
                  className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                  placeholder="+****************"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Location</label>
                <input
                  type="text"
                  value={contactSettings.contactInfo.location}
                  onChange={(e) => setContactSettings({
                    ...contactSettings,
                    contactInfo: { ...contactSettings.contactInfo, location: e.target.value }
                  })}
                  className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                  placeholder="City, Country"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Website</label>
                <input
                  type="url"
                  value={contactSettings.contactInfo.website}
                  onChange={(e) => setContactSettings({
                    ...contactSettings,
                    contactInfo: { ...contactSettings.contactInfo, website: e.target.value }
                  })}
                  className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                  placeholder="https://yourwebsite.com"
                />
              </div>
            </div>
          </div>

          {/* Form Settings */}
          <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
            <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
              <Settings className="w-5 h-5 text-purple-400" />
              <span>Form Settings</span>
            </h2>

            <div className="space-y-4">
              {/* Required Fields */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">Required Fields</label>
                <div className="space-y-2">
                  {['name', 'email', 'subject', 'message'].map((field) => (
                    <div key={field} className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id={field}
                        checked={contactSettings.formSettings.requiredFields.includes(field)}
                        onChange={() => toggleRequiredField(field)}
                        className="w-4 h-4 text-purple-600 bg-white/5 border-white/10 rounded focus:ring-purple-500/20 focus:ring-2"
                      />
                      <label htmlFor={field} className="text-gray-300 capitalize">
                        {field}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Max Message Length */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Max Message Length: {contactSettings.formSettings.maxMessageLength} characters
                </label>
                <input
                  type="range"
                  min="100"
                  max="5000"
                  step="100"
                  value={contactSettings.formSettings.maxMessageLength}
                  onChange={(e) => setContactSettings({
                    ...contactSettings,
                    formSettings: {
                      ...contactSettings.formSettings,
                      maxMessageLength: parseInt(e.target.value)
                    }
                  })}
                  className="w-full"
                />
              </div>

              {/* Toggle Options */}
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="enableNotifications"
                    checked={contactSettings.formSettings.enableNotifications}
                    onChange={(e) => setContactSettings({
                      ...contactSettings,
                      formSettings: {
                        ...contactSettings.formSettings,
                        enableNotifications: e.target.checked
                      }
                    })}
                    className="w-4 h-4 text-purple-600 bg-white/5 border-white/10 rounded focus:ring-purple-500/20 focus:ring-2"
                  />
                  <label htmlFor="enableNotifications" className="text-gray-300">
                    Enable Email Notifications
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="autoReply"
                    checked={contactSettings.formSettings.autoReply}
                    onChange={(e) => setContactSettings({
                      ...contactSettings,
                      formSettings: {
                        ...contactSettings.formSettings,
                        autoReply: e.target.checked
                      }
                    })}
                    className="w-4 h-4 text-purple-600 bg-white/5 border-white/10 rounded focus:ring-purple-500/20 focus:ring-2"
                  />
                  <label htmlFor="autoReply" className="text-gray-300">
                    Send Auto-Reply to Visitors
                  </label>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Custom Messages */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
      >
        <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
          <MessageSquare className="w-5 h-5 text-cyan-400" />
          <span>Custom Messages</span>
        </h2>

        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Success Message</label>
            <textarea
              value={contactSettings.customMessages.successMessage}
              onChange={(e) => setContactSettings({
                ...contactSettings,
                customMessages: {
                  ...contactSettings.customMessages,
                  successMessage: e.target.value
                }
              })}
              className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
              rows={3}
              placeholder="Message shown when form is submitted successfully"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Error Message</label>
            <textarea
              value={contactSettings.customMessages.errorMessage}
              onChange={(e) => setContactSettings({
                ...contactSettings,
                customMessages: {
                  ...contactSettings.customMessages,
                  errorMessage: e.target.value
                }
              })}
              className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
              rows={3}
              placeholder="Message shown when form submission fails"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Auto-Reply Subject</label>
            <input
              type="text"
              value={contactSettings.customMessages.autoReplySubject}
              onChange={(e) => setContactSettings({
                ...contactSettings,
                customMessages: {
                  ...contactSettings.customMessages,
                  autoReplySubject: e.target.value
                }
              })}
              className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
              placeholder="Subject for auto-reply emails"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Auto-Reply Message</label>
            <textarea
              value={contactSettings.customMessages.autoReplyMessage}
              onChange={(e) => setContactSettings({
                ...contactSettings,
                customMessages: {
                  ...contactSettings.customMessages,
                  autoReplyMessage: e.target.value
                }
              })}
              className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
              rows={4}
              placeholder="Auto-reply message content (use {name} for personalization)"
            />
            <p className="text-xs text-gray-500 mt-1">
              Use {'{name}'} to personalize the message with the sender's name
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ContactManager;
