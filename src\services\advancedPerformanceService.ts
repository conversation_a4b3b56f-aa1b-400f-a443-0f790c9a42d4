// Advanced Performance Optimization Service
// Implements comprehensive performance monitoring and optimization

export interface PerformanceMetrics {
  // Core Web Vitals
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  fcp: number; // First Contentful Paint
  ttfb: number; // Time to First Byte
  
  // Additional metrics
  domContentLoaded: number;
  loadComplete: number;
  firstPaint: number;
  
  // Resource metrics
  totalResources: number;
  totalSize: number;
  compressedSize: number;
  
  // Runtime metrics
  memoryUsage: number;
  fps: number;
  
  // Network metrics
  connectionType: string;
  effectiveType: string;
  downlink: number;
}

export interface OptimizationConfig {
  enableImageOptimization: boolean;
  enableCodeSplitting: boolean;
  enableResourceHints: boolean;
  enableServiceWorker: boolean;
  enableCriticalCSS: boolean;
  enableLazyLoading: boolean;
  compressionLevel: number;
  maxImageSize: number;
  chunkSizeLimit: number;
}

class AdvancedPerformanceService {
  private static instance: AdvancedPerformanceService;
  private metrics: PerformanceMetrics;
  private config: OptimizationConfig;
  private observer: PerformanceObserver | null = null;
  private isMonitoring = false;

  private defaultConfig: OptimizationConfig = {
    enableImageOptimization: true,
    enableCodeSplitting: true,
    enableResourceHints: true,
    enableServiceWorker: true,
    enableCriticalCSS: true,
    enableLazyLoading: true,
    compressionLevel: 6,
    maxImageSize: 1920,
    chunkSizeLimit: 244 * 1024 // 244KB
  };

  private constructor() {
    this.config = { ...this.defaultConfig };
    this.metrics = this.initializeMetrics();
    this.startMonitoring();
  }

  public static getInstance(): AdvancedPerformanceService {
    if (!AdvancedPerformanceService.instance) {
      AdvancedPerformanceService.instance = new AdvancedPerformanceService();
    }
    return AdvancedPerformanceService.instance;
  }

  // Initialize metrics with default values
  private initializeMetrics(): PerformanceMetrics {
    return {
      lcp: 0,
      fid: 0,
      cls: 0,
      fcp: 0,
      ttfb: 0,
      domContentLoaded: 0,
      loadComplete: 0,
      firstPaint: 0,
      totalResources: 0,
      totalSize: 0,
      compressedSize: 0,
      memoryUsage: 0,
      fps: 60,
      connectionType: 'unknown',
      effectiveType: 'unknown',
      downlink: 0
    };
  }

  // Start performance monitoring
  public startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.measureCoreWebVitals();
    this.measureResourceMetrics();
    this.measureNetworkInfo();
    this.measureMemoryUsage();
    this.measureFPS();
    this.setupPerformanceObserver();

    console.log('📊 Advanced performance monitoring started');
  }

  // Measure Core Web Vitals
  private measureCoreWebVitals(): void {
    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as any;
          this.metrics.lcp = lastEntry.startTime;
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      } catch (e) {
        console.warn('LCP measurement not supported');
      }

      // First Input Delay (FID)
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            this.metrics.fid = entry.processingStart - entry.startTime;
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
      } catch (e) {
        console.warn('FID measurement not supported');
      }

      // Cumulative Layout Shift (CLS)
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
              this.metrics.cls = clsValue;
            }
          });
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      } catch (e) {
        console.warn('CLS measurement not supported');
      }
    }

    // Navigation timing metrics
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          this.metrics.ttfb = navigation.responseStart - navigation.requestStart;
          this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.startTime;
          this.metrics.loadComplete = navigation.loadEventEnd - navigation.startTime;
        }

        // First Contentful Paint
        const paintEntries = performance.getEntriesByType('paint');
        const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
        const fpEntry = paintEntries.find(entry => entry.name === 'first-paint');
        
        if (fcpEntry) this.metrics.fcp = fcpEntry.startTime;
        if (fpEntry) this.metrics.firstPaint = fpEntry.startTime;
      }, 0);
    });
  }

  // Measure resource metrics
  private measureResourceMetrics(): void {
    window.addEventListener('load', () => {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      
      this.metrics.totalResources = resources.length;
      this.metrics.totalSize = resources.reduce((total, resource) => {
        return total + (resource.transferSize || 0);
      }, 0);
      this.metrics.compressedSize = resources.reduce((total, resource) => {
        return total + (resource.encodedBodySize || 0);
      }, 0);
    });
  }

  // Measure network information
  private measureNetworkInfo(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      this.metrics.connectionType = connection.type || 'unknown';
      this.metrics.effectiveType = connection.effectiveType || 'unknown';
      this.metrics.downlink = connection.downlink || 0;

      // Listen for network changes
      connection.addEventListener('change', () => {
        this.metrics.connectionType = connection.type || 'unknown';
        this.metrics.effectiveType = connection.effectiveType || 'unknown';
        this.metrics.downlink = connection.downlink || 0;
      });
    }
  }

  // Measure memory usage
  private measureMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.metrics.memoryUsage = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
    }

    // Update memory usage periodically
    setInterval(() => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        this.metrics.memoryUsage = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
      }
    }, 5000);
  }

  // Measure FPS
  private measureFPS(): void {
    let frameCount = 0;
    let lastTime = performance.now();

    const measureFrame = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        this.metrics.fps = frameCount;
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(measureFrame);
    };

    requestAnimationFrame(measureFrame);
  }

  // Setup performance observer for detailed monitoring
  private setupPerformanceObserver(): void {
    if (!('PerformanceObserver' in window)) return;

    try {
      this.observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          // Log slow resources
          if (entry.entryType === 'resource' && entry.duration > 1000) {
            console.warn(`Slow resource detected: ${entry.name} (${entry.duration.toFixed(2)}ms)`);
          }
          
          // Log long tasks
          if (entry.entryType === 'longtask') {
            console.warn(`Long task detected: ${entry.duration.toFixed(2)}ms`);
          }
        });
      });

      this.observer.observe({ entryTypes: ['resource', 'longtask'] });
    } catch (e) {
      console.warn('Performance observer setup failed:', e);
    }
  }

  // Get current performance metrics
  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // Get performance score (0-100)
  public getPerformanceScore(): number {
    let score = 100;

    // LCP scoring (25%)
    if (this.metrics.lcp > 4000) score -= 25;
    else if (this.metrics.lcp > 2500) score -= 15;
    else if (this.metrics.lcp > 1200) score -= 5;

    // FID scoring (25%)
    if (this.metrics.fid > 300) score -= 25;
    else if (this.metrics.fid > 100) score -= 15;
    else if (this.metrics.fid > 50) score -= 5;

    // CLS scoring (25%)
    if (this.metrics.cls > 0.25) score -= 25;
    else if (this.metrics.cls > 0.1) score -= 15;
    else if (this.metrics.cls > 0.05) score -= 5;

    // FCP scoring (25%)
    if (this.metrics.fcp > 3000) score -= 25;
    else if (this.metrics.fcp > 1800) score -= 15;
    else if (this.metrics.fcp > 1000) score -= 5;

    return Math.max(0, Math.round(score));
  }

  // Advanced image optimization with modern formats
  public optimizeImages(): void {
    if (!this.config.enableImageOptimization) return;

    const images = document.querySelectorAll('img');
    images.forEach((img) => {
      // Add loading="lazy" if not present
      if (!img.hasAttribute('loading') && this.config.enableLazyLoading) {
        img.setAttribute('loading', 'lazy');
      }

      // Add decoding="async" for better performance
      if (!img.hasAttribute('decoding')) {
        img.setAttribute('decoding', 'async');
      }

      // Add fetchpriority for important images
      if (img.closest('.hero, .about, .navbar')) {
        img.setAttribute('fetchpriority', 'high');
      }

      // Optimize image format based on browser support
      this.optimizeImageFormat(img);
    });

    // Preload critical images
    this.preloadCriticalImages();
  }

  // Optimize image format for modern browsers
  private optimizeImageFormat(img: HTMLImageElement): void {
    const src = img.src;
    if (!src || src.includes('.svg')) return;

    // Check for WebP support
    if (this.supportsWebP() && !src.includes('.webp')) {
      const webpSrc = src.replace(/\.(jpg|jpeg|png)$/i, '.webp');

      // Create a new image to test if WebP version exists
      const testImg = new Image();
      testImg.onload = () => {
        img.src = webpSrc;
      };
      testImg.onerror = () => {
        // WebP version doesn't exist, keep original
      };
      testImg.src = webpSrc;
    }
  }

  // Check WebP support
  private supportsWebP(): boolean {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }

  // Preload only essential images to avoid unused preload warnings
  private preloadCriticalImages(): void {
    // Only preload favicon which is always used
    const criticalImages = [
      '/favicon.svg'
    ];

    criticalImages.forEach(src => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      document.head.appendChild(link);
    });
  }

  // Preload critical resources
  public preloadCriticalResources(): void {
    if (!this.config.enableResourceHints) return;

    // Preload critical CSS
    const criticalCSS = document.querySelectorAll('link[rel="stylesheet"]');
    criticalCSS.forEach((link, index) => {
      if (index < 2) { // Only first 2 stylesheets
        link.setAttribute('rel', 'preload');
        link.setAttribute('as', 'style');
        link.setAttribute('onload', "this.onload=null;this.rel='stylesheet'");
      }
    });

    // Font preloading completely disabled for better performance
    // Using system fonts to eliminate 404 errors and improve loading speed
    console.log('📝 Font preloading disabled - using system fonts for optimal performance');
  }

  // Enhanced performance monitoring with real-time metrics
  public startAdvancedMonitoring(): void {
    // Monitor Core Web Vitals
    this.monitorCoreWebVitals();

    // Monitor resource loading
    this.monitorResourceLoading();

    // Monitor memory usage
    this.monitorMemoryUsage();

    // Monitor frame rate
    this.monitorFrameRate();

    console.log('📊 Advanced performance monitoring started');
  }

  // Monitor Core Web Vitals
  private monitorCoreWebVitals(): void {
    // Largest Contentful Paint (LCP)
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      const lastEntry = entries[entries.length - 1];
      this.metrics.lcp = lastEntry.startTime;
      console.log('📏 LCP:', this.metrics.lcp.toFixed(2) + 'ms');
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay (FID)
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach((entry: any) => {
        this.metrics.fid = entry.processingStart - entry.startTime;
        console.log('⚡ FID:', this.metrics.fid.toFixed(2) + 'ms');
      });
    }).observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift (CLS)
    let clsValue = 0;
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      this.metrics.cls = clsValue;
      console.log('📐 CLS:', this.metrics.cls.toFixed(4));
    }).observe({ entryTypes: ['layout-shift'] });
  }

  // Monitor resource loading performance
  private monitorResourceLoading(): void {
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach((entry: any) => {
        if (entry.transferSize > 100000) { // Log large resources (>100KB)
          console.log(`📦 Large resource loaded: ${entry.name} (${(entry.transferSize / 1024).toFixed(2)}KB)`);
        }
      });
    }).observe({ entryTypes: ['resource'] });
  }

  // Monitor memory usage
  private monitorMemoryUsage(): void {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        const usedMB = (memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
        const totalMB = (memory.totalJSHeapSize / 1024 / 1024).toFixed(2);

        if (memory.usedJSHeapSize / memory.jsHeapSizeLimit > 0.8) {
          console.warn('🚨 High memory usage:', usedMB + 'MB /' + totalMB + 'MB');
        }
      }, 30000); // Check every 30 seconds
    }
  }

  // Monitor frame rate
  private monitorFrameRate(): void {
    let lastTime = performance.now();
    let frameCount = 0;

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();

      if (currentTime - lastTime >= 1000) { // Every second
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));

        if (fps < 30) {
          console.warn('🎬 Low FPS detected:', fps + 'fps');
        }

        frameCount = 0;
        lastTime = currentTime;
      }

      requestAnimationFrame(measureFPS);
    };

    requestAnimationFrame(measureFPS);
  }

  // Enable critical CSS inlining
  public inlineCriticalCSS(): void {
    if (!this.config.enableCriticalCSS) return;

    // This would typically be done at build time
    // For runtime, we can prioritize above-the-fold styles
    const criticalStyles = `
      /* Critical CSS for above-the-fold content */
      body { margin: 0; font-family: system-ui, -apple-system, sans-serif; }
      .hero { min-height: 100vh; display: flex; align-items: center; }
      .loading { position: fixed; inset: 0; z-index: 9999; }
    `;

    const style = document.createElement('style');
    style.textContent = criticalStyles;
    document.head.insertBefore(style, document.head.firstChild);
  }

  // Optimize third-party scripts
  public optimizeThirdPartyScripts(): void {
    // Defer non-critical scripts
    const scripts = document.querySelectorAll('script[src]');
    scripts.forEach((script) => {
      const src = script.getAttribute('src') || '';
      
      // Defer analytics and non-critical scripts
      if (src.includes('analytics') || src.includes('gtag') || src.includes('facebook')) {
        script.setAttribute('defer', '');
      }
    });
  }

  // Get optimization recommendations
  public getOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];
    const score = this.getPerformanceScore();

    if (score < 90) {
      if (this.metrics.lcp > 2500) {
        recommendations.push('Optimize Largest Contentful Paint (LCP) - consider image optimization and server response times');
      }
      
      if (this.metrics.fid > 100) {
        recommendations.push('Reduce First Input Delay (FID) - minimize JavaScript execution time');
      }
      
      if (this.metrics.cls > 0.1) {
        recommendations.push('Improve Cumulative Layout Shift (CLS) - ensure proper image dimensions and avoid layout shifts');
      }
      
      if (this.metrics.fcp > 1800) {
        recommendations.push('Optimize First Contentful Paint (FCP) - reduce render-blocking resources');
      }

      if (this.metrics.totalSize > 3 * 1024 * 1024) { // 3MB
        recommendations.push('Reduce total page size - enable compression and optimize assets');
      }

      if (this.metrics.fps < 30) {
        recommendations.push('Improve frame rate - optimize animations and reduce JavaScript complexity');
      }

      if (this.metrics.memoryUsage > 0.8) {
        recommendations.push('Optimize memory usage - check for memory leaks and optimize data structures');
      }
    }

    return recommendations;
  }

  // Update configuration
  public updateConfig(newConfig: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Performance configuration updated');
  }

  // Get configuration
  public getConfig(): OptimizationConfig {
    return { ...this.config };
  }

  // Stop monitoring
  public stopMonitoring(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    this.isMonitoring = false;
    console.log('📊 Performance monitoring stopped');
  }

  // Generate performance report
  public generateReport(): {
    score: number;
    metrics: PerformanceMetrics;
    recommendations: string[];
    grade: string;
  } {
    const score = this.getPerformanceScore();
    const recommendations = this.getOptimizationRecommendations();
    
    let grade = 'F';
    if (score >= 90) grade = 'A';
    else if (score >= 80) grade = 'B';
    else if (score >= 70) grade = 'C';
    else if (score >= 60) grade = 'D';

    return {
      score,
      metrics: this.getMetrics(),
      recommendations,
      grade
    };
  }
}

// Export singleton instance
export const advancedPerformanceService = AdvancedPerformanceService.getInstance();
