import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Download, 
  Upload, 
  Save, 
  RefreshCw,
  Database,
  FileText,
  Shield,
  Clock,
  CheckCircle,
  AlertTriangle,
  X,
  HardDrive,
  Cloud,
  Archive
} from 'lucide-react';
import { cmsService } from '../../services/cmsService';
import toast from 'react-hot-toast';
import { SuccessToast, ErrorToast } from '../CustomToast';

interface BackupManagerProps {
  onDataChange: () => void;
}

interface BackupData {
  timestamp: string;
  version: string;
  data: {
    projects: any[];
    blogPosts: any[];
    personalInfo: any;
    skills: any[];
    experience: any[];
    education: any[];
    seoData: any;
    themeSettings: any;
    contactSettings: any;
    heroContent: any;
  };
}

const BackupManager: React.FC<BackupManagerProps> = ({ onDataChange }) => {
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [backupHistory, setBackupHistory] = useState<BackupData[]>([]);

  const generateBackup = (): BackupData => {
    return {
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      data: {
        projects: cmsService.getProjects(),
        blogPosts: cmsService.getBlogPosts(),
        personalInfo: cmsService.getPersonalInfo(),
        skills: cmsService.getSkills(),
        experience: cmsService.getExperience(),
        education: cmsService.getEducation(),
        seoData: JSON.parse(localStorage.getItem('seo_data') || '{}'),
        themeSettings: JSON.parse(localStorage.getItem('theme_settings') || '{}'),
        contactSettings: JSON.parse(localStorage.getItem('contact_settings') || '{}'),
        heroContent: JSON.parse(localStorage.getItem('hero_content') || '{}')
      }
    };
  };

  const exportData = async () => {
    setIsExporting(true);
    try {
      const backup = generateBackup();
      
      // Save to backup history
      const history = [...backupHistory, backup].slice(-10); // Keep last 10 backups
      setBackupHistory(history);
      localStorage.setItem('backup_history', JSON.stringify(history));

      // Download as JSON file
      const dataStr = JSON.stringify(backup, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `portfolio-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast(() => (
        <SuccessToast
          message="Data exported successfully!"
          icon={<Download className="w-5 h-5 text-green-400" />}
        />
      ));
    } catch (error) {
      console.error('Export error:', error);
      toast(() => (
        <ErrorToast
          message="Failed to export data"
        />
      ));
    } finally {
      setIsExporting(false);
    }
  };

  const importData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const backup: BackupData = JSON.parse(e.target?.result as string);
        
        if (!backup.data || !backup.timestamp) {
          throw new Error('Invalid backup file format');
        }

        // Restore all data
        const { data } = backup;
        
        // Restore CMS data
        if (data.projects) {
          data.projects.forEach(project => {
            const { id, createdAt, updatedAt, ...projectData } = project;
            cmsService.addProject(projectData);
          });
        }
        if (data.blogPosts) {
          data.blogPosts.forEach(post => {
            const { id, createdAt, updatedAt, publishedAt, ...postData } = post;
            cmsService.addBlogPost(postData);
          });
        }
        if (data.personalInfo) {
          cmsService.updatePersonalInfo(data.personalInfo);
        }
        if (data.skills) {
          data.skills.forEach(skill => {
            const { id, ...skillData } = skill;
            cmsService.addSkill(skillData);
          });
        }
        if (data.experience) {
          data.experience.forEach(exp => {
            const { id, ...expData } = exp;
            cmsService.addExperience(expData);
          });
        }
        if (data.education) {
          data.education.forEach(edu => {
            const { id, ...eduData } = edu;
            cmsService.addEducation(eduData);
          });
        }

        // Restore settings
        if (data.seoData) {
          localStorage.setItem('seo_data', JSON.stringify(data.seoData));
        }
        if (data.themeSettings) {
          localStorage.setItem('theme_settings', JSON.stringify(data.themeSettings));
        }
        if (data.contactSettings) {
          localStorage.setItem('contact_settings', JSON.stringify(data.contactSettings));
        }
        if (data.heroContent) {
          localStorage.setItem('hero_content', JSON.stringify(data.heroContent));
        }

        onDataChange();

        toast(() => (
          <SuccessToast
            message="Data imported successfully!"
            icon={<Upload className="w-5 h-5 text-green-400" />}
          />
        ));
      } catch (error) {
        console.error('Import error:', error);
        toast(() => (
          <ErrorToast
            message="Failed to import data. Please check the file format."
          />
        ));
      } finally {
        setIsImporting(false);
      }
    };

    reader.readAsText(file);
    event.target.value = ''; // Reset input
  };

  const createAutoBackup = () => {
    const backup = generateBackup();
    const history = [...backupHistory, backup].slice(-10);
    setBackupHistory(history);
    localStorage.setItem('backup_history', JSON.stringify(history));
    localStorage.setItem('last_auto_backup', new Date().toISOString());

    toast(() => (
      <SuccessToast
        message="Auto backup created!"
        icon={<Save className="w-5 h-5 text-green-400" />}
      />
    ));
  };

  const restoreFromHistory = (backup: BackupData) => {
    if (window.confirm('Are you sure you want to restore from this backup? This will overwrite all current data.')) {
      setIsImporting(true);
      try {
        const { data } = backup;
        
        // Clear existing data
        localStorage.clear();
        
        // Restore all data (same logic as importData)
        if (data.projects) {
          data.projects.forEach(project => {
            const { id, createdAt, updatedAt, ...projectData } = project;
            cmsService.addProject(projectData);
          });
        }
        if (data.blogPosts) {
          data.blogPosts.forEach(post => {
            const { id, createdAt, updatedAt, publishedAt, ...postData } = post;
            cmsService.addBlogPost(postData);
          });
        }
        if (data.personalInfo) {
          cmsService.updatePersonalInfo(data.personalInfo);
        }
        if (data.skills) {
          data.skills.forEach(skill => {
            const { id, ...skillData } = skill;
            cmsService.addSkill(skillData);
          });
        }
        if (data.experience) {
          data.experience.forEach(exp => {
            const { id, ...expData } = exp;
            cmsService.addExperience(expData);
          });
        }
        if (data.education) {
          data.education.forEach(edu => {
            const { id, ...eduData } = edu;
            cmsService.addEducation(eduData);
          });
        }

        // Restore settings
        if (data.seoData) {
          localStorage.setItem('seo_data', JSON.stringify(data.seoData));
        }
        if (data.themeSettings) {
          localStorage.setItem('theme_settings', JSON.stringify(data.themeSettings));
        }
        if (data.contactSettings) {
          localStorage.setItem('contact_settings', JSON.stringify(data.contactSettings));
        }
        if (data.heroContent) {
          localStorage.setItem('hero_content', JSON.stringify(data.heroContent));
        }

        onDataChange();

        toast(() => (
          <SuccessToast
            message="Data restored successfully!"
            icon={<RefreshCw className="w-5 h-5 text-green-400" />}
          />
        ));
      } catch (error) {
        console.error('Restore error:', error);
        toast(() => (
          <ErrorToast
            message="Failed to restore data"
          />
        ));
      } finally {
        setIsImporting(false);
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDataSize = (data: any) => {
    const size = new Blob([JSON.stringify(data)]).size;
    return size < 1024 ? `${size} B` : `${(size / 1024).toFixed(1)} KB`;
  };

  React.useEffect(() => {
    // Load backup history
    const history = localStorage.getItem('backup_history');
    if (history) {
      try {
        setBackupHistory(JSON.parse(history));
      } catch (error) {
        console.error('Error loading backup history:', error);
      }
    }
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Backup & Restore</h1>
          <p className="text-gray-400">Manage your portfolio data backups and restore points</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={createAutoBackup}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600/20 text-blue-400 rounded-xl hover:bg-blue-600/30 transition-all duration-200"
          >
            <Save className="w-4 h-4" />
            <span>Auto Backup</span>
          </button>
        </div>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Backup & Export */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
        >
          <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
            <Database className="w-5 h-5 text-green-400" />
            <span>Export Data</span>
          </h2>

          <div className="space-y-4">
            <div className="p-4 bg-green-600/10 rounded-xl border border-green-500/20">
              <h3 className="text-green-400 font-semibold mb-2">Full Portfolio Backup</h3>
              <p className="text-green-200 text-sm mb-4">
                Export all your portfolio data including projects, blog posts, settings, and configurations.
              </p>
              <button
                onClick={exportData}
                disabled={isExporting}
                className={`w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                  isExporting
                    ? 'bg-gray-600 text-gray-300 cursor-not-allowed'
                    : 'bg-green-600 text-white hover:bg-green-700'
                }`}
              >
                {isExporting ? (
                  <>
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    <span>Exporting...</span>
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4" />
                    <span>Export All Data</span>
                  </>
                )}
              </button>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="p-3 bg-white/5 rounded-xl text-center">
                <HardDrive className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                <div className="text-white font-semibold">{cmsService.getProjects().length}</div>
                <div className="text-gray-400 text-sm">Projects</div>
              </div>
              <div className="p-3 bg-white/5 rounded-xl text-center">
                <FileText className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                <div className="text-white font-semibold">{cmsService.getBlogPosts().length}</div>
                <div className="text-gray-400 text-sm">Blog Posts</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Import & Restore */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
        >
          <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
            <Upload className="w-5 h-5 text-blue-400" />
            <span>Import Data</span>
          </h2>

          <div className="space-y-4">
            <div className="p-4 bg-blue-600/10 rounded-xl border border-blue-500/20">
              <h3 className="text-blue-400 font-semibold mb-2">Restore from Backup</h3>
              <p className="text-blue-200 text-sm mb-4">
                Import a previously exported backup file to restore your portfolio data.
              </p>
              <label className={`w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-xl font-medium transition-all duration-200 cursor-pointer ${
                isImporting
                  ? 'bg-gray-600 text-gray-300'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}>
                {isImporting ? (
                  <>
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    <span>Importing...</span>
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4" />
                    <span>Choose Backup File</span>
                  </>
                )}
                <input
                  type="file"
                  accept=".json"
                  onChange={importData}
                  disabled={isImporting}
                  className="hidden"
                />
              </label>
            </div>

            <div className="p-4 bg-yellow-600/10 rounded-xl border border-yellow-500/20">
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle className="w-4 h-4 text-yellow-400" />
                <h3 className="text-yellow-400 font-semibold">Warning</h3>
              </div>
              <p className="text-yellow-200 text-sm">
                Importing will overwrite all current data. Make sure to export your current data first if you want to keep it.
              </p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Backup History */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
      >
        <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
          <Clock className="w-5 h-5 text-purple-400" />
          <span>Backup History</span>
        </h2>

        {backupHistory.length > 0 ? (
          <div className="space-y-3">
            {backupHistory.slice().reverse().map((backup, index) => (
              <div
                key={backup.timestamp}
                className="flex items-center justify-between p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all duration-200"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-lg flex items-center justify-center">
                    <Archive className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="text-white font-medium">
                      Backup #{backupHistory.length - index}
                    </div>
                    <div className="text-gray-400 text-sm">
                      {formatDate(backup.timestamp)} • {getDataSize(backup.data)}
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => restoreFromHistory(backup)}
                    className="px-3 py-2 bg-blue-600/20 text-blue-400 rounded-lg hover:bg-blue-600/30 transition-all duration-200 text-sm"
                  >
                    Restore
                  </button>
                  <button
                    onClick={() => {
                      const dataStr = JSON.stringify(backup, null, 2);
                      const dataBlob = new Blob([dataStr], { type: 'application/json' });
                      const url = URL.createObjectURL(dataBlob);
                      const link = document.createElement('a');
                      link.href = url;
                      link.download = `backup-${backup.timestamp.split('T')[0]}.json`;
                      link.click();
                      URL.revokeObjectURL(url);
                    }}
                    className="px-3 py-2 bg-green-600/20 text-green-400 rounded-lg hover:bg-green-600/30 transition-all duration-200 text-sm"
                  >
                    Download
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Archive className="w-12 h-12 text-gray-500 mx-auto mb-4" />
            <p className="text-gray-400">No backup history available</p>
            <p className="text-gray-500 text-sm">Create your first backup to see it here</p>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default BackupManager;
