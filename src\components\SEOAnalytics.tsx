import React, { useEffect, useState } from 'react';

interface SEOAnalyticsProps {
  pageType?: string;
  title?: string;
  description?: string;
  keywords?: string[];
}

interface SEOMetrics {
  titleScore: number;
  descriptionScore: number;
  keywordScore: number;
  overallScore: number;
  issues: string[];
  recommendations: string[];
}

const SEOAnalytics: React.FC<SEOAnalyticsProps> = ({
  pageType = 'home',
  title = '',
  description = '',
  keywords = []
}) => {
  const [metrics, setMetrics] = useState<SEOMetrics>({
    titleScore: 0,
    descriptionScore: 0,
    keywordScore: 0,
    overallScore: 0,
    issues: [],
    recommendations: []
  });

  const calculateSEOScore = () => {
    let titleScore = 0;
    let descriptionScore = 0;
    let keywordScore = 0;
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Title Analysis
    if (title.length >= 30 && title.length <= 60) {
      titleScore = 100;
    } else if (title.length < 30) {
      titleScore = 60;
      issues.push('Title is too short (less than 30 characters)');
      recommendations.push('Expand your title to 30-60 characters for better SEO');
    } else {
      titleScore = 70;
      issues.push('Title is too long (more than 60 characters)');
      recommendations.push('Shorten your title to 30-60 characters');
    }

    // Check for power words in title
    const powerWords = ['best', 'top', 'expert', 'professional', 'premium', 'advanced', 'ultimate', 'complete'];
    const hasPowerWords = powerWords.some(word => title.toLowerCase().includes(word));
    if (hasPowerWords) {
      titleScore += 10;
    } else {
      recommendations.push('Consider adding power words like "best", "top", "expert" to your title');
    }

    // Check for numbers in title
    if (/\d/.test(title)) {
      titleScore += 5;
    } else {
      recommendations.push('Consider adding numbers to your title (e.g., "5+ Years", "50+ Projects")');
    }

    // Description Analysis
    if (description.length >= 120 && description.length <= 160) {
      descriptionScore = 100;
    } else if (description.length < 120) {
      descriptionScore = 60;
      issues.push('Meta description is too short (less than 120 characters)');
      recommendations.push('Expand your description to 120-160 characters');
    } else {
      descriptionScore = 70;
      issues.push('Meta description is too long (more than 160 characters)');
      recommendations.push('Shorten your description to 120-160 characters');
    }

    // Check for call-to-action in description
    const ctaWords = ['hire', 'contact', 'get', 'download', 'view', 'explore', 'discover'];
    const hasCTA = ctaWords.some(word => description.toLowerCase().includes(word));
    if (hasCTA) {
      descriptionScore += 10;
    } else {
      recommendations.push('Add a call-to-action to your description (e.g., "Hire now", "Get in touch")');
    }

    // Keyword Analysis
    if (keywords.length >= 10) {
      keywordScore = 100;
    } else if (keywords.length >= 5) {
      keywordScore = 80;
    } else {
      keywordScore = 50;
      issues.push('Not enough keywords (minimum 10 recommended)');
      recommendations.push('Add more relevant keywords to improve search visibility');
    }

    // Check for long-tail keywords
    const longTailKeywords = keywords.filter(keyword => keyword.split(' ').length >= 3);
    if (longTailKeywords.length >= 3) {
      keywordScore += 10;
    } else {
      recommendations.push('Add more long-tail keywords (3+ words) for better targeting');
    }

    // Check for location-based keywords
    const locationKeywords = keywords.filter(keyword => 
      keyword.toLowerCase().includes('india') || 
      keyword.toLowerCase().includes('gurugram') || 
      keyword.toLowerCase().includes('delhi')
    );
    if (locationKeywords.length >= 2) {
      keywordScore += 5;
    } else {
      recommendations.push('Add location-based keywords for better local SEO');
    }

    // Overall Score Calculation
    const overallScore = Math.round((titleScore + descriptionScore + keywordScore) / 3);

    // Additional SEO Checks
    if (title.toLowerCase().includes(pageType)) {
      titleScore += 5;
    }

    if (description.toLowerCase().includes('portfolio') || description.toLowerCase().includes('developer')) {
      descriptionScore += 5;
    }

    // Performance recommendations based on score
    if (overallScore >= 90) {
      recommendations.push('Excellent SEO! Consider A/B testing different variations');
    } else if (overallScore >= 70) {
      recommendations.push('Good SEO foundation. Focus on the issues mentioned above');
    } else {
      recommendations.push('SEO needs improvement. Address all issues for better rankings');
    }

    setMetrics({
      titleScore: Math.min(titleScore, 100),
      descriptionScore: Math.min(descriptionScore, 100),
      keywordScore: Math.min(keywordScore, 100),
      overallScore: Math.min(overallScore, 100),
      issues,
      recommendations
    });
  };

  useEffect(() => {
    calculateSEOScore();
  }, [title, description, keywords, pageType]);

  // Track SEO performance
  useEffect(() => {
    // Send SEO metrics to analytics
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'seo_analysis', {
        event_category: 'SEO',
        event_label: pageType,
        value: metrics.overallScore,
        custom_parameters: {
          title_score: metrics.titleScore,
          description_score: metrics.descriptionScore,
          keyword_score: metrics.keywordScore,
          issues_count: metrics.issues.length
        }
      });
    }

    // Store SEO metrics for admin panel
    const seoMetrics = {
      timestamp: new Date().toISOString(),
      pageType,
      metrics,
      title,
      description,
      keywordCount: keywords.length
    };

    localStorage.setItem(`seo_metrics_${pageType}`, JSON.stringify(seoMetrics));
  }, [metrics, pageType, title, description, keywords]);

  // Advanced SEO tracking
  useEffect(() => {
    // Track page performance
    if (typeof window !== 'undefined') {
      // Core Web Vitals tracking
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navigationEntry = entry as PerformanceNavigationTiming;
            const loadTime = navigationEntry.loadEventEnd - navigationEntry.loadEventStart;
            
            // Track load time for SEO
            if ((window as any).gtag) {
              (window as any).gtag('event', 'page_load_time', {
                event_category: 'Performance',
                event_label: pageType,
                value: Math.round(loadTime),
                custom_parameters: {
                  seo_score: metrics.overallScore
                }
              });
            }
          }
        }
      });

      observer.observe({ entryTypes: ['navigation'] });

      return () => observer.disconnect();
    }
  }, [pageType, metrics.overallScore]);

  // Schema.org tracking for rich snippets
  useEffect(() => {
    const schemaData = {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": title,
      "description": description,
      "url": window.location.href,
      "keywords": keywords.join(', '),
      "author": {
        "@type": "Person",
        "name": "Nural Bhardwaj"
      },
      "publisher": {
        "@type": "Person",
        "name": "Nural Bhardwaj"
      },
      "dateModified": new Date().toISOString(),
      "mainEntity": {
        "@type": "Person",
        "@id": "https://nuralbhardwaj.me#person"
      }
    };

    // Inject schema for current page
    const existingSchema = document.querySelector(`script[data-page="${pageType}"]`);
    if (existingSchema) {
      existingSchema.remove();
    }

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.setAttribute('data-page', pageType);
    script.textContent = JSON.stringify(schemaData);
    document.head.appendChild(script);

    return () => {
      const scriptToRemove = document.querySelector(`script[data-page="${pageType}"]`);
      if (scriptToRemove) {
        scriptToRemove.remove();
      }
    };
  }, [title, description, keywords, pageType]);

  // Inject advanced meta tags for current page
  useEffect(() => {
    // Remove existing dynamic meta tags
    const existingMetas = document.querySelectorAll('meta[data-dynamic="true"]');
    existingMetas.forEach(meta => meta.remove());

    // Add optimized meta tags
    const metaTags = [
      { name: 'page-topic', content: `${pageType} - Full Stack Development` },
      { name: 'page-type', content: pageType },
      { name: 'content-score', content: metrics.overallScore.toString() },
      { name: 'last-updated', content: new Date().toISOString() },
      { property: 'article:author', content: 'Nural Bhardwaj' },
      { property: 'article:publisher', content: 'https://nuralbhardwaj.me' },
      { name: 'twitter:label1', content: 'Experience' },
      { name: 'twitter:data1', content: '5+ Years' },
      { name: 'twitter:label2', content: 'Projects' },
      { name: 'twitter:data2', content: '50+' }
    ];

    metaTags.forEach(tag => {
      const meta = document.createElement('meta');
      if (tag.name) meta.setAttribute('name', tag.name);
      if (tag.property) meta.setAttribute('property', tag.property);
      meta.setAttribute('content', tag.content);
      meta.setAttribute('data-dynamic', 'true');
      document.head.appendChild(meta);
    });

    return () => {
      const dynamicMetas = document.querySelectorAll('meta[data-dynamic="true"]');
      dynamicMetas.forEach(meta => meta.remove());
    };
  }, [pageType, metrics.overallScore]);

  return null; // This component only handles SEO tracking, no UI
};

export default SEOAnalytics;
