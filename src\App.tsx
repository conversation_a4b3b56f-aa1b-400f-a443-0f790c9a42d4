import { AnimatePresence, motion } from 'framer-motion';
import { ArrowUp } from 'lucide-react';
import React, { Suspense, useEffect, useState } from 'react';
import { HelmetProvider } from 'react-helmet-async';
import { Toaster } from 'react-hot-toast';
import About from './components/About';
import { FloatingActionButton } from './components/AnimatedButton';
import { AutoDeployNotification } from './components/AutoDeployNotification';
import Blog from './components/Blog';
import Contact from './components/Contact';
import { CursorFollower } from './components/EnhancedAnimations';
import Footer from './components/Footer';
import Hero from './components/Hero';
import { HackerTerminalLoader } from './components/Loading';
import { MobileOptimizedAnimation, useMobileCriticalOptimization, useMobileOptimization } from './components/MobilePerformanceOptimizer';
import MobileResponsivenessTest from './components/MobileResponsivenessTest';
import Navbar from './components/Navbar';
import Projects from './components/Projects';
import { useResponsive } from './components/ResponsiveUtils';
import SEOHead from './components/SEOHead';
import Skills from './components/Skills';
import { AnimationProvider } from './contexts/AnimationContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { captchaService } from './services/captchaService';
import { cdnService } from './services/cdnService';
import { codeSplittingService } from './services/codeSplittingService';
import { cspService } from './services/cspService';
import { imageOptimizationService } from './services/imageOptimizationService';
import { securityService } from './services/securityService';
import { sslService } from './services/sslService';
import { pageTransition } from './utils/animations';
import { deviceDetection, shouldShowLoadingAnimation } from './utils/deviceDetection';
import './utils/spacingProtection';

// Lazy load heavy components
const LazyInteractiveResume = React.lazy(() => import('./components/InteractiveResume'));
const LazySEOAnalytics = React.lazy(() => import('./components/SEOAnalytics'));
const LazySEODominance = React.lazy(() => import('./components/SEODominance'));
const LazyPWAManager = React.lazy(() => import('./components/PWAManager'));

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const { isMobile: isMobileDevice } = useMobileOptimization();
  const { isDesktop } = useResponsive();

  // Apply critical mobile optimizations
  useMobileCriticalOptimization();

  // Get device info and determine if loading animation should be shown
  const deviceInfo = deviceDetection.getDeviceInfo();
  const showLoadingAnimation = shouldShowLoadingAnimation();

  // Initialize all optimization services
  useEffect(() => {
    // Initialize security services
    securityService.getClientIdentifier();
    captchaService.initialize({ provider: 'custom', theme: 'dark' });
    void cspService; // Initialize CSP service
    void sslService; // Initialize SSL service

    // Initialize advanced security
    import('./services/advancedSecurityService').then(({ advancedSecurityService }) => {
      advancedSecurityService.getConfig();
      console.log('🛡️ Advanced security monitoring active');
    }).catch(error => {
      console.warn('Advanced security service failed to load:', error);
    });

    // Initialize security middleware
    import('./services/securityMiddleware').then(({ securityMiddleware }) => {
      securityMiddleware.getSecurityStatus();
      console.log('🛡️ Security middleware active');
    }).catch(error => {
      console.warn('Security middleware failed to load:', error);
    });

    // Initialize performance services
    void imageOptimizationService; // Initialize image optimization
    void codeSplittingService; // Initialize code splitting
    void cdnService; // Initialize CDN service

    // Initialize advanced performance services
    Promise.all([
      import('./services/advancedPerformanceService'),
      import('./services/memoryOptimizationService'),
      import('./services/resourcePreloadingService')
    ]).then(([
      { advancedPerformanceService },
      { memoryOptimizationService },
      { resourcePreloadingService }
    ]) => {
      // Essential optimizations
      advancedPerformanceService.optimizeImages();
      advancedPerformanceService.startAdvancedMonitoring();

      // Memory optimization
      memoryOptimizationService.startMemoryOptimization();

      // Intelligent resource preloading
      resourcePreloadingService.getConfig();

      console.log('⚡ Advanced performance suite active');
    }).catch(error => {
      console.warn('Performance services failed to load:', error);
    });

    console.log('🛡️ Security services initialized');
    console.log('⚡ Performance services initialized');
    console.log('📱 Device Info:', deviceInfo);
    console.log('🎬 Show Loading Animation:', showLoadingAnimation);
  }, [deviceInfo, showLoadingAnimation]);

  useEffect(() => {
    // Show loading animation based on device preferences
    if (!showLoadingAnimation) {
      // Skip loading animation only if user prefers reduced motion
      setIsLoading(false);
      return;
    }

    // Show loading animation for appropriate time based on device
    const loadingTime = deviceInfo.isMobile ? 3000 : 4000; // Shorter on mobile, longer on desktop
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, loadingTime);

    return () => clearTimeout(timer);
  }, [showLoadingAnimation, deviceInfo.isMobile]);

  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          setShowScrollTop(window.scrollY > 500);
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <HelmetProvider>
      <LanguageProvider>
        <AnimationProvider>
          <SEOHead />
          <Suspense fallback={<div className="text-green-400 text-sm">Loading SEO...</div>}>
            <LazySEODominance pageType="home" />
            <LazySEOAnalytics pageType="home" />
            <LazyPWAManager />
          </Suspense>
          <AnimatePresence mode="wait">
          {isLoading && showLoadingAnimation ? (
            <HackerTerminalLoader key="loader" />
          ) : (
          <motion.div
            key="app"
            className="min-h-screen bg-gray-900 text-white overflow-x-hidden"
            variants={pageTransition}
            initial="initial"
            animate="animate"
            exit="exit"
          >
          {/* Custom Cursor for Desktop Only */}
          {isDesktop && !isMobileDevice && <CursorFollower />}

          <Navbar />
          <Hero />
          <About />
          <Skills />
          <Projects />
          <MobileOptimizedAnimation enableOnMobile={false}>
            <Blog />
          </MobileOptimizedAnimation>
          <Suspense fallback={<div className="text-green-400 text-sm p-8 text-center">Loading Resume...</div>}>
            <LazyInteractiveResume />
          </Suspense>
          <Contact />
          <Footer />

          {/* Mobile Responsiveness Test Component */}
          {process.env.NODE_ENV === 'development' && <MobileResponsivenessTest />}

          {/* Enhanced Toast Notifications */}
          <Toaster
            position="top-right"
            gutter={12}
            containerStyle={{
              top: 80,
              right: 20,
            }}
            toastOptions={{
              duration: 5000,
              style: {
                background: 'transparent',
                boxShadow: 'none',
                border: 'none',
                padding: 0,
                margin: 0,
              },
              success: {
                duration: 3000,
              },
              error: {
                duration: 6000,
              },
            }}
          />

          {/* Auto-Deploy Notification */}
          <AutoDeployNotification />

          {/* Floating Action Button */}
          <AnimatePresence>
            {showScrollTop && (
              <FloatingActionButton
                onClick={scrollToTop}
                className="fixed bottom-8 right-8 z-50"
              >
                <ArrowUp className="w-6 h-6" />
              </FloatingActionButton>
            )}
          </AnimatePresence>
          </motion.div>
        )}
      </AnimatePresence>
        </AnimationProvider>
      </LanguageProvider>
    </HelmetProvider>
  );
}

export default App;