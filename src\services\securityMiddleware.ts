// Comprehensive Security Middleware
// Integrates all security services for complete protection

import { securityService } from './securityService';
import { advancedSecurityService } from './advancedSecurityService';
import { cspService } from './cspService';
import { sslService } from './sslService';
import { captchaService } from './captchaService';

export interface SecurityCheckResult {
  allowed: boolean;
  reason?: string;
  action_required?: string;
  risk_score: number;
  threats: string[];
}

export interface RequestContext {
  url: string;
  method: string;
  headers: Record<string, string>;
  body?: any;
  params?: any;
  ip?: string;
  userAgent?: string;
  timestamp: number;
}

class SecurityMiddleware {
  private static instance: SecurityMiddleware;
  private isInitialized = false;

  private constructor() {
    this.initialize();
  }

  public static getInstance(): SecurityMiddleware {
    if (!SecurityMiddleware.instance) {
      SecurityMiddleware.instance = new SecurityMiddleware();
    }
    return SecurityMiddleware.instance;
  }

  // Initialize all security services
  private initialize(): void {
    if (this.isInitialized) return;

    try {
      // Initialize core security services
      securityService.getClientIdentifier();
      advancedSecurityService.getConfig();
      cspService.getViolations();
      sslService.getMetrics();
      
      // Setup request interceptors
      this.setupRequestInterceptors();
      
      // Setup global error handlers
      this.setupErrorHandlers();
      
      this.isInitialized = true;
      console.log('🛡️ Security middleware initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize security middleware:', error);
    }
  }

  // Main security check function
  public async performSecurityCheck(context: RequestContext): Promise<SecurityCheckResult> {
    try {
      // Step 1: Basic rate limiting
      const rateLimitResult = securityService.performSecurityCheck(context, this.getEndpointType(context.url));
      if (!rateLimitResult.allowed) {
        return {
          allowed: false,
          reason: rateLimitResult.reason,
          action_required: 'Rate limit exceeded - wait before retrying',
          risk_score: 5,
          threats: ['Rate limiting']
        };
      }

      // Step 2: Advanced threat analysis
      const threatAnalysis = advancedSecurityService.analyzeRequest(context);
      
      // Step 3: Content Security Policy validation
      const cspValidation = this.validateCSP(context);
      
      // Step 4: SSL/TLS validation
      const sslValidation = this.validateSSL(context);
      
      // Step 5: Combine all results
      const combinedRiskScore = threatAnalysis.risk_score + 
                               (cspValidation.violations * 2) + 
                               (sslValidation.insecure ? 3 : 0);
      
      const allThreats = [
        ...threatAnalysis.threats,
        ...cspValidation.threats,
        ...sslValidation.threats
      ];

      // Step 6: Make final decision
      const isAllowed = combinedRiskScore < 8; // Threshold for blocking
      
      // Step 7: Log security event
      if (!isAllowed || combinedRiskScore > 5) {
        advancedSecurityService.logSecurityEvent({
          type: combinedRiskScore >= 8 ? 'threat_detected' : 'suspicious_behavior',
          severity: this.getSeverityFromRiskScore(combinedRiskScore),
          source: context.ip || 'unknown',
          details: {
            context,
            risk_score: combinedRiskScore,
            threats: allThreats
          },
          action_taken: isAllowed ? 'Monitored' : 'Blocked'
        });
      }

      return {
        allowed: isAllowed,
        reason: isAllowed ? undefined : `Security threat detected: ${allThreats.join(', ')}`,
        action_required: isAllowed ? undefined : 'Request blocked due to security concerns',
        risk_score: combinedRiskScore,
        threats: allThreats
      };

    } catch (error) {
      console.error('Security check failed:', error);
      return {
        allowed: false,
        reason: 'Security check failed',
        action_required: 'Internal security error',
        risk_score: 10,
        threats: ['Security system error']
      };
    }
  }

  // Validate Content Security Policy
  private validateCSP(context: RequestContext): { violations: number; threats: string[] } {
    const threats: string[] = [];
    let violations = 0;

    // Check for CSP violations in request
    if (context.headers['content-type']?.includes('text/html')) {
      // Check for inline scripts in HTML content
      if (context.body && typeof context.body === 'string') {
        if (/<script[^>]*>/.test(context.body)) {
          threats.push('Inline script detected');
          violations++;
        }
        
        if (/on\w+\s*=/.test(context.body)) {
          threats.push('Inline event handlers detected');
          violations++;
        }
      }
    }

    return { violations, threats };
  }

  // Validate SSL/TLS security
  private validateSSL(context: RequestContext): { insecure: boolean; threats: string[] } {
    const threats: string[] = [];
    let insecure = false;

    // Check if request is over HTTPS
    if (context.url.startsWith('http://') && !context.url.includes('localhost')) {
      threats.push('Insecure HTTP connection');
      insecure = true;
    }

    // Check for weak SSL/TLS indicators
    if (context.headers['x-forwarded-proto'] === 'http') {
      threats.push('HTTP forwarded protocol');
      insecure = true;
    }

    return { insecure, threats };
  }

  // Get endpoint type for rate limiting
  private getEndpointType(url: string): string {
    if (url.includes('/admin')) return 'admin-login';
    if (url.includes('/contact')) return 'contact';
    if (url.includes('/api/')) return 'api';
    if (url.includes('/download')) return 'download';
    if (url.includes('/search')) return 'search';
    return 'general';
  }

  // Get severity from risk score
  private getSeverityFromRiskScore(score: number): 'low' | 'medium' | 'high' | 'critical' {
    if (score >= 10) return 'critical';
    if (score >= 8) return 'high';
    if (score >= 5) return 'medium';
    return 'low';
  }

  // Setup request interceptors
  private setupRequestInterceptors(): void {
    // Intercept fetch requests
    const originalFetch = window.fetch;
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const context = this.createRequestContext(input, init);
      const securityCheck = await this.performSecurityCheck(context);
      
      if (!securityCheck.allowed) {
        console.warn('🚫 Request blocked by security middleware:', securityCheck.reason);
        throw new Error(`Security: ${securityCheck.reason}`);
      }
      
      return originalFetch(input, init);
    };

    // Intercept XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method: string, url: string | URL, ...args: any[]) {
      const context: RequestContext = {
        url: url.toString(),
        method: method.toUpperCase(),
        headers: {},
        timestamp: Date.now()
      };
      
      // Note: For XMLHttpRequest, we do a simplified check
      const identifier = securityService.getClientIdentifier();
      if (!securityService.checkRateLimit(identifier, 'api')) {
        throw new Error('Security: Rate limit exceeded');
      }
      
      return originalXHROpen.call(this, method, url, ...args);
    };
  }

  // Setup global error handlers
  private setupErrorHandlers(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      if (event.reason?.message?.includes('Security:')) {
        console.warn('🛡️ Security-related promise rejection:', event.reason);
        event.preventDefault(); // Prevent default error handling
      }
    });

    // Handle global errors
    window.addEventListener('error', (event) => {
      if (event.message?.includes('Security:')) {
        console.warn('🛡️ Security-related error:', event.message);
      }
    });

    // Handle CSP violations
    document.addEventListener('securitypolicyviolation', (event) => {
      console.warn('🚨 CSP Violation:', event);
      advancedSecurityService.logSecurityEvent({
        type: 'security_violation',
        severity: 'medium',
        source: 'csp',
        details: {
          directive: event.violatedDirective,
          blocked_uri: event.blockedURI,
          source_file: event.sourceFile,
          line_number: event.lineNumber
        },
        action_taken: 'CSP violation logged'
      });
    });
  }

  // Create request context from fetch parameters
  private createRequestContext(input: RequestInfo | URL, init?: RequestInit): RequestContext {
    const url = typeof input === 'string' ? input : input.toString();
    const method = init?.method || 'GET';
    const headers = init?.headers ? this.headersToObject(init.headers) : {};
    
    return {
      url,
      method: method.toUpperCase(),
      headers,
      body: init?.body,
      timestamp: Date.now(),
      userAgent: navigator.userAgent
    };
  }

  // Convert headers to object
  private headersToObject(headers: HeadersInit): Record<string, string> {
    const result: Record<string, string> = {};
    
    if (headers instanceof Headers) {
      headers.forEach((value, key) => {
        result[key.toLowerCase()] = value;
      });
    } else if (Array.isArray(headers)) {
      headers.forEach(([key, value]) => {
        result[key.toLowerCase()] = value;
      });
    } else if (headers) {
      Object.entries(headers).forEach(([key, value]) => {
        result[key.toLowerCase()] = value;
      });
    }
    
    return result;
  }

  // Manual security check for custom requests
  public async checkRequest(url: string, options: any = {}): Promise<SecurityCheckResult> {
    const context: RequestContext = {
      url,
      method: options.method || 'GET',
      headers: options.headers || {},
      body: options.body,
      params: options.params,
      timestamp: Date.now(),
      userAgent: navigator.userAgent
    };

    return this.performSecurityCheck(context);
  }

  // Get security status
  public getSecurityStatus(): {
    initialized: boolean;
    services_active: string[];
    recent_threats: number;
    blocked_requests: number;
  } {
    const metrics = securityService.getMetrics();
    const events = advancedSecurityService.getSecurityEvents(50);
    const recentThreats = events.filter(e => 
      e.type === 'threat_detected' && 
      (Date.now() - e.timestamp) < 3600000 // Last hour
    ).length;

    return {
      initialized: this.isInitialized,
      services_active: [
        'Rate Limiting',
        'Threat Detection',
        'CSP Protection',
        'SSL Validation',
        'Behavioral Analysis'
      ],
      recent_threats: recentThreats,
      blocked_requests: metrics.blockedRequests
    };
  }

  // Emergency security lockdown
  public emergencyLockdown(): void {
    console.warn('🚨 EMERGENCY SECURITY LOCKDOWN ACTIVATED');
    
    // Block all non-essential requests
    const originalFetch = window.fetch;
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input.toString();
      
      // Only allow essential requests
      const essentialPaths = ['/api/health', '/api/status'];
      if (!essentialPaths.some(path => url.includes(path))) {
        throw new Error('Security: Emergency lockdown active');
      }
      
      return originalFetch(input, init);
    };

    // Log lockdown event
    advancedSecurityService.logSecurityEvent({
      type: 'security_violation',
      severity: 'critical',
      source: 'system',
      details: { action: 'emergency_lockdown' },
      action_taken: 'All non-essential requests blocked'
    });
  }
}

// Export singleton instance
export const securityMiddleware = SecurityMiddleware.getInstance();
