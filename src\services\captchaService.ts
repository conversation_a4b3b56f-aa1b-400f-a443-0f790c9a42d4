// Advanced CAPTCHA Service with Multiple Providers
// Supports reCAPTCHA, hCaptcha, and custom challenges

export type CaptchaProvider = 'recaptcha' | 'hcaptcha' | 'custom' | 'turnstile';

export interface CaptchaConfig {
  provider: CaptchaProvider;
  siteKey: string;
  secretKey?: string; // For server-side verification
  theme?: 'light' | 'dark';
  size?: 'normal' | 'compact' | 'invisible';
  language?: string;
}

export interface CaptchaChallenge {
  id: string;
  question: string;
  answer: string;
  type: 'math' | 'text' | 'image';
  options?: string[];
  imageUrl?: string;
}

export interface CaptchaResult {
  success: boolean;
  token?: string;
  error?: string;
  score?: number; // For reCAPTCHA v3
}

class CaptchaService {
  private static instance: CaptchaService;
  private config: CaptchaConfig;
  private challenges: CaptchaChallenge[] = [];
  private verificationAttempts: Map<string, number> = new Map();

  // Default configuration
  private defaultConfig: CaptchaConfig = {
    provider: 'custom',
    siteKey: 'demo-site-key',
    theme: 'dark',
    size: 'normal',
    language: 'en'
  };

  private constructor() {
    this.config = this.loadConfig();
    this.generateCustomChallenges();
    this.loadExternalProviders();
  }

  public static getInstance(): CaptchaService {
    if (!CaptchaService.instance) {
      CaptchaService.instance = new CaptchaService();
    }
    return CaptchaService.instance;
  }

  // Initialize CAPTCHA provider
  public async initialize(config?: Partial<CaptchaConfig>): Promise<void> {
    if (config) {
      this.config = { ...this.config, ...config };
      this.saveConfig();
    }

    switch (this.config.provider) {
      case 'recaptcha':
        await this.loadRecaptcha();
        break;
      case 'hcaptcha':
        await this.loadHCaptcha();
        break;
      case 'turnstile':
        await this.loadTurnstile();
        break;
      case 'custom':
        // Custom challenges are already generated
        break;
    }
  }

  // Load Google reCAPTCHA
  private async loadRecaptcha(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (typeof window.grecaptcha !== 'undefined') {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = `https://www.google.com/recaptcha/api.js?render=${this.config.siteKey}`;
      script.async = true;
      script.defer = true;
      
      script.onload = () => {
        window.grecaptcha.ready(() => {
          console.log('reCAPTCHA loaded successfully');
          resolve();
        });
      };
      
      script.onerror = () => {
        console.error('Failed to load reCAPTCHA');
        reject(new Error('Failed to load reCAPTCHA'));
      };
      
      document.head.appendChild(script);
    });
  }

  // Load hCaptcha
  private async loadHCaptcha(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (typeof window.hcaptcha !== 'undefined') {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://js.hcaptcha.com/1/api.js';
      script.async = true;
      script.defer = true;
      
      script.onload = () => {
        console.log('hCaptcha loaded successfully');
        resolve();
      };
      
      script.onerror = () => {
        console.error('Failed to load hCaptcha');
        reject(new Error('Failed to load hCaptcha'));
      };
      
      document.head.appendChild(script);
    });
  }

  // Load Cloudflare Turnstile
  private async loadTurnstile(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (typeof window.turnstile !== 'undefined') {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
      script.async = true;
      script.defer = true;
      
      script.onload = () => {
        console.log('Turnstile loaded successfully');
        resolve();
      };
      
      script.onerror = () => {
        console.error('Failed to load Turnstile');
        reject(new Error('Failed to load Turnstile'));
      };
      
      document.head.appendChild(script);
    });
  }

  // Execute CAPTCHA challenge
  public async execute(action: string = 'submit'): Promise<CaptchaResult> {
    try {
      switch (this.config.provider) {
        case 'recaptcha':
          return await this.executeRecaptcha(action);
        case 'hcaptcha':
          return await this.executeHCaptcha();
        case 'turnstile':
          return await this.executeTurnstile();
        case 'custom':
          return await this.executeCustomChallenge();
        default:
          throw new Error('Unknown CAPTCHA provider');
      }
    } catch (error) {
      console.error('CAPTCHA execution failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'CAPTCHA failed'
      };
    }
  }

  // Execute reCAPTCHA
  private async executeRecaptcha(action: string): Promise<CaptchaResult> {
    if (typeof window.grecaptcha === 'undefined') {
      throw new Error('reCAPTCHA not loaded');
    }

    const token = await window.grecaptcha.execute(this.config.siteKey, { action });
    
    return {
      success: true,
      token,
      score: 0.9 // Simulated score for demo
    };
  }

  // Execute hCaptcha
  private async executeHCaptcha(): Promise<CaptchaResult> {
    return new Promise((resolve) => {
      // For demo purposes, simulate hCaptcha response
      setTimeout(() => {
        resolve({
          success: true,
          token: 'hcaptcha-demo-token-' + Date.now()
        });
      }, 1000);
    });
  }

  // Execute Turnstile
  private async executeTurnstile(): Promise<CaptchaResult> {
    return new Promise((resolve) => {
      // For demo purposes, simulate Turnstile response
      setTimeout(() => {
        resolve({
          success: true,
          token: 'turnstile-demo-token-' + Date.now()
        });
      }, 800);
    });
  }

  // Execute custom challenge
  private async executeCustomChallenge(): Promise<CaptchaResult> {
    const challenge = this.getRandomChallenge();
    
    return new Promise((resolve) => {
      // This would typically show a modal or inline challenge
      // For demo, we'll simulate user interaction
      const userAnswer = prompt(challenge.question);
      
      const success = userAnswer?.toLowerCase().trim() === challenge.answer.toLowerCase().trim();
      
      resolve({
        success,
        token: success ? 'custom-challenge-' + Date.now() : undefined,
        error: success ? undefined : 'Incorrect answer'
      });
    });
  }

  // Generate custom challenges
  private generateCustomChallenges(): void {
    this.challenges = [
      {
        id: 'math1',
        question: 'What is 7 + 5?',
        answer: '12',
        type: 'math'
      },
      {
        id: 'math2',
        question: 'What is 15 - 8?',
        answer: '7',
        type: 'math'
      },
      {
        id: 'math3',
        question: 'What is 6 × 4?',
        answer: '24',
        type: 'math'
      },
      {
        id: 'text1',
        question: 'What color is the sky on a clear day?',
        answer: 'blue',
        type: 'text'
      },
      {
        id: 'text2',
        question: 'How many days are in a week?',
        answer: '7',
        type: 'text'
      },
      {
        id: 'text3',
        question: 'What is the opposite of "hot"?',
        answer: 'cold',
        type: 'text'
      },
      {
        id: 'tech1',
        question: 'What does HTML stand for? (abbreviation)',
        answer: 'hypertext markup language',
        type: 'text'
      },
      {
        id: 'tech2',
        question: 'What programming language is this portfolio built with? (hint: starts with T)',
        answer: 'typescript',
        type: 'text'
      }
    ];
  }

  // Get random challenge
  private getRandomChallenge(): CaptchaChallenge {
    const randomIndex = Math.floor(Math.random() * this.challenges.length);
    return this.challenges[randomIndex];
  }

  // Verify CAPTCHA token (client-side validation)
  public async verify(token: string, userAnswer?: string): Promise<boolean> {
    try {
      switch (this.config.provider) {
        case 'recaptcha':
          return await this.verifyRecaptcha(token);
        case 'hcaptcha':
          return await this.verifyHCaptcha(token);
        case 'turnstile':
          return await this.verifyTurnstile(token);
        case 'custom':
          return this.verifyCustomChallenge(token, userAnswer);
        default:
          return false;
      }
    } catch (error) {
      console.error('CAPTCHA verification failed:', error);
      return false;
    }
  }

  // Verify reCAPTCHA token
  private async verifyRecaptcha(token: string): Promise<boolean> {
    // In production, this would be done server-side
    // For demo, we'll simulate verification
    return token.startsWith('recaptcha-') || token.length > 100;
  }

  // Verify hCaptcha token
  private async verifyHCaptcha(token: string): Promise<boolean> {
    // In production, this would be done server-side
    return token.startsWith('hcaptcha-demo-token-');
  }

  // Verify Turnstile token
  private async verifyTurnstile(token: string): Promise<boolean> {
    // In production, this would be done server-side
    return token.startsWith('turnstile-demo-token-');
  }

  // Verify custom challenge
  private verifyCustomChallenge(token: string, userAnswer?: string): boolean {
    return token.startsWith('custom-challenge-');
  }

  // Render CAPTCHA widget
  public renderWidget(containerId: string, callback?: (token: string) => void): void {
    const container = document.getElementById(containerId);
    if (!container) {
      console.error('CAPTCHA container not found:', containerId);
      return;
    }

    switch (this.config.provider) {
      case 'recaptcha':
        this.renderRecaptchaWidget(container, callback);
        break;
      case 'hcaptcha':
        this.renderHCaptchaWidget(container, callback);
        break;
      case 'turnstile':
        this.renderTurnstileWidget(container, callback);
        break;
      case 'custom':
        this.renderCustomWidget(container, callback);
        break;
    }
  }

  // Render reCAPTCHA widget
  private renderRecaptchaWidget(container: HTMLElement, callback?: (token: string) => void): void {
    if (typeof window.grecaptcha === 'undefined') {
      console.error('reCAPTCHA not loaded');
      return;
    }

    window.grecaptcha.render(container, {
      sitekey: this.config.siteKey,
      theme: this.config.theme,
      size: this.config.size,
      callback: callback
    });
  }

  // Render hCaptcha widget
  private renderHCaptchaWidget(container: HTMLElement, callback?: (token: string) => void): void {
    container.innerHTML = `
      <div class="h-captcha" 
           data-sitekey="${this.config.siteKey}"
           data-theme="${this.config.theme}"
           data-size="${this.config.size}">
      </div>
    `;

    // Simulate hCaptcha callback for demo
    if (callback) {
      setTimeout(() => {
        callback('hcaptcha-demo-token-' + Date.now());
      }, 2000);
    }
  }

  // Render Turnstile widget
  private renderTurnstileWidget(container: HTMLElement, callback?: (token: string) => void): void {
    container.innerHTML = `
      <div class="cf-turnstile" 
           data-sitekey="${this.config.siteKey}"
           data-theme="${this.config.theme}">
      </div>
    `;

    // Simulate Turnstile callback for demo
    if (callback) {
      setTimeout(() => {
        callback('turnstile-demo-token-' + Date.now());
      }, 1500);
    }
  }

  // Render custom widget
  private renderCustomWidget(container: HTMLElement, callback?: (token: string) => void): void {
    const challenge = this.getRandomChallenge();
    
    container.innerHTML = `
      <div class="custom-captcha bg-gray-800 border border-gray-600 rounded-lg p-4">
        <p class="text-white mb-3">${challenge.question}</p>
        <input type="text" id="captcha-answer" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white" placeholder="Your answer...">
        <button id="captcha-verify" class="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Verify</button>
      </div>
    `;

    const input = container.querySelector('#captcha-answer') as HTMLInputElement;
    const button = container.querySelector('#captcha-verify') as HTMLButtonElement;

    button.addEventListener('click', () => {
      const userAnswer = input.value.toLowerCase().trim();
      const isCorrect = userAnswer === challenge.answer.toLowerCase().trim();
      
      if (isCorrect && callback) {
        callback('custom-challenge-' + Date.now());
        container.innerHTML = '<div class="text-green-400">✓ Verified successfully!</div>';
      } else {
        input.style.borderColor = '#ef4444';
        input.placeholder = 'Incorrect, try again...';
        input.value = '';
      }
    });

    input.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        button.click();
      }
    });
  }

  // Load external providers
  private async loadExternalProviders(): Promise<void> {
    // This method can be used to preload multiple providers
    // Currently handled individually in initialize method
  }

  // Get current configuration
  public getConfig(): CaptchaConfig {
    return { ...this.config };
  }

  // Update configuration
  public updateConfig(config: Partial<CaptchaConfig>): void {
    this.config = { ...this.config, ...config };
    this.saveConfig();
  }

  // Save configuration
  private saveConfig(): void {
    try {
      localStorage.setItem('captcha_config', JSON.stringify(this.config));
    } catch (error) {
      console.error('Failed to save CAPTCHA config:', error);
    }
  }

  // Load configuration
  private loadConfig(): CaptchaConfig {
    try {
      const saved = localStorage.getItem('captcha_config');
      return saved ? { ...this.defaultConfig, ...JSON.parse(saved) } : this.defaultConfig;
    } catch (error) {
      console.error('Failed to load CAPTCHA config:', error);
      return this.defaultConfig;
    }
  }

  // Reset CAPTCHA widget
  public reset(containerId?: string): void {
    if (containerId) {
      const container = document.getElementById(containerId);
      if (container) {
        container.innerHTML = '';
      }
    }

    switch (this.config.provider) {
      case 'recaptcha':
        if (typeof window.grecaptcha !== 'undefined') {
          window.grecaptcha.reset();
        }
        break;
      case 'hcaptcha':
        if (typeof window.hcaptcha !== 'undefined') {
          window.hcaptcha.reset();
        }
        break;
      // Custom and Turnstile reset handled by clearing container
    }
  }
}

// Extend window interface for CAPTCHA providers
declare global {
  interface Window {
    grecaptcha: any;
    hcaptcha: any;
    turnstile: any;
  }
}

// Export singleton instance
export const captchaService = CaptchaService.getInstance();
