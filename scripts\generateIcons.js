// Simple icon generation script for PWA
// This creates placeholder icons based on the favicon

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create icons directory if it doesn't exist
const iconsDir = path.join(__dirname, '../public/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Icon sizes needed for PWA
const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Simple SVG template for icons
const createIconSVG = (size) => `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="${size}" height="${size}" rx="${size * 0.1}" fill="url(#grad)"/>
  <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${size * 0.4}" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="central">NB</text>
</svg>`;

// Generate icons
iconSizes.forEach(size => {
  const svgContent = createIconSVG(size);
  const filename = `icon-${size}x${size}.png`;
  const svgFilename = `icon-${size}x${size}.svg`;
  
  // Save as SVG (browsers can use SVG icons)
  fs.writeFileSync(path.join(iconsDir, svgFilename), svgContent);
  
  // Create a simple PNG placeholder (you can replace these with actual PNG files later)
  // For now, we'll create SVG files and update the manifest to use them
  console.log(`Generated ${svgFilename}`);
});

// Also create apple-touch-icon.png
const appleTouchIcon = createIconSVG(180);
fs.writeFileSync(path.join(__dirname, '../public/apple-touch-icon.svg'), appleTouchIcon);

console.log('Icon generation complete!');
console.log('Note: Generated SVG icons. For production, consider converting to PNG format.');
