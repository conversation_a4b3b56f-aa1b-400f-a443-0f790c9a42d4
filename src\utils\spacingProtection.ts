/**
 * Ultra Aggressive Spacing Protection System
 * Prevents notification popups from breaking text spacing
 */

class SpacingProtectionService {
  private observer: MutationObserver | null = null;
  private isActive = false;

  constructor() {
    this.init();
  }

  private init() {
    // Add spacing protection class to body immediately
    document.body.classList.add('spacing-protection');
    document.documentElement.classList.add('spacing-protection');

    // Force spacing on all elements immediately
    this.forceNormalSpacing();

    // Set up mutation observer to watch for DOM changes
    this.setupMutationObserver();

    // Set up interval to continuously enforce spacing
    this.setupContinuousProtection();

    // Listen for notification events
    this.setupNotificationListeners();
  }

  private forceNormalSpacing() {
    const allElements = document.querySelectorAll('*');
    allElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      htmlElement.style.setProperty('word-spacing', 'normal', 'important');
      htmlElement.style.setProperty('letter-spacing', 'normal', 'important');
      htmlElement.style.setProperty('white-space', 'normal', 'important');
    });
  }

  private setupMutationObserver() {
    this.observer = new MutationObserver((mutations) => {
      let needsUpdate = false;

      mutations.forEach((mutation) => {
        // Check for added nodes
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              needsUpdate = true;
            }
          });
        }

        // Check for attribute changes
        if (mutation.type === 'attributes') {
          const target = mutation.target as HTMLElement;
          if (mutation.attributeName === 'style' || 
              mutation.attributeName === 'class') {
            needsUpdate = true;
          }
        }
      });

      if (needsUpdate) {
        // Small delay to let other scripts finish
        setTimeout(() => {
          this.forceNormalSpacing();
        }, 10);
      }
    });

    this.observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['style', 'class']
    });
  }

  private setupContinuousProtection() {
    // Minimal protection - check every 500ms
    setInterval(() => {
      this.forceNormalSpacing();
    }, 500);

    // Only on resize
    window.addEventListener('resize', () => {
      setTimeout(() => {
        this.forceNormalSpacing();
      }, 200);
    });
  }

  private setupNotificationListeners() {
    // Listen for notification permission requests
    if ('Notification' in window) {
      const originalRequestPermission = Notification.requestPermission;
      Notification.requestPermission = async (...args) => {
        const result = await originalRequestPermission.apply(Notification, args);
        setTimeout(() => {
          this.forceNormalSpacing();
        }, 100);
        return result;
      };
    }

    // Listen for PWA install prompts
    window.addEventListener('beforeinstallprompt', () => {
      setTimeout(() => {
        this.forceNormalSpacing();
      }, 100);
    });

    // Listen for any modal/dialog events
    document.addEventListener('DOMNodeInserted', (event) => {
      const target = event.target as HTMLElement;
      if (target.nodeType === Node.ELEMENT_NODE) {
        const hasModalClasses = target.classList?.contains('modal') ||
                               target.classList?.contains('popup') ||
                               target.classList?.contains('notification') ||
                               target.classList?.contains('alert') ||
                               target.getAttribute('role') === 'dialog' ||
                               target.getAttribute('role') === 'alertdialog';
        
        if (hasModalClasses) {
          setTimeout(() => {
            this.forceNormalSpacing();
          }, 50);
        }
      }
    });
  }

  public activate() {
    if (!this.isActive) {
      this.isActive = true;
      this.forceNormalSpacing();
    }
  }

  public deactivate() {
    if (this.isActive) {
      this.isActive = false;
      if (this.observer) {
        this.observer.disconnect();
      }
    }
  }

  public forceUpdate() {
    this.forceNormalSpacing();
  }
}

// Create global instance
const spacingProtection = new SpacingProtectionService();

// Export for manual control if needed
export default spacingProtection;

// Simple activation
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    spacingProtection.activate();
  });
} else {
  spacingProtection.activate();
}
