// Advanced SEO Configuration for Maximum Search Engine Visibility

export const ADVANCED_SEO_CONFIG = {
  // Primary Domain Configuration
  domain: 'https://nuralbhardwaj.me',
  siteName: 'Nural Bhardwaj - Professional Developer Portfolio',
  
  // High-Impact Keywords for Top Rankings
  primaryKeywords: [
    'Nural Bhardwaj',
    'Full Stack Developer',
    'UI/UX Designer',
    'React Developer',
    'Node.js Developer',
    'TypeScript Developer',
    'JavaScript Developer',
    'Web Developer',
    'Frontend Developer',
    'Backend Developer',
    'Software Engineer'
  ],

  // Location-Based Keywords for Local SEO Dominance
  locationKeywords: [
    'Full Stack Developer India',
    'React Developer Gurugram',
    'Web Developer Haryana',
    'UI/UX Designer Delhi NCR',
    'JavaScript Developer India',
    'TypeScript Developer Gurugram',
    'Software Engineer Delhi',
    'Web Developer Delhi NCR',
    'Frontend Developer India',
    'Backend Developer Gurugram'
  ],

  // Service-Based Keywords for Business Growth
  serviceKeywords: [
    'Custom Web Development',
    'React Application Development',
    'E-commerce Website Development',
    'Mobile App Development',
    'API Development Services',
    'Database Design Services',
    'Cloud Computing Solutions',
    'DevOps Services',
    'Website Redesign Services',
    'Performance Optimization',
    'SEO Optimization Services',
    'Responsive Web Design',
    'Progressive Web Apps',
    'Single Page Applications',
    'Full Stack Development Services'
  ],

  // Technology Keywords for Technical SEO
  technologyKeywords: [
    'React.js Development',
    'Next.js Development',
    'Vue.js Development',
    'Angular Development',
    'Express.js Development',
    'MongoDB Development',
    'PostgreSQL Development',
    'AWS Cloud Services',
    'Docker Containerization',
    'Kubernetes Orchestration',
    'GraphQL API Development',
    'REST API Development',
    'Microservices Architecture',
    'Serverless Computing',
    'JAMstack Development'
  ],

  // Long-Tail Keywords for High Conversion
  longTailKeywords: [
    'Best Full Stack Developer for Hire',
    'Professional Web Development Services',
    'Custom React Application Development',
    'Experienced UI/UX Designer Portfolio',
    'Top Rated JavaScript Developer',
    'Expert TypeScript Development Services',
    'Modern Web Application Development',
    'Responsive Website Design Services',
    'Full Stack Development Consultant',
    'Senior Software Engineer Portfolio',
    'Freelance Full Stack Developer',
    'Remote Web Developer Services',
    'Enterprise Web Application Development',
    'Startup Web Development Services',
    'E-commerce Platform Development'
  ],

  // Industry-Specific Keywords
  industryKeywords: [
    'Fintech Developer',
    'E-commerce Developer',
    'SaaS Developer',
    'Startup Developer',
    'Enterprise Developer',
    'Healthcare Web Developer',
    'Education Platform Developer',
    'Real Estate Web Developer',
    'Travel Website Developer',
    'Food Delivery App Developer'
  ],

  // Page-Specific SEO Configuration
  pages: {
    home: {
      title: '🏆 Nural Bhardwaj - #1 Full Stack Developer & UI/UX Designer | 50+ Projects | Hire Now',
      description: '⭐ TOP-RATED Full Stack Developer with 5+ years experience. React, Node.js, TypeScript expert. 50+ successful projects delivered. 99% client satisfaction. Available for hire! 🚀 Get free consultation.',
      focusKeywords: ['Nural Bhardwaj', 'Full Stack Developer', 'UI/UX Designer', 'React Developer'],
      priority: 1.0,
      changeFreq: 'daily'
    },
    about: {
      title: 'About Nural Bhardwaj - Senior Full Stack Developer | 5+ Years Experience | 50+ Projects',
      description: 'Meet Nural Bhardwaj - Senior Full Stack Developer & UI/UX Designer with 5+ years experience. Specialized in React, Node.js, TypeScript. 50+ successful projects, 99% client satisfaction rate.',
      focusKeywords: ['About Nural Bhardwaj', 'Senior Full Stack Developer', 'Developer Experience'],
      priority: 0.9,
      changeFreq: 'weekly'
    },
    projects: {
      title: 'Projects Portfolio - 50+ Successful Web Applications | React, Node.js, TypeScript',
      description: 'Explore 50+ successful projects by Nural Bhardwaj. Full stack web applications, e-commerce platforms, mobile apps, and enterprise solutions. React, Node.js, TypeScript expertise.',
      focusKeywords: ['Web Development Projects', 'React Projects', 'Portfolio Projects'],
      priority: 0.95,
      changeFreq: 'weekly'
    },
    blog: {
      title: 'Tech Blog - Web Development Insights | React, Node.js, TypeScript Tutorials',
      description: 'Latest insights on web development, React tutorials, Node.js guides, TypeScript tips, and industry best practices. Learn from a senior full stack developer with 5+ years experience.',
      focusKeywords: ['Web Development Blog', 'React Tutorials', 'Programming Blog'],
      priority: 0.85,
      changeFreq: 'daily'
    },
    contact: {
      title: 'Hire Nural Bhardwaj - Full Stack Developer | Get Free Consultation | Quick Response',
      description: 'Ready to start your project? Contact Nural Bhardwaj for professional full stack development services. Free consultation, quick response, competitive rates. Available for freelance & contract work.',
      focusKeywords: ['Hire Full Stack Developer', 'Contact Nural Bhardwaj', 'Web Development Services'],
      priority: 0.9,
      changeFreq: 'monthly'
    },
    resume: {
      title: 'Resume - Nural Bhardwaj | Senior Full Stack Developer | Download CV PDF',
      description: 'Download Nural Bhardwaj\'s professional resume. Senior Full Stack Developer with 5+ years experience, 50+ projects completed. Skills: React, Node.js, TypeScript, and more.',
      focusKeywords: ['Nural Bhardwaj Resume', 'Full Stack Developer CV', 'Developer Resume'],
      priority: 0.8,
      changeFreq: 'monthly'
    }
  },

  // Social Media Optimization
  socialMedia: {
    twitter: {
      handle: '@nuralbhardwaj',
      cardType: 'summary_large_image'
    },
    linkedin: {
      profile: 'https://linkedin.com/in/nural-bhardwaj'
    },
    github: {
      profile: 'https://github.com/NuralBhardwaj'
    }
  },

  // Local SEO Configuration
  localSEO: {
    businessName: 'Nural Bhardwaj - Premium Full Stack Development & UI/UX Design Services',
    businessType: 'Software Development & Digital Design Consultant',
    address: {
      street: 'Sector 14',
      city: 'Gurugram',
      state: 'Haryana',
      zipCode: '122001',
      country: 'India'
    },
    contact: {
      phone: '+91-9876543210',
      email: '<EMAIL>'
    },
    hours: 'Mon-Fri 9AM-9PM IST, Sat 10AM-6PM IST',
    priceRange: '$$$',
    serviceArea: ['India', 'United States', 'United Kingdom', 'Canada', 'Australia']
  },

  // Technical SEO Settings
  technical: {
    enableLazyLoading: true,
    enableImageOptimization: true,
    enableCaching: true,
    enableCompression: true,
    enableMinification: true,
    enableCriticalCSS: true,
    enableServiceWorker: true,
    enableHTTP2Push: true
  },

  // Analytics Configuration
  analytics: {
    googleAnalytics: 'G-V4VD9F3LXK',
    googleSearchConsole: '', // TODO: Add your Google Search Console verification code
    bingWebmaster: '', // TODO: Add your Bing Webmaster verification code
    yandexWebmaster: '', // TODO: Add your Yandex Webmaster verification code
    hotjar: '', // TODO: Add your Hotjar site ID
    mixpanel: '', // TODO: Add your Mixpanel project token
    facebookPixel: '' // TODO: Add your Facebook Pixel ID
  },

  // Rich Snippets Configuration
  richSnippets: {
    enablePersonSchema: true,
    enableWebsiteSchema: true,
    enableLocalBusinessSchema: true,
    enableBreadcrumbSchema: true,
    enableReviewSchema: true,
    enableFAQSchema: true,
    enableHowToSchema: true
  },

  // Content Optimization Rules
  contentRules: {
    minTitleLength: 30,
    maxTitleLength: 60,
    minDescriptionLength: 120,
    maxDescriptionLength: 160,
    minKeywords: 10,
    maxKeywords: 50,
    keywordDensity: {
      min: 1,
      max: 3
    }
  },

  // Performance Targets
  performanceTargets: {
    firstContentfulPaint: 1.5, // seconds
    largestContentfulPaint: 2.5, // seconds
    firstInputDelay: 100, // milliseconds
    cumulativeLayoutShift: 0.1,
    timeToInteractive: 3.0 // seconds
  }
};

// SEO Scoring Algorithm
export const calculateSEOScore = (data: any): number => {
  let score = 0;
  const maxScore = 100;

  // Title optimization (20 points)
  if (data.title) {
    const titleLength = data.title.length;
    if (titleLength >= 30 && titleLength <= 60) {
      score += 20;
    } else if (titleLength >= 25 && titleLength <= 70) {
      score += 15;
    } else {
      score += 5;
    }
  }

  // Description optimization (20 points)
  if (data.description) {
    const descLength = data.description.length;
    if (descLength >= 120 && descLength <= 160) {
      score += 20;
    } else if (descLength >= 100 && descLength <= 180) {
      score += 15;
    } else {
      score += 5;
    }
  }

  // Keywords optimization (15 points)
  if (data.keywords && data.keywords.length >= 10) {
    score += 15;
  } else if (data.keywords && data.keywords.length >= 5) {
    score += 10;
  } else {
    score += 3;
  }

  // Technical SEO (15 points)
  if (data.canonical) score += 5;
  if (data.robots) score += 5;
  if (data.sitemap) score += 5;

  // Social Media (10 points)
  if (data.openGraph) score += 5;
  if (data.twitter) score += 5;

  // Structured Data (10 points)
  if (data.structuredData) score += 10;

  // Performance (10 points)
  if (data.performance) {
    const perfFeatures = Object.values(data.performance).filter(Boolean).length;
    score += Math.min(perfFeatures * 2, 10);
  }

  return Math.min(score, maxScore);
};

export default ADVANCED_SEO_CONFIG;
