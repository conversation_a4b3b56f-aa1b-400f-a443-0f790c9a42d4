// Advanced Security Monitoring and Protection Service
// Implements real-time threat detection, behavioral analysis, and automated response

export interface SecurityEvent {
  id: string;
  timestamp: number;
  type: 'threat_detected' | 'rate_limit' | 'suspicious_behavior' | 'blocked_request' | 'security_violation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  details: any;
  action_taken: string;
}

export interface BehaviorPattern {
  identifier: string;
  requests_per_minute: number;
  unique_endpoints: number;
  error_rate: number;
  suspicious_patterns: string[];
  risk_score: number;
}

export interface SecurityConfig {
  enable_real_time_monitoring: boolean;
  enable_behavioral_analysis: boolean;
  enable_geo_blocking: boolean;
  enable_honeypot: boolean;
  auto_block_threshold: number;
  alert_threshold: number;
  blocked_countries: string[];
  allowed_countries: string[];
}

class AdvancedSecurityService {
  private static instance: AdvancedSecurityService;
  private securityEvents: SecurityEvent[] = [];
  private behaviorPatterns: Map<string, BehaviorPattern> = new Map();
  private honeypotEndpoints: Set<string> = new Set([
    '/admin.php', '/wp-admin/', '/phpmyadmin/', '/administrator/',
    '/.env', '/config.php', '/database.sql', '/backup.zip'
  ]);
  
  private config: SecurityConfig = {
    enable_real_time_monitoring: true,
    enable_behavioral_analysis: true,
    enable_geo_blocking: false,
    enable_honeypot: true,
    auto_block_threshold: 8, // Risk score threshold for auto-blocking
    alert_threshold: 6, // Risk score threshold for alerts
    blocked_countries: ['CN', 'RU', 'KP'], // High-risk countries
    allowed_countries: [] // Empty means all allowed except blocked
  };

  private constructor() {
    this.initializeMonitoring();
  }

  public static getInstance(): AdvancedSecurityService {
    if (!AdvancedSecurityService.instance) {
      AdvancedSecurityService.instance = new AdvancedSecurityService();
    }
    return AdvancedSecurityService.instance;
  }

  // Initialize real-time monitoring
  private initializeMonitoring(): void {
    if (this.config.enable_real_time_monitoring) {
      this.startBehavioralAnalysis();
      this.setupHoneypots();
      console.log('🛡️ Advanced security monitoring initialized');
    }
  }

  // Analyze request behavior patterns
  public analyzeRequest(request: any): { risk_score: number; threats: string[] } {
    const identifier = this.getClientIdentifier(request);
    const threats: string[] = [];
    let riskScore = 0;

    // Update behavior pattern
    this.updateBehaviorPattern(identifier, request);
    const pattern = this.behaviorPatterns.get(identifier);

    if (!pattern) return { risk_score: 0, threats: [] };

    // High request rate
    if (pattern.requests_per_minute > 60) {
      threats.push('High request rate detected');
      riskScore += 3;
    }

    // Too many unique endpoints
    if (pattern.unique_endpoints > 20) {
      threats.push('Scanning behavior detected');
      riskScore += 4;
    }

    // High error rate
    if (pattern.error_rate > 0.5) {
      threats.push('High error rate - potential probing');
      riskScore += 2;
    }

    // Suspicious patterns
    if (pattern.suspicious_patterns.length > 0) {
      threats.push(`Suspicious patterns: ${pattern.suspicious_patterns.join(', ')}`);
      riskScore += pattern.suspicious_patterns.length * 2;
    }

    // Honeypot access
    if (this.isHoneypotAccess(request.url)) {
      threats.push('Honeypot access detected');
      riskScore += 8; // High risk score for honeypot access
    }

    // Geo-blocking check
    if (this.config.enable_geo_blocking) {
      const geoRisk = this.checkGeoLocation(request);
      if (geoRisk.blocked) {
        threats.push(`Blocked country access: ${geoRisk.country}`);
        riskScore += 5;
      }
    }

    pattern.risk_score = riskScore;
    return { risk_score: riskScore, threats };
  }

  // Update behavior pattern for a client
  private updateBehaviorPattern(identifier: string, request: any): void {
    const pattern = this.behaviorPatterns.get(identifier) || {
      identifier,
      requests_per_minute: 0,
      unique_endpoints: 0,
      error_rate: 0,
      suspicious_patterns: [],
      risk_score: 0
    };

    // Count requests in last minute
    const recentRequests = this.getRecentRequests(identifier, 60000);
    pattern.requests_per_minute = recentRequests.length;

    // Count unique endpoints
    const uniqueEndpoints = new Set(recentRequests.map(r => r.endpoint));
    pattern.unique_endpoints = uniqueEndpoints.size;

    // Calculate error rate
    const errorRequests = recentRequests.filter(r => r.status >= 400);
    pattern.error_rate = recentRequests.length > 0 ? errorRequests.length / recentRequests.length : 0;

    // Detect suspicious patterns
    pattern.suspicious_patterns = this.detectSuspiciousPatterns(request);

    this.behaviorPatterns.set(identifier, pattern);
  }

  // Detect suspicious patterns in request
  private detectSuspiciousPatterns(request: any): string[] {
    const patterns: string[] = [];
    const url = request.url || '';
    const userAgent = request.userAgent || '';
    const body = JSON.stringify(request.body || {});

    // Common attack patterns
    const attackPatterns = [
      { name: 'SQL Injection', regex: /(union|select|insert|delete|drop|create|alter|exec)/i },
      { name: 'XSS', regex: /<script|javascript:|on\w+=/i },
      { name: 'Path Traversal', regex: /\.\.\//g },
      { name: 'Command Injection', regex: /(\||&|;|\$\(|`)/g },
      { name: 'File Inclusion', regex: /(include|require|file_get_contents)/i },
      { name: 'LDAP Injection', regex: /(\*|\)|\(|\||&)/g }
    ];

    const testString = `${url} ${body}`;
    attackPatterns.forEach(pattern => {
      if (pattern.regex.test(testString)) {
        patterns.push(pattern.name);
      }
    });

    // Suspicious user agents
    const suspiciousAgents = [
      /bot/i, /crawler/i, /spider/i, /scraper/i, /scanner/i,
      /hack/i, /exploit/i, /injection/i, /sqlmap/i, /nikto/i,
      /nmap/i, /burp/i, /metasploit/i, /kali/i
    ];

    if (suspiciousAgents.some(agent => agent.test(userAgent))) {
      patterns.push('Suspicious User Agent');
    }

    return patterns;
  }

  // Check if request is accessing honeypot
  private isHoneypotAccess(url: string): boolean {
    if (!this.config.enable_honeypot) return false;
    return Array.from(this.honeypotEndpoints).some(endpoint => url.includes(endpoint));
  }

  // Geo-location based blocking
  private checkGeoLocation(request: any): { blocked: boolean; country?: string } {
    // This would integrate with a real geo-location service
    // For now, we'll simulate based on IP patterns
    const ip = request.ip || '';
    
    // Simulate geo-location detection
    const simulatedCountry = this.simulateCountryFromIP(ip);
    
    if (this.config.blocked_countries.includes(simulatedCountry)) {
      return { blocked: true, country: simulatedCountry };
    }

    if (this.config.allowed_countries.length > 0 && 
        !this.config.allowed_countries.includes(simulatedCountry)) {
      return { blocked: true, country: simulatedCountry };
    }

    return { blocked: false, country: simulatedCountry };
  }

  // Simulate country detection from IP (in real implementation, use MaxMind or similar)
  private simulateCountryFromIP(ip: string): string {
    // This is a simulation - in production, use a real geo-IP service
    const hash = ip.split('.').reduce((acc, octet) => acc + parseInt(octet), 0);
    const countries = ['US', 'CA', 'GB', 'DE', 'FR', 'JP', 'AU', 'CN', 'RU', 'IN'];
    return countries[hash % countries.length];
  }

  // Get recent requests for behavioral analysis
  private getRecentRequests(identifier: string, timeWindow: number): any[] {
    const now = Date.now();
    return this.securityEvents
      .filter(event => 
        event.source === identifier && 
        (now - event.timestamp) <= timeWindow
      )
      .map(event => event.details);
  }

  // Log security event
  public logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp'>): void {
    const securityEvent: SecurityEvent = {
      id: this.generateEventId(),
      timestamp: Date.now(),
      ...event
    };

    this.securityEvents.push(securityEvent);

    // Keep only last 1000 events
    if (this.securityEvents.length > 1000) {
      this.securityEvents = this.securityEvents.slice(-1000);
    }

    // Auto-response based on severity
    this.handleSecurityEvent(securityEvent);
  }

  // Handle security events with automated response
  private handleSecurityEvent(event: SecurityEvent): void {
    if (event.severity === 'critical' || event.severity === 'high') {
      console.warn('🚨 High severity security event:', event);
      
      // Auto-block for critical threats
      if (event.severity === 'critical') {
        this.autoBlockSource(event.source, 3600000); // 1 hour block
      }
    }

    // Send alerts for high-risk events
    if (event.severity === 'high' || event.severity === 'critical') {
      this.sendSecurityAlert(event);
    }
  }

  // Auto-block a source
  private autoBlockSource(source: string, duration: number): void {
    // This would integrate with your existing security service
    console.log(`🔒 Auto-blocking source ${source} for ${duration}ms`);
  }

  // Send security alert
  private sendSecurityAlert(event: SecurityEvent): void {
    // This would send alerts via email, webhook, etc.
    console.log('📧 Security alert sent:', event);
  }

  // Generate unique event ID
  private generateEventId(): string {
    return `sec_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  // Get client identifier
  private getClientIdentifier(request?: any): string {
    if (request?.ip) return request.ip;
    
    // Fallback to browser fingerprinting
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Security fingerprint', 2, 2);
    }
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|');
    
    return btoa(fingerprint).substring(0, 16);
  }

  // Start behavioral analysis monitoring
  private startBehavioralAnalysis(): void {
    if (!this.config.enable_behavioral_analysis) return;

    setInterval(() => {
      this.analyzeBehaviorPatterns();
    }, 30000); // Analyze every 30 seconds
  }

  // Analyze all behavior patterns
  private analyzeBehaviorPatterns(): void {
    this.behaviorPatterns.forEach((pattern, identifier) => {
      if (pattern.risk_score >= this.config.auto_block_threshold) {
        this.logSecurityEvent({
          type: 'suspicious_behavior',
          severity: 'high',
          source: identifier,
          details: pattern,
          action_taken: 'Auto-blocked due to high risk score'
        });
      } else if (pattern.risk_score >= this.config.alert_threshold) {
        this.logSecurityEvent({
          type: 'suspicious_behavior',
          severity: 'medium',
          source: identifier,
          details: pattern,
          action_taken: 'Alert generated for suspicious behavior'
        });
      }
    });
  }

  // Setup honeypot endpoints
  private setupHoneypots(): void {
    if (!this.config.enable_honeypot) return;
    
    // Monitor for honeypot access
    console.log('🍯 Honeypot endpoints active:', Array.from(this.honeypotEndpoints));
  }

  // Get security configuration
  public getConfig(): SecurityConfig {
    return { ...this.config };
  }

  // Update security configuration
  public updateConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('🔧 Security configuration updated');
  }

  // Get security events
  public getSecurityEvents(limit: number = 100): SecurityEvent[] {
    return this.securityEvents.slice(-limit);
  }

  // Get behavior patterns
  public getBehaviorPatterns(): BehaviorPattern[] {
    return Array.from(this.behaviorPatterns.values());
  }

  // Clear security data
  public clearSecurityData(): void {
    this.securityEvents = [];
    this.behaviorPatterns.clear();
    console.log('🗑️ Security data cleared');
  }
}

// Export singleton instance
export const advancedSecurityService = AdvancedSecurityService.getInstance();
