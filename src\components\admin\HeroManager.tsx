import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Home, 
  Save, 
  Eye, 
  Type, 
  Palette, 
  Sparkles,
  Play,
  RefreshCw,
  X
} from 'lucide-react';
import { cmsService } from '../../services/cmsService';
import toast from 'react-hot-toast';
import { SuccessToast, ErrorToast } from '../CustomToast';

interface HeroManagerProps {
  onDataChange: () => void;
}

interface HeroContent {
  greeting: string;
  name: string;
  surname: string;
  description: string;
  typingPhrases: string[];
  ctaButtons: {
    primary: string;
    secondary: string;
  };
  socialLinks: {
    github: string;
    linkedin: string;
    email: string;
  };
  backgroundSettings: {
    particleCount: number;
    snowflakeCount: number;
    enableAnimations: boolean;
  };
}

const HeroManager: React.FC<HeroManagerProps> = ({ onDataChange }) => {
  const [heroContent, setHeroContent] = useState<HeroContent>({
    greeting: '<PERSON>, <PERSON>\'m',
    name: '<PERSON><PERSON>',
    surname: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Crafting exceptional digital experiences through innovative full-stack development and user-centered design.',
    typingPhrases: [
      'Full Stack Developer',
      'UI/UX Designer', 
      'Creative Thinker',
      'Problem Solver'
    ],
    ctaButtons: {
      primary: 'View My Work',
      secondary: 'Download CV'
    },
    socialLinks: {
      github: 'https://github.com/NuralBhardwaj/',
      linkedin: 'https://www.linkedin.com/in/nural-bhardwaj/',
      email: '<EMAIL>'
    },
    backgroundSettings: {
      particleCount: 20,
      snowflakeCount: 23,
      enableAnimations: true
    }
  });

  const [newPhrase, setNewPhrase] = useState('');
  const [previewMode, setPreviewMode] = useState(false);

  useEffect(() => {
    loadHeroContent();
  }, []);

  const loadHeroContent = () => {
    // Load from localStorage or use defaults
    const saved = localStorage.getItem('hero_content');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setHeroContent({ ...heroContent, ...parsed });
      } catch (error) {
        console.error('Error loading hero content:', error);
      }
    }
  };

  const saveHeroContent = () => {
    try {
      localStorage.setItem('hero_content', JSON.stringify(heroContent));
      onDataChange();
      
      toast(() => (
        <SuccessToast
          message="Hero section updated successfully!"
          icon={<Save className="w-5 h-5 text-green-400" />}
        />
      ));
    } catch (error) {
      toast(() => (
        <ErrorToast
          message="Failed to save hero content"
          icon={<X className="w-5 h-5 text-red-400" />}
        />
      ));
    }
  };

  const addTypingPhrase = () => {
    if (newPhrase.trim() && !heroContent.typingPhrases.includes(newPhrase.trim())) {
      setHeroContent({
        ...heroContent,
        typingPhrases: [...heroContent.typingPhrases, newPhrase.trim()]
      });
      setNewPhrase('');
    }
  };

  const removeTypingPhrase = (phrase: string) => {
    setHeroContent({
      ...heroContent,
      typingPhrases: heroContent.typingPhrases.filter(p => p !== phrase)
    });
  };

  const resetToDefaults = () => {
    if (window.confirm('Are you sure you want to reset hero content to defaults?')) {
      localStorage.removeItem('hero_content');
      loadHeroContent();
      toast(() => (
        <SuccessToast
          message="Hero content reset to defaults!"
          icon={<RefreshCw className="w-5 h-5 text-green-400" />}
        />
      ));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Hero Section Management</h1>
          <p className="text-gray-400">Customize your homepage hero section content and appearance</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setPreviewMode(!previewMode)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-200 ${
              previewMode 
                ? 'bg-blue-600 text-white' 
                : 'bg-white/10 text-gray-300 hover:bg-white/20'
            }`}
          >
            <Eye className="w-4 h-4" />
            <span>{previewMode ? 'Edit Mode' : 'Preview'}</span>
          </button>
          <button
            onClick={resetToDefaults}
            className="flex items-center space-x-2 px-4 py-2 bg-orange-600/20 text-orange-400 rounded-xl hover:bg-orange-600/30 transition-all duration-200"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Reset</span>
          </button>
          <button
            onClick={saveHeroContent}
            className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-xl transition-all duration-200"
          >
            <Save className="w-4 h-4" />
            <span>Save Changes</span>
          </button>
        </div>
      </div>

      {previewMode ? (
        /* Preview Mode */
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-br from-gray-900 via-black to-gray-900 rounded-2xl p-8 border border-purple-500/30 min-h-[400px] flex items-center justify-center"
        >
          <div className="text-center space-y-6">
            <h1 className="text-4xl md:text-6xl font-black text-white">
              {heroContent.greeting}{' '}
              <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-cyan-400 bg-clip-text text-transparent">
                {heroContent.name}
              </span>
              <br />
              <span className="bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-400 bg-clip-text text-transparent">
                {heroContent.surname}
              </span>
            </h1>
            <div className="text-xl text-gray-300">
              A <span className="text-purple-400 font-bold">{heroContent.typingPhrases[0]}</span>
            </div>
            <p className="text-gray-400 max-w-2xl mx-auto">{heroContent.description}</p>
            <div className="flex space-x-4 justify-center">
              <button className="px-6 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 text-white rounded-xl">
                {heroContent.ctaButtons.primary}
              </button>
              <button className="px-6 py-3 border border-white/20 text-white rounded-xl">
                {heroContent.ctaButtons.secondary}
              </button>
            </div>
          </div>
        </motion.div>
      ) : (
        /* Edit Mode */
        <div className="grid lg:grid-cols-2 gap-6">
          {/* Basic Content */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
          >
            <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
              <Type className="w-5 h-5 text-purple-400" />
              <span>Basic Content</span>
            </h2>

            <div className="space-y-4">
              {/* Greeting */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Greeting Text</label>
                <input
                  type="text"
                  value={heroContent.greeting}
                  onChange={(e) => setHeroContent({ ...heroContent, greeting: e.target.value })}
                  className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                  placeholder="Hi, I'm"
                />
              </div>

              {/* Name */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">First Name</label>
                  <input
                    type="text"
                    value={heroContent.name}
                    onChange={(e) => setHeroContent({ ...heroContent, name: e.target.value })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Nural"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Last Name</label>
                  <input
                    type="text"
                    value={heroContent.surname}
                    onChange={(e) => setHeroContent({ ...heroContent, surname: e.target.value })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Bhardwaj"
                  />
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
                <textarea
                  value={heroContent.description}
                  onChange={(e) => setHeroContent({ ...heroContent, description: e.target.value })}
                  className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                  placeholder="Your professional description"
                  rows={3}
                />
              </div>

              {/* CTA Buttons */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Primary Button</label>
                  <input
                    type="text"
                    value={heroContent.ctaButtons.primary}
                    onChange={(e) => setHeroContent({ 
                      ...heroContent, 
                      ctaButtons: { ...heroContent.ctaButtons, primary: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="View My Work"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Secondary Button</label>
                  <input
                    type="text"
                    value={heroContent.ctaButtons.secondary}
                    onChange={(e) => setHeroContent({ 
                      ...heroContent, 
                      ctaButtons: { ...heroContent.ctaButtons, secondary: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Download CV"
                  />
                </div>
              </div>
            </div>
          </motion.div>

          {/* Typing Animation & Settings */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            {/* Typing Phrases */}
            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
                <Sparkles className="w-5 h-5 text-cyan-400" />
                <span>Typing Animation</span>
              </h2>

              <div className="space-y-4">
                {/* Add New Phrase */}
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newPhrase}
                    onChange={(e) => setNewPhrase(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addTypingPhrase()}
                    className="flex-1 px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Add typing phrase"
                  />
                  <button
                    onClick={addTypingPhrase}
                    className="px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-xl transition-all duration-200"
                  >
                    Add
                  </button>
                </div>

                {/* Current Phrases */}
                <div className="space-y-2">
                  {heroContent.typingPhrases.map((phrase, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-white/5 rounded-xl border border-white/10"
                    >
                      <span className="text-white">{phrase}</span>
                      <button
                        onClick={() => removeTypingPhrase(phrase)}
                        className="p-1 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded transition-all duration-200"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Background Settings */}
            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
                <Palette className="w-5 h-5 text-pink-400" />
                <span>Visual Settings</span>
              </h2>

              <div className="space-y-4">
                {/* Particle Count */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Floating Particles: {heroContent.backgroundSettings.particleCount}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="50"
                    value={heroContent.backgroundSettings.particleCount}
                    onChange={(e) => setHeroContent({
                      ...heroContent,
                      backgroundSettings: {
                        ...heroContent.backgroundSettings,
                        particleCount: parseInt(e.target.value)
                      }
                    })}
                    className="w-full"
                  />
                </div>

                {/* Snowflake Count */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Snowflakes: {heroContent.backgroundSettings.snowflakeCount}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="50"
                    value={heroContent.backgroundSettings.snowflakeCount}
                    onChange={(e) => setHeroContent({
                      ...heroContent,
                      backgroundSettings: {
                        ...heroContent.backgroundSettings,
                        snowflakeCount: parseInt(e.target.value)
                      }
                    })}
                    className="w-full"
                  />
                </div>

                {/* Enable Animations */}
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="enableAnimations"
                    checked={heroContent.backgroundSettings.enableAnimations}
                    onChange={(e) => setHeroContent({
                      ...heroContent,
                      backgroundSettings: {
                        ...heroContent.backgroundSettings,
                        enableAnimations: e.target.checked
                      }
                    })}
                    className="w-5 h-5 text-purple-600 bg-white/5 border-white/10 rounded focus:ring-purple-500/20 focus:ring-2"
                  />
                  <label htmlFor="enableAnimations" className="text-gray-300 font-medium">
                    Enable Background Animations
                  </label>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default HeroManager;
