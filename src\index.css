/* Import enhanced animations first */
@import './styles/enhanced-animations.css';

/* Tailwind CSS directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Comprehensive spacing fixes for the entire website */
.text-spacing-fix {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  white-space: pre-wrap !important;
}

/* Terminal text spacing fix */
.terminal-text {
  font-family: 'Courier New', monospace !important;
  white-space: pre !important;
  word-spacing: normal !important;
  letter-spacing: 0.05em !important;
  line-height: 1.2 !important;
}

/* Global spacing fixes to prevent text compression */
* {
  word-spacing: normal;
  letter-spacing: normal;
}

/* Prevent text spacing issues in all text elements */
p, span, div, h1, h2, h3, h4, h5, h6, a, button {
  word-spacing: normal !important;
  white-space: normal !important;
}

/* Special handling for flex containers */
.flex, .inline-flex {
  word-spacing: normal !important;
}

/* Ensure proper spacing in space-x and space-y utilities */
.space-x-1 > * + *, .space-x-2 > * + *, .space-x-3 > * + *, .space-x-4 > * + * {
  margin-left: var(--tw-space-x-reverse) !important;
}

.space-y-1 > * + *, .space-y-2 > * + *, .space-y-3 > * + *, .space-y-4 > * + * {
  margin-top: var(--tw-space-y-reverse) !important;
}

/* Admin picture background fix */
.admin-picture-bg {
  background-color: white !important;
  background-image: none !important;
}

/* Remove shine effect from admin picture */
.admin-picture-bg img {
  filter: brightness(1) contrast(1) !important;
  transition: transform 0.3s ease !important;
}

.admin-picture-bg img:hover {
  filter: brightness(1) contrast(1) !important;
  transform: scale(1.05) !important;
}

/* Comprehensive spacing reset to prevent text compression */
html, body {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Prevent Tailwind CSS from compressing text spacing */
.space-x-1 > :not([hidden]) ~ :not([hidden]),
.space-x-2 > :not([hidden]) ~ :not([hidden]),
.space-x-3 > :not([hidden]) ~ :not([hidden]),
.space-x-4 > :not([hidden]) ~ :not([hidden]),
.space-x-5 > :not([hidden]) ~ :not([hidden]),
.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0 !important;
  margin-right: calc(var(--tw-space-x) * var(--tw-space-x-reverse)) !important;
  margin-left: calc(var(--tw-space-x) * calc(1 - var(--tw-space-x-reverse))) !important;
}

.space-y-1 > :not([hidden]) ~ :not([hidden]),
.space-y-2 > :not([hidden]) ~ :not([hidden]),
.space-y-3 > :not([hidden]) ~ :not([hidden]),
.space-y-4 > :not([hidden]) ~ :not([hidden]),
.space-y-5 > :not([hidden]) ~ :not([hidden]),
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0 !important;
  margin-top: calc(var(--tw-space-y) * calc(1 - var(--tw-space-y-reverse))) !important;
  margin-bottom: calc(var(--tw-space-y) * var(--tw-space-y-reverse)) !important;
}

/* Force normal spacing on all text elements */
h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, textarea {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Specific fixes for common problematic elements */
.text-xl, .text-2xl, .text-3xl, .text-4xl, .text-5xl {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Fix for flex containers that might compress text */
.flex > *, .inline-flex > * {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Comprehensive spacing fixes for all components - More robust */
.space-x-1 > * + * {
  margin-left: 0.25rem !important;
}

.space-x-2 > * + * {
  margin-left: 0.5rem !important;
}

.space-x-3 > * + * {
  margin-left: 0.75rem !important;
}

.space-x-4 > * + * {
  margin-left: 1rem !important;
}

.space-x-6 > * + * {
  margin-left: 1.5rem !important;
}

.space-x-8 > * + * {
  margin-left: 2rem !important;
}

/* Force spacing even when notifications appear */
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  margin-left: 0.25rem !important;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  margin-left: 0.5rem !important;
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  margin-left: 0.75rem !important;
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  margin-left: 1rem !important;
}

.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  margin-left: 1.5rem !important;
}

.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  margin-left: 2rem !important;
}

/* Fix for project and blog cards */
.project-card *, .blog-card * {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  white-space: normal !important;
}

/* Fix for navbar items */
.navbar-item {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Fix for hero section text */
.hero-text {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Force normal spacing on all interactive elements */
button, a, input, textarea, select {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Fix for card content */
.card-content {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  line-height: 1.5 !important;
}

/* Prevent spacing issues when notifications appear */
body.notification-active *,
body:has(.notification-popup) *,
body:has([role="dialog"]) *,
body:has(.modal) *,
body:has(.popup) * {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Force spacing preservation during any modal/popup state */
.notification-popup ~ * {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Specific fixes for when PWA notification appears */
body:has(.pwa-notification) *,
body:has(.notification-banner) *,
body:has(.alert) * {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Force spacing preservation during state changes */
.navbar-item, .nav-link, .menu-item {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  white-space: nowrap !important;
}

/* Robust spacing for all interactive elements */
button span, a span, .button-text, .link-text {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  display: inline-block !important;
}

/* Prevent CSS resets from affecting spacing */
* {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Override any dynamic CSS changes */
[style*="word-spacing"], [style*="letter-spacing"] {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Specific fixes for common problematic areas */
.flex > span, .inline-flex > span {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  margin-right: 0.25rem !important;
}

/* Toast notification spacing fix */
.toast-container * {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Enhanced notification popup fixes */
body:has(.notification-popup) .text-spacing-fix,
body:has(.notification-popup) h1,
body:has(.notification-popup) h2,
body:has(.notification-popup) h3,
body:has(.notification-popup) .hero-text,
body:has(.notification-popup) .blog-title,
body:has(.notification-popup) .section-title {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Specific fixes for the problematic text */
body:has([role="alert"]) .text-spacing-fix,
body:has([role="alert"]) h1,
body:has([role="alert"]) h2,
body:has([role="alert"]) h3,
body:has([role="alert"]) .hero-text,
body:has([role="alert"]) .blog-title,
body:has([role="alert"]) .section-title {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Toast notification fixes */
body:has(.Toastify__toast) .text-spacing-fix,
body:has(.Toastify__toast) h1,
body:has(.Toastify__toast) h2,
body:has(.Toastify__toast) h3,
body:has(.Toastify__toast) .hero-text,
body:has(.Toastify__toast) .blog-title,
body:has(.Toastify__toast) .section-title {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Universal notification protection */
body:has([class*="toast"]) *,
body:has([class*="notification"]) *,
body:has([class*="alert"]) *,
body:has([class*="popup"]) *,
body:has([class*="Toastify"]) * {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Force normal spacing when any overlay is present */
body:has([role="dialog"]) *,
body:has([role="alert"]) *,
body:has([role="alertdialog"]) *,
body:has([aria-live]) * {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Specific protection for problematic elements */
.section-title,
.hero-text,
.blog-title,
.blog-main-title,
.text-spacing-fix {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  white-space: normal !important;
}

/* Extra protection for blog main title */
.blog-main-title,
.blog-main-title *,
.blog-main-title span,
.latest-insights-title,
.latest-insights-title *,
.latest-insights-title span,
.latest-insights-word {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  white-space: normal !important;
}

/* Nuclear protection for Latest Insights specifically */
body .latest-insights-title,
body .latest-insights-title *,
body .latest-insights-title span,
body .latest-insights-word,
html .latest-insights-title,
html .latest-insights-title *,
html .latest-insights-title span,
html .latest-insights-word {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  white-space: normal !important;
}

/* Protection during any notification state */
body:has([class*="toast"]) .latest-insights-title,
body:has([class*="toast"]) .latest-insights-title *,
body:has([class*="toast"]) .latest-insights-word,
body:has([class*="notification"]) .latest-insights-title,
body:has([class*="notification"]) .latest-insights-title *,
body:has([class*="notification"]) .latest-insights-word,
body:has([class*="alert"]) .latest-insights-title,
body:has([class*="alert"]) .latest-insights-title *,
body:has([class*="alert"]) .latest-insights-word {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  white-space: normal !important;
}

/* Minimal spacing protection for performance */

/* Force spacing on notification states */
body:has([role="dialog"]) *,
body:has([role="alertdialog"]) *,
body:has([role="banner"]) *,
body:has(.notification) *,
body:has(.toast) *,
body:has(.alert) *,
body:has(.modal) *,
body:has(.popup) * {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  white-space: normal !important;
}

/* Simple spacing protection */
.text-spacing-fix, .card-content {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Terminal ASCII Art Optimization */
.terminal-text {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  white-space: pre !important;
  font-variant-ligatures: none !important;
  text-rendering: optimizeSpeed !important;
  overflow: hidden !important;
  width: 100% !important;
  min-width: 520px !important;
  max-width: 520px !important;
  font-family: 'Courier New', monospace !important;
  font-size: 11px !important;
  line-height: 1.0 !important;
}

/* Ensure ASCII art container is wide enough */
.terminal-text::-webkit-scrollbar {
  height: 4px;
}

.terminal-text::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
}

.terminal-text::-webkit-scrollbar-thumb {
  background: rgba(34, 197, 94, 0.5);
  border-radius: 2px;
}

.terminal-text::-webkit-scrollbar-thumb:hover {
  background: rgba(34, 197, 94, 0.8);
}

/* Ultra Advanced Custom Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(2deg); }
}

@keyframes float-slow {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-30px) rotate(3deg); }
}

@keyframes float-reverse {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(20px) rotate(-2deg); }
}

@keyframes bounce-slow {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-15px) scale(1.05); }
}

@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes gradient-x {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes gradient-y {
  0%, 100% { background-position: 50% 0%; }
  50% { background-position: 50% 100%; }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fade-in-down {
  from {
    opacity: 0;
    transform: translateY(-40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes skill-fill {
  from { 
    transform: scaleX(0);
    opacity: 0;
  }
  to { 
    transform: scaleX(1);
    opacity: 1;
  }
}

@keyframes shine {
  0% { transform: translateX(-100%) skewX(-15deg); }
  100% { transform: translateX(200%) skewX(-15deg); }
}

@keyframes glow-pulse {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 40px rgba(139, 92, 246, 0.6);
    transform: scale(1.02);
  }
}

@keyframes text-glow {
  0%, 100% { text-shadow: 0 0 20px rgba(139, 92, 246, 0.5); }
  50% { text-shadow: 0 0 40px rgba(139, 92, 246, 0.8); }
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes scroll-indicator {
  0% { transform: translateY(0); opacity: 1; }
  100% { transform: translateY(20px); opacity: 0; }
}

@keyframes pulse-slow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

@keyframes wiggle {
  0%, 7%, 100% { transform: rotate(0deg); }
  15% { transform: rotate(-3deg); }
  20% { transform: rotate(3deg); }
}

@keyframes heartbeat {
  0%, 50%, 100% { transform: scale(1); }
  25%, 75% { transform: scale(1.1); }
}

/* Apply Ultra Advanced Animations */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 8s ease-in-out infinite;
}

.animate-float-reverse {
  animation: float-reverse 7s ease-in-out infinite;
}

.animate-bounce-slow {
  animation: bounce-slow 4s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradient-x 3s ease infinite;
}

.animate-gradient-y {
  background-size: 200% 200%;
  animation: gradient-y 3s ease infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slideUp {
  animation: slideUp 0.6s ease-out forwards;
}

.animate-fade-in-down {
  animation: fade-in-down 0.8s ease-out forwards;
}

.animate-slide-in-left {
  animation: slide-in-left 0.6s ease-out forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.6s ease-out forwards;
}

.animate-blink {
  animation: blink 1s infinite;
}

.animate-skill-fill {
  animation: skill-fill 2s ease-out forwards;
  transform-origin: left;
}

.animate-shine {
  animation: shine 2s infinite;
}

.animate-glow-pulse {
  animation: glow-pulse 2s ease-in-out infinite;
}

.animate-text-glow {
  animation: text-glow 2s ease-in-out infinite;
}

.animate-scroll-indicator {
  animation: scroll-indicator 2s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 3s ease-in-out infinite;
}

.animate-wiggle {
  animation: wiggle 1s ease-in-out;
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

/* Ultra Advanced 3D Transform Utilities */
.perspective-1000 {
  perspective: 1000px;
}

.perspective-2000 {
  perspective: 2000px;
}

.perspective-3000 {
  perspective: 3000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.rotate-y-12 {
  transform: rotateY(12deg);
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

.rotate-x-12 {
  transform: rotateX(12deg);
}

/* Ultra Modern 3D Effects */
@keyframes float-3d {
  0%, 100% {
    transform: translateY(0px) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
  }
  25% {
    transform: translateY(-20px) rotateX(5deg) rotateY(5deg) rotateZ(2deg);
  }
  50% {
    transform: translateY(-30px) rotateX(0deg) rotateY(10deg) rotateZ(0deg);
  }
  75% {
    transform: translateY(-20px) rotateX(-5deg) rotateY(5deg) rotateZ(-2deg);
  }
}

@keyframes morph-3d {
  0%, 100% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    transform: rotate(0deg) scale(1);
  }
  25% {
    border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
    transform: rotate(90deg) scale(1.1);
  }
  50% {
    border-radius: 50% 50% 25% 75% / 25% 75% 25% 75%;
    transform: rotate(180deg) scale(0.9);
  }
  75% {
    border-radius: 25% 75% 50% 50% / 75% 25% 75% 25%;
    transform: rotate(270deg) scale(1.05);
  }
}

@keyframes hologram {
  0%, 100% {
    opacity: 0.8;
    filter: hue-rotate(0deg) brightness(1.2);
    transform: translateZ(0px);
  }
  25% {
    opacity: 0.6;
    filter: hue-rotate(90deg) brightness(1.5);
    transform: translateZ(10px);
  }
  50% {
    opacity: 0.9;
    filter: hue-rotate(180deg) brightness(1.1);
    transform: translateZ(20px);
  }
  75% {
    opacity: 0.7;
    filter: hue-rotate(270deg) brightness(1.4);
    transform: translateZ(10px);
  }
}

@keyframes neon-pulse {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(139, 92, 246, 0.5),
      0 0 40px rgba(139, 92, 246, 0.3),
      0 0 60px rgba(139, 92, 246, 0.1),
      inset 0 0 20px rgba(139, 92, 246, 0.1);
  }
  50% {
    box-shadow:
      0 0 30px rgba(139, 92, 246, 0.8),
      0 0 60px rgba(139, 92, 246, 0.6),
      0 0 90px rgba(139, 92, 246, 0.4),
      inset 0 0 30px rgba(139, 92, 246, 0.2);
  }
}

@keyframes matrix-rain {
  0% { transform: translateY(-100vh) rotateX(0deg); }
  100% { transform: translateY(100vh) rotateX(360deg); }
}

@keyframes cyber-glitch {
  0%, 100% {
    transform: translate(0);
    filter: hue-rotate(0deg);
  }
  10% {
    transform: translate(-2px, 2px);
    filter: hue-rotate(90deg);
  }
  20% {
    transform: translate(-4px, -2px);
    filter: hue-rotate(180deg);
  }
  30% {
    transform: translate(4px, 2px);
    filter: hue-rotate(270deg);
  }
  40% {
    transform: translate(-2px, -4px);
    filter: hue-rotate(360deg);
  }
  50% {
    transform: translate(2px, 4px);
    filter: hue-rotate(90deg);
  }
  60% {
    transform: translate(-4px, 2px);
    filter: hue-rotate(180deg);
  }
  70% {
    transform: translate(4px, -2px);
    filter: hue-rotate(270deg);
  }
  80% {
    transform: translate(-2px, 4px);
    filter: hue-rotate(360deg);
  }
  90% {
    transform: translate(2px, -4px);
    filter: hue-rotate(90deg);
  }
}

.animate-float-3d {
  animation: float-3d 8s ease-in-out infinite;
}

.animate-morph-3d {
  animation: morph-3d 10s ease-in-out infinite;
}

.animate-hologram {
  animation: hologram 4s ease-in-out infinite;
}

.animate-neon-pulse {
  animation: neon-pulse 2s ease-in-out infinite;
}

.animate-matrix-rain {
  animation: matrix-rain 3s linear infinite;
}

.animate-cyber-glitch {
  animation: cyber-glitch 0.3s ease-in-out infinite;
}

/* Ultra Modern Glassmorphism */
.glass-morphism {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-morphism-strong {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glass-morphism-purple {
  background: rgba(139, 92, 246, 0.1);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(139, 92, 246, 0.2);
  box-shadow:
    0 10px 35px rgba(139, 92, 246, 0.3),
    inset 0 1px 0 rgba(139, 92, 246, 0.1);
}

/* Ultra Modern Neumorphism */
.neumorphism-dark {
  background: #1a1a1a;
  box-shadow:
    20px 20px 40px #0d0d0d,
    -20px -20px 40px #272727,
    inset 5px 5px 10px rgba(255, 255, 255, 0.05),
    inset -5px -5px 10px rgba(0, 0, 0, 0.5);
}

.neumorphism-inset {
  background: #1a1a1a;
  box-shadow:
    inset 20px 20px 40px #0d0d0d,
    inset -20px -20px 40px #272727,
    5px 5px 10px rgba(0, 0, 0, 0.3),
    -5px -5px 10px rgba(255, 255, 255, 0.05);
}

.neumorphism-purple {
  background: linear-gradient(145deg, #8b5cf6, #7c3aed);
  box-shadow:
    20px 20px 40px #6d28d9,
    -20px -20px 40px #a855f7,
    inset 5px 5px 10px rgba(255, 255, 255, 0.1),
    inset -5px -5px 10px rgba(0, 0, 0, 0.3);
}

/* Ultra Modern Holographic Effects */
.holographic {
  background: linear-gradient(
    45deg,
    #ff006e,
    #8338ec,
    #3a86ff,
    #06ffa5,
    #ffbe0b,
    #fb5607
  );
  background-size: 300% 300%;
  animation: holographic-shift 3s ease infinite;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@keyframes holographic-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.holographic-border {
  position: relative;
  background: rgba(0, 0, 0, 0.8);
}

.holographic-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(
    45deg,
    #ff006e,
    #8338ec,
    #3a86ff,
    #06ffa5,
    #ffbe0b,
    #fb5607
  );
  background-size: 300% 300%;
  animation: holographic-shift 3s ease infinite;
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
}

/* Ultra Modern Cyber Grid */
.cyber-grid {
  background-image:
    linear-gradient(rgba(139, 92, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(139, 92, 246, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: cyber-grid-move 20s linear infinite;
}

@keyframes cyber-grid-move {
  0% { background-position: 0 0; }
  100% { background-position: 50px 50px; }
}

/* Ultra Modern Particle Effects */
.particle-field {
  position: relative;
  overflow: hidden;
}

.particle-field::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(139, 92, 246, 0.8), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(6, 255, 165, 0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255, 0, 110, 0.8), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(131, 56, 236, 0.8), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(58, 134, 255, 0.8), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: particle-float 15s linear infinite;
  pointer-events: none;
}

@keyframes particle-float {
  0% { transform: translateY(100vh) rotate(0deg); }
  100% { transform: translateY(-100vh) rotate(360deg); }
}

.transform-gpu {
  transform: translateZ(0);
  will-change: transform;
}

/* Ultra Smooth Scrolling */
html {
  scroll-behavior: smooth;
  scroll-padding-top: 120px;
}

/* Better Section Spacing */
section {
  scroll-margin-top: 120px;
  position: relative;
}

/* Ensure proper section transitions */
#home, #about, #skills, #projects, #blog, #resume, #contact {
  scroll-margin-top: 100px;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

/* Fix text spacing issues */
* {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Ensure proper text spacing */
p, h1, h2, h3, h4, h5, h6, span, div, a, button {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  white-space: normal !important;
}

/* Fix specific text rendering issues */
.text-spacing-fix {
  word-spacing: 0.25em !important;
  letter-spacing: 0.02em !important;
}

/* Prevent text compression */
.no-text-compress {
  font-feature-settings: normal !important;
  font-variant-ligatures: normal !important;
  text-rendering: optimizeLegibility !important;
}

/* Fix text wrapping and spacing */
.text-wrap {
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Ensure proper line height for readability */
p {
  line-height: 1.6;
  margin-bottom: 1rem;
}

h1, h2, h3, h4, h5, h6 {
  line-height: 1.2;
  margin-bottom: 0.5rem;
}

/* Fix potential text compression issues */
.no-compress {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

/* Ensure proper spacing between inline elements */
span + span,
a + a,
button + button {
  margin-left: 0.25rem !important;
}

/* Fix text spacing in specific components */
.hero-title,
.section-title,
.card-title,
.nav-link,
.button-text {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  white-space: normal !important;
}

/* Fix text spacing in paragraphs and descriptions */
.description-text,
.content-text,
.body-text {
  word-spacing: 0.1em !important;
  letter-spacing: 0.01em !important;
  line-height: 1.6 !important;
}

/* Ensure proper text rendering */
body, html {
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Fix Blog and Projects section text issues */
.blog-card, .project-card {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

.blog-card h3, .project-card h3 {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  white-space: normal !important;
  line-height: 1.3 !important;
  margin-bottom: 0.75rem !important;
}

.blog-card p, .project-card p {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  white-space: normal !important;
  line-height: 1.5 !important;
  margin-bottom: 1rem !important;
}

/* Fix text overlapping in cards */
.blog-card .text-overlay-fix,
.project-card .text-overlay-fix {
  position: relative !important;
  z-index: 10 !important;
  background: transparent !important;
}

/* Ensure proper spacing in modal content */
.modal-content {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  line-height: 1.6 !important;
}

/* Fix text positioning in overlays */
.absolute.inset-0 {
  z-index: 1 !important;
}

.absolute.inset-0 + * {
  position: relative !important;
  z-index: 10 !important;
}

/* Specific fixes for blog and project card text overlapping */
.blog-card .relative,
.project-card .relative {
  z-index: 10 !important;
}

.blog-card .absolute,
.project-card .absolute {
  z-index: 1 !important;
}

.blog-card .absolute.top-4,
.project-card .absolute.top-4 {
  z-index: 20 !important;
}

/* Fix text in card content areas */
.blog-card > div:last-child,
.project-card > div:last-child {
  position: relative !important;
  z-index: 15 !important;
  background: transparent !important;
}

/* Ensure proper text rendering in cards */
.blog-card h3,
.project-card h3,
.blog-card p,
.project-card p {
  text-shadow: none !important;
  background: transparent !important;
  position: relative !important;
  z-index: 20 !important;
}

/* Fix specific text overlapping issues */
.line-clamp-2,
.line-clamp-3 {
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
  word-break: break-word !important;
}

.line-clamp-3 {
  -webkit-line-clamp: 3 !important;
  line-clamp: 3 !important;
}

/* Fix gradient text issues */
.group-hover\:text-transparent:hover {
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
}

/* Enhanced card animations and effects */
.blog-card, .project-card, .skill-card {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
  will-change: transform, box-shadow !important;
}

.blog-card:hover, .project-card:hover, .skill-card:hover {
  transform: translateY(-12px) scale(1.03) !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5),
              0 0 0 1px rgba(255, 255, 255, 0.1),
              0 0 50px rgba(147, 51, 234, 0.2) !important;
}

/* Skills card specific fixes */
.skill-card {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

.skill-card h4, .skill-card p {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  line-height: 1.5 !important;
  margin-bottom: 1rem !important;
}

/* Global text spacing fixes */
* {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Specific text element fixes */
h1, h2, h3, h4, h5, h6 {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  line-height: 1.2 !important;
}

p, span, div {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  line-height: 1.5 !important;
}

/* Fix text in all components */
.hero-section *,
.about-section *,
.skills-section *,
.projects-section *,
.blog-section *,
.contact-section * {
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Ensure proper text rendering */
.text-spacing-fix {
  word-spacing: normal !important;
  letter-spacing: normal !important;
  white-space: normal !important;
  line-height: inherit !important;
}

/* Fix gradient text issues */
.bg-clip-text {
  -webkit-background-clip: text !important;
  background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  word-spacing: normal !important;
  letter-spacing: normal !important;
}

/* Fix background overlap issues for cards */
.blog-card, .project-card {
  position: relative !important;
  z-index: 20 !important;
  isolation: isolate !important;
  background: rgba(0, 0, 0, 0.95) !important;
  backdrop-filter: blur(32px) saturate(180%) !important;
  -webkit-backdrop-filter: blur(32px) saturate(180%) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8),
              inset 0 1px 0 rgba(255, 255, 255, 0.15),
              0 0 0 1px rgba(0, 0, 0, 0.05) !important;
}

.blog-card::before, .project-card::before {
  content: '' !important;
  position: absolute !important;
  inset: 0 !important;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.98) 0%, rgba(17, 24, 39, 0.98) 30%, rgba(31, 41, 55, 0.95) 70%, rgba(0, 0, 0, 0.98) 100%) !important;
  border-radius: inherit !important;
  z-index: -1 !important;
  backdrop-filter: blur(24px) !important;
  -webkit-backdrop-filter: blur(24px) !important;
}

/* Ensure proper stacking context */
.blog-card > *, .project-card > * {
  position: relative !important;
  z-index: 2 !important;
}

/* Fix image container z-index */
.blog-card img, .project-card img {
  position: relative !important;
  z-index: 1 !important;
}

/* Enhanced card borders and shadows */
.blog-card:hover, .project-card:hover {
  border-color: rgba(147, 51, 234, 0.5) !important;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 0 50px rgba(147, 51, 234, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

/* Prevent background bleed-through */
.blog-card .absolute, .project-card .absolute {
  z-index: 1 !important;
}

.blog-card .absolute.inset-0, .project-card .absolute.inset-0 {
  z-index: 0 !important;
}

/* Enhanced backdrop blur effects */
.backdrop-blur-xl {
  backdrop-filter: blur(24px) !important;
  -webkit-backdrop-filter: blur(24px) !important;
}

/* Improved gradient overlays */
.bg-gradient-to-br {
  background-attachment: fixed !important;
}

/* Enhanced button hover effects */
.group\/btn:hover .transform {
  transform: translateX(4px) !important;
}

/* Smooth image scaling */
.group:hover img {
  transform: scale(1.1) !important;
  filter: brightness(1.1) contrast(1.05) !important;
}

/* Enhanced glow effects */
.hover\:shadow-purple-500\/20:hover {
  box-shadow: 0 25px 50px -12px rgba(147, 51, 234, 0.2) !important;
}

.hover\:shadow-cyan-500\/20:hover {
  box-shadow: 0 25px 50px -12px rgba(6, 182, 212, 0.2) !important;
}

/* Fix section overlapping issues */
.section-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  z-index: 1;
}

/* Ultra Advanced Custom Scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: linear-gradient(180deg, #111827, #1f2937);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #a855f7, #06b6d4);
  border-radius: 6px;
  border: 2px solid #1f2937;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #9333ea, #0891b2);
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

::-webkit-scrollbar-corner {
  background: #1f2937;
}

/* Ultra Advanced Selection Styling */
::selection {
  background: linear-gradient(135deg, #a855f7, #06b6d4);
  color: white;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

::-moz-selection {
  background: linear-gradient(135deg, #a855f7, #06b6d4);
  color: white;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Ultra Advanced Focus Styles */
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: none;
  box-shadow: 
    0 0 0 2px rgba(139, 92, 246, 0.5),
    0 0 20px rgba(139, 92, 246, 0.3),
    inset 0 0 20px rgba(139, 92, 246, 0.1);
  transform: scale(1.02);
  transition: all 0.3s ease;
}

/* Ultra Advanced Backdrop Blur Effects */
.backdrop-blur-xs {
  backdrop-filter: blur(2px);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(12px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(16px);
}

.backdrop-blur-xl {
  backdrop-filter: blur(24px);
}

.backdrop-blur-2xl {
  backdrop-filter: blur(40px);
}

.backdrop-blur-3xl {
  backdrop-filter: blur(64px);
}

/* Ultra Advanced Glass Morphism Effects */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Ultra Advanced Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #a855f7, #06b6d4, #ec4899);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-x 3s ease infinite;
}

.gradient-text-static {
  background: linear-gradient(135deg, #a855f7, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Ultra Advanced Button Effects */
.btn-gradient {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #a855f7, #06b6d4);
  transition: all 0.3s ease;
}

.btn-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.btn-gradient:hover::before {
  left: 100%;
}

.btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 10px 30px rgba(139, 92, 246, 0.4),
    0 0 40px rgba(139, 92, 246, 0.2);
}

/* Ultra Advanced Loading Animations */
@keyframes spin-fast {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

.animate-spin-fast {
  animation: spin-fast 0.8s linear infinite;
}

.animate-pulse-ring {
  animation: pulse-ring 1.5s ease-out infinite;
}

/* Ultra Advanced Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 40px rgba(139, 92, 246, 0.2);
}

/* Enhanced Modal Styles */
.modal-backdrop {
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  background: rgba(0, 0, 0, 0.95) !important;
  z-index: 9999;
}

.modal-content {
  backdrop-filter: blur(40px) saturate(180%);
  -webkit-backdrop-filter: blur(40px) saturate(180%);
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(17, 24, 39, 0.95) 50%, rgba(0, 0, 0, 0.95) 100%) !important;
  border: 2px solid rgba(147, 51, 234, 0.3) !important;
  box-shadow:
    0 25px 50px -12px rgba(147, 51, 234, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0 0 1px rgba(0, 0, 0, 0.05) !important;
}

/* Enhanced Project Card Hover Effects */
.project-card:hover .absolute.inset-0 {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Enhanced Modal Animations */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(25px);
  }
}

@keyframes modalSlideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-modalFadeIn {
  animation: modalFadeIn 0.4s ease-out forwards;
}

.animate-modalSlideUp {
  animation: modalSlideUp 0.5s ease-out forwards;
}

/* Enhanced Button Hover Effects */
.btn-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover-lift:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 
    0 0 30px rgba(139, 92, 246, 0.6),
    0 0 60px rgba(139, 92, 246, 0.4),
    inset 0 0 20px rgba(139, 92, 246, 0.1);
}

/* Ultra Advanced Typography */
.text-shadow-sm {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.text-shadow-md {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
}

.text-shadow-lg {
  text-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
}

.text-shadow-glow {
  text-shadow: 
    0 0 10px rgba(139, 92, 246, 0.8),
    0 0 20px rgba(139, 92, 246, 0.6),
    0 0 40px rgba(139, 92, 246, 0.4);
}

/* Ultra Advanced Responsive Utilities */
@media (max-width: 640px) {
  .animate-float,
  .animate-float-slow,
  .animate-float-reverse {
    animation-duration: 4s;
  }
  
  .text-6xl,
  .text-7xl,
  .text-8xl,
  .text-9xl {
    line-height: 1.1;
  }
}

/* Ultra Advanced Performance Optimizations */
* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
}

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-scroll {
  will-change: scroll-position;
}

/* Ultra Advanced Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}

/* Ultra Advanced Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Ultra Advanced High Contrast Support */
@media (prefers-contrast: high) {
  .text-gray-400 {
    color: #d1d5db;
  }

  .border-white\/10 {
    border-color: rgba(255, 255, 255, 0.3);
  }
}

/* Terminal ASCII Art Optimization - MUCH LARGER AND CLEARER */
.terminal-text {
  font-feature-settings: "liga" 0;
  -webkit-font-feature-settings: "liga" 0;
  text-rendering: optimizeSpeed;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  color: #22c55e !important;
  text-shadow: 0 0 20px rgba(34, 197, 94, 1), 0 0 40px rgba(34, 197, 94, 0.8) !important;
  font-weight: 900 !important;
}

/* REMOVED PROBLEMATIC CSS - USING COMPONENT-BASED RESPONSIVE DESIGN */

/* Enhanced Bee Animations */
@keyframes bee-wing-flutter {
  0%, 100% { transform: rotateY(0deg) rotateX(0deg); }
  25% { transform: rotateY(15deg) rotateX(5deg); }
  50% { transform: rotateY(0deg) rotateX(-5deg); }
  75% { transform: rotateY(-15deg) rotateX(5deg); }
}

@keyframes bee-body-wobble {
  0%, 100% { transform: rotate(0deg) scale(1); }
  25% { transform: rotate(1deg) scale(1.02); }
  50% { transform: rotate(0deg) scale(1); }
  75% { transform: rotate(-1deg) scale(1.02); }
}

@keyframes bee-glow {
  0%, 100% {
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.3),
                0 0 20px rgba(255, 193, 7, 0.2),
                0 0 30px rgba(255, 193, 7, 0.1);
  }
  50% {
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.5),
                0 0 30px rgba(255, 193, 7, 0.3),
                0 0 45px rgba(255, 193, 7, 0.2);
  }
}

@keyframes bee-trail {
  0% {
    transform: scale(0.8) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.5) rotate(180deg);
    opacity: 0.2;
  }
  100% {
    transform: scale(2) rotate(360deg);
    opacity: 0;
  }
}

@keyframes sparkle-dance {
  0%, 100% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }
  25% {
    transform: scale(1) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: scale(0.8) rotate(270deg);
    opacity: 0.6;
  }
}

/* Bee-specific animation classes */
.animate-bee-wing-flutter { animation: bee-wing-flutter 0.1s ease-in-out infinite; }
.animate-bee-body-wobble { animation: bee-body-wobble 2s ease-in-out infinite; }
.animate-bee-glow { animation: bee-glow 3s ease-in-out infinite; }
.animate-bee-trail { animation: bee-trail 1s ease-out infinite; }
.animate-sparkle-dance { animation: sparkle-dance 1.5s ease-in-out infinite; }

/* Mobile Performance Optimizations */
@media (max-width: 768px) {
  /* Optimize touch interactions */
  * {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* Reduce motion for better performance */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Optimize scrolling */
  body {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Hide scrollbar on mobile for cleaner look */
  ::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  /* Reduce animation complexity on mobile */
  .animate-float-3d,
  .animate-morph-3d,
  .animate-hologram,
  .animate-cyber-glitch {
    animation: none !important;
  }

  /* Simplify transforms on mobile */
  .perspective-1000,
  .perspective-2000,
  .perspective-3000 {
    perspective: none !important;
  }

  /* Disable expensive filters on mobile */
  .glass-morphism,
  .glass-morphism-strong,
  .glass-morphism-purple {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }

  /* Fix mobile layout issues */
  .hero-section {
    padding-top: 80px !important;
    min-height: 100vh !important;
  }

  /* Ensure proper spacing for mobile hero */
  #home {
    padding-top: 80px !important;
  }

  /* Fix text overlapping on mobile */
  .hero-title {
    line-height: 1.1 !important;
    margin-bottom: 1rem !important;
  }

  /* Ensure proper z-index stacking */
  .navbar {
    z-index: 50 !important;
  }

  .online-indicator {
    z-index: 40 !important;
  }

  .hero-content {
    z-index: 10 !important;
  }

  /* Better mobile text sizing */
  .hero-title h1 {
    font-size: 2.5rem !important;
    line-height: 1.2 !important;
  }

  /* Fix mobile spacing issues */
  .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Better mobile button sizing */
  .animated-button {
    padding: 0.75rem 1.5rem !important;
    font-size: 0.9rem !important;
  }

  /* Fix mobile card layouts */
  .project-card,
  .skill-card,
  .blog-card {
    margin-bottom: 1rem !important;
  }

  /* Better mobile navigation */
  .mobile-nav {
    padding: 1rem !important;
  }

  /* Fix mobile section spacing */
  section {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
    scroll-margin-top: 80px !important;
  }

  /* Better mobile scroll positioning */
  #home, #about, #skills, #projects, #blog, #resume, #contact {
    scroll-margin-top: 80px !important;
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  /* Fix mobile text positioning */
  .hero-section {
    padding-bottom: 4rem !important;
  }

  /* Better mobile footer spacing */
  footer {
    margin-top: 4rem !important;
  }

  /* Fix mobile text spacing issues */
  p, h1, h2, h3, h4, h5, h6, span, div {
    word-spacing: normal !important;
    letter-spacing: normal !important;
    white-space: normal !important;
  }

  /* Ensure proper mobile text wrapping */
  .text-wrap {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }

  /* Fix mobile line heights */
  p {
    line-height: 1.5 !important;
    margin-bottom: 1rem !important;
  }

  h1, h2, h3, h4, h5, h6 {
    line-height: 1.1 !important;
    margin-bottom: 0.75rem !important;
  }

  /* Fix mobile inline element spacing */
  span + span,
  a + a,
  button + button {
    margin-left: 0.5rem !important;
  }

  /* Prevent text compression on mobile */
  .hero-title,
  .section-title,
  .card-title {
    white-space: normal !important;
    word-spacing: normal !important;
    letter-spacing: normal !important;
  }

  /* Fix mobile text overflow */
  .text-ellipsis {
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    white-space: nowrap !important;
  }

  /* Mobile-specific fixes for blog and project cards */
  .blog-card, .project-card {
    word-spacing: normal !important;
    letter-spacing: normal !important;
  }

  .blog-card h3, .project-card h3 {
    font-size: 1.125rem !important;
    line-height: 1.4 !important;
    margin-bottom: 0.75rem !important;
    word-spacing: normal !important;
    letter-spacing: normal !important;
  }

  .blog-card p, .project-card p {
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
    margin-bottom: 1rem !important;
    word-spacing: normal !important;
    letter-spacing: normal !important;
  }

  /* Fix mobile text positioning */
  .blog-card .text-overlay-fix,
  .project-card .text-overlay-fix {
    position: relative !important;
    z-index: 25 !important;
    background: transparent !important;
  }

  /* Ensure proper mobile text rendering */
  .blog-card > div:last-child,
  .project-card > div:last-child {
    padding: 1rem !important;
    position: relative !important;
    z-index: 20 !important;
  }

  /* Enhanced mobile card effects */
  .blog-card, .project-card {
    transform: none !important;
    transition: all 0.3s ease !important;
  }

  .blog-card:hover, .project-card:hover {
    transform: translateY(-4px) scale(1.01) !important;
    box-shadow: 0 15px 30px -8px rgba(0, 0, 0, 0.4) !important;
  }

  /* Mobile-optimized backdrop blur */
  .backdrop-blur-xl {
    backdrop-filter: blur(12px) !important;
    -webkit-backdrop-filter: blur(12px) !important;
  }

  /* Reduce mobile animations for performance */
  .group:hover img {
    transform: scale(1.05) !important;
    filter: brightness(1.05) !important;
  }

  /* Mobile card content spacing */
  .blog-card .p-6,
  .project-card .p-6 {
    padding: 1.25rem !important;
  }

  /* Mobile button sizing */
  .blog-card button,
  .project-card button {
    padding: 0.75rem 1rem !important;
    font-size: 0.875rem !important;
  }
}