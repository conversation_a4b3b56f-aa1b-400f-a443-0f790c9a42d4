import React, { useEffect, useState, useRef } from 'react';
import { motion, useAnimation, useInView, useMotionValue, useSpring } from 'framer-motion';

// Primitive Stone Arrowhead Cursor Follower Component (Desktop Only)
export const CursorFollower: React.FC = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const [isMoving, setIsMoving] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Don't render on mobile devices
  if (isMobile) {
    return null;
  }

  useEffect(() => {
    let moveTimeout: NodeJS.Timeout;

    const updateMousePosition = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });

      // Detect if mouse is moving
      setIsMoving(true);
      clearTimeout(moveTimeout);
      moveTimeout = setTimeout(() => setIsMoving(false), 150);
    };

    const handleMouseEnter = () => setIsHovering(true);
    const handleMouseLeave = () => setIsHovering(false);

    window.addEventListener('mousemove', updateMousePosition);

    // Add hover listeners to interactive elements
    const interactiveElements = document.querySelectorAll('button, a, [role="button"]');
    interactiveElements.forEach(el => {
      el.addEventListener('mouseenter', handleMouseEnter);
      el.addEventListener('mouseleave', handleMouseLeave);
    });

    return () => {
      window.removeEventListener('mousemove', updateMousePosition);
      clearTimeout(moveTimeout);
      interactiveElements.forEach(el => {
        el.removeEventListener('mouseenter', handleMouseEnter);
        el.removeEventListener('mouseleave', handleMouseLeave);
      });
    };
  }, []);

  return (
    <motion.div
      className="fixed top-0 left-0 pointer-events-none z-50"
      animate={{
        x: mousePosition.x - 20,
        y: mousePosition.y - 25,
        scale: isHovering ? 1.15 : 1,
        rotate: isMoving ? [0, 2, -2, 0] : 0,
      }}
      transition={{
        type: "spring",
        stiffness: 400,
        damping: 25,
      }}
    >
      {/* Primitive Stone Arrowhead Cursor */}
      <div className="relative w-10 h-14">
        {/* Stone Arrowhead */}
        <motion.div
          className="absolute top-0 left-1/2 transform -translate-x-1/2"
          animate={{
            y: isMoving ? [0, -1, 0] : 0,
          }}
          transition={{
            duration: 0.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          {/* Arrowhead Shadow */}
          <div className="absolute top-1 left-1/2 transform -translate-x-1/2 w-6 h-6 bg-black/30 blur-sm"></div>

          {/* Main Stone Arrowhead with thick outline */}
          <div className="relative w-5 h-6 bg-gradient-to-br from-gray-400 via-gray-500 to-gray-700 shadow-xl border-2 border-gray-800"
               style={{ clipPath: 'polygon(50% 0%, 15% 85%, 50% 70%, 85% 85%)' }}>

            {/* Stone texture and highlights */}
            <div className="absolute top-1 left-1 w-2 h-2 bg-gradient-to-br from-gray-300/70 to-transparent rounded-sm"></div>
            <div className="absolute top-2 right-1 w-1 h-1 bg-gray-300/50 rounded-full"></div>

            {/* Chip/notch detail on left edge */}
            <div className="absolute top-3 left-0 w-1 h-1 bg-gray-800 rounded-sm transform -rotate-45"></div>

            {/* Stone edge highlight */}
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-1 h-3 bg-gradient-to-b from-gray-200/80 to-transparent"></div>
          </div>
        </motion.div>

        {/* Rope Bindings */}
        <div className="absolute top-5 left-1/2 transform -translate-x-1/2">
          {/* First rope band - diagonal */}
          <div className="absolute w-4 h-1 bg-gradient-to-r from-amber-200 via-amber-300 to-amber-400 rounded-full shadow-sm border border-amber-500/50 transform rotate-12"></div>

          {/* Second rope band - opposite diagonal */}
          <div className="absolute w-4 h-1 bg-gradient-to-r from-amber-200 via-amber-300 to-amber-400 rounded-full shadow-sm border border-amber-500/50 transform -rotate-12"></div>

          {/* Rope texture details */}
          <div className="absolute top-0 left-1 w-0.5 h-0.5 bg-amber-600 rounded-full transform rotate-12"></div>
          <div className="absolute top-0 right-1 w-0.5 h-0.5 bg-amber-600 rounded-full transform -rotate-12"></div>
        </div>

        {/* Wooden Shaft */}
        <motion.div
          className="absolute top-6 left-1/2 transform -translate-x-1/2"
          animate={{
            scaleY: isMoving ? [1, 1.03, 1] : 1,
          }}
          transition={{
            duration: 0.4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          {/* Shaft Shadow */}
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-5 h-2 bg-black/25 rounded-full blur-sm"></div>

          {/* Main Wooden Shaft with thick outline */}
          <div className="relative w-3 h-7 bg-gradient-to-br from-amber-500 via-amber-600 to-amber-800 rounded-lg shadow-lg border-2 border-amber-900">
            {/* Wood grain highlight */}
            <div className="absolute top-1 left-0.5 w-1 h-5 bg-gradient-to-b from-amber-300/70 to-transparent rounded-l-lg"></div>

            {/* Wood grain texture lines */}
            <div className="absolute top-1 left-0 right-0 h-0.5 bg-amber-700/60 rounded-full"></div>
            <div className="absolute top-2.5 left-0 right-0 h-0.5 bg-amber-700/40 rounded-full"></div>
            <div className="absolute top-4 left-0 right-0 h-0.5 bg-amber-700/60 rounded-full"></div>
            <div className="absolute top-5.5 left-0 right-0 h-0.5 bg-amber-700/40 rounded-full"></div>

            {/* Wood knots */}
            <div className="absolute top-3 left-1 w-1 h-0.5 bg-amber-800 rounded-full"></div>

            {/* Shaft end */}
            <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-amber-800 via-amber-900 to-amber-800 rounded-b-lg"></div>
          </div>
        </motion.div>

        {/* Primitive Sparkle Effects when hovering */}
        {isHovering && (
          <motion.div
            className="absolute inset-0"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1.5 h-1.5 bg-orange-400 shadow-lg"
                style={{
                  left: `${20 + Math.cos(i * 60 * Math.PI / 180) * 15}px`,
                  top: `${25 + Math.sin(i * 60 * Math.PI / 180) * 15}px`,
                  clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
                }}
                animate={{
                  scale: [0, 1.3, 0],
                  opacity: [0, 0.9, 0],
                  rotate: [0, 120, 240, 360],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2,
                  ease: "easeInOut"
                }}
              />
            ))}

            {/* Stone dust effect */}
            {[...Array(4)].map((_, i) => (
              <motion.div
                key={`dust-${i}`}
                className="absolute w-1 h-1 bg-gray-400 rounded-full"
                style={{
                  left: `${18 + Math.cos((i * 90 + 45) * Math.PI / 180) * 10}px`,
                  top: `${23 + Math.sin((i * 90 + 45) * Math.PI / 180) * 10}px`,
                }}
                animate={{
                  scale: [0, 1, 0],
                  opacity: [0, 0.6, 0],
                  y: [0, -8, -15],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.3,
                  ease: "easeOut"
                }}
              />
            ))}

            {/* Arrowhead Trail Effect */}
            <motion.div
              className="absolute top-0 left-1/2 transform -translate-x-1/2"
              animate={{
                scale: [1, 1.6, 1],
                opacity: [0.4, 0.1, 0.4],
              }}
              transition={{
                duration: 1.2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <div className="w-8 h-8 bg-gradient-to-r from-gray-400/30 via-amber-400/40 to-gray-400/30 rounded-full blur-md"></div>
            </motion.div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

// Enhanced Parallax Component (Optimized for Mobile)
export const ParallaxElement: React.FC<{
  children: React.ReactNode;
  speed?: number;
  className?: string;
}> = ({ children, speed = 0.5, className = '' }) => {
  const ref = useRef<HTMLDivElement>(null);
  const y = useMotionValue(0);
  const ySpring = useSpring(y, { stiffness: 400, damping: 90 });
  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    // Disable parallax on mobile for performance
    if (isMobile) {
      return;
    }

    let ticking = false;
    const updateY = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          if (ref.current) {
            const rect = ref.current.getBoundingClientRect();
            const scrolled = window.pageYOffset;
            const rate = scrolled * -speed;
            y.set(rate);
          }
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', updateY, { passive: true });
    return () => window.removeEventListener('scroll', updateY);
  }, [speed, y, isMobile]);

  return (
    <motion.div
      ref={ref}
      style={isMobile ? {} : { y: ySpring }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

// Enhanced Scroll Reveal Animation
export const ScrollReveal: React.FC<{
  children: React.ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right';
  delay?: number;
  duration?: number;
  className?: string;
}> = ({ 
  children, 
  direction = 'up', 
  delay = 0, 
  duration = 0.6,
  className = '' 
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const controls = useAnimation();

  const variants = {
    hidden: {
      opacity: 0,
      y: direction === 'up' ? 50 : direction === 'down' ? -50 : 0,
      x: direction === 'left' ? 50 : direction === 'right' ? -50 : 0,
    },
    visible: {
      opacity: 1,
      y: 0,
      x: 0,
      transition: {
        duration,
        delay,
        ease: [0.25, 0.25, 0.25, 0.75],
      },
    },
  };

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={variants}
      className={className}
    >
      {children}
    </motion.div>
  );
};

// Enhanced Stagger Animation
export const StaggerContainer: React.FC<{
  children: React.ReactNode;
  staggerDelay?: number;
  className?: string;
}> = ({ children, staggerDelay = 0.1, className = '' }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-50px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: staggerDelay,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.25, 0.25, 0.75],
      },
    },
  };

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={containerVariants}
      className={className}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div key={index} variants={itemVariants}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};

// Enhanced Hover Effect Component
export const HoverEffect: React.FC<{
  children: React.ReactNode;
  scale?: number;
  rotate?: number;
  className?: string;
}> = ({ children, scale = 1.05, rotate = 0, className = '' }) => {
  return (
    <motion.div
      className={className}
      whileHover={{
        scale,
        rotate,
        transition: { duration: 0.3, ease: "easeOut" },
      }}
      whileTap={{ scale: 0.95 }}
    >
      {children}
    </motion.div>
  );
};

// Floating Animation Component
export const FloatingElement: React.FC<{
  children: React.ReactNode;
  duration?: number;
  delay?: number;
  className?: string;
}> = ({ children, duration = 3, delay = 0, className = '' }) => {
  return (
    <motion.div
      className={className}
      animate={{
        y: [0, -10, 0],
        rotate: [0, 1, -1, 0],
      }}
      transition={{
        duration,
        delay,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    >
      {children}
    </motion.div>
  );
};

// Text Reveal Animation
export const TextReveal: React.FC<{
  text: string;
  className?: string;
  delay?: number;
}> = ({ text, className = '', delay = 0 }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: delay,
      },
    },
  };

  const letterVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.25, 0.25, 0.75],
      },
    },
  };

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={containerVariants}
      className={className}
    >
      {text.split('').map((char, index) => (
        <motion.span
          key={index}
          variants={letterVariants}
          style={{ display: 'inline-block' }}
        >
          {char === ' ' ? '\u00A0' : char}
        </motion.span>
      ))}
    </motion.div>
  );
};

// Magnetic Button Component
export const MagneticButton: React.FC<{
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}> = ({ children, className = '', onClick }) => {
  const ref = useRef<HTMLButtonElement>(null);
  const x = useMotionValue(0);
  const y = useMotionValue(0);

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!ref.current) return;
    
    const rect = ref.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    x.set((e.clientX - centerX) * 0.1);
    y.set((e.clientY - centerY) * 0.1);
  };

  const handleMouseLeave = () => {
    x.set(0);
    y.set(0);
  };

  return (
    <motion.button
      ref={ref}
      style={{ x, y }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onClick={onClick}
      className={className}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      transition={{ type: "spring", stiffness: 400, damping: 25 }}
    >
      {children}
    </motion.button>
  );
};
