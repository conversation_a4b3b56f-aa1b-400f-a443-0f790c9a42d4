import React, { useState, useEffect, useRef } from 'react';
import { imageOptimizationService, ResponsiveImageConfig } from '../services/imageOptimizationService';
import { cdnService } from '../services/cdnService';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  sizes?: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  quality?: number;
  formats?: ('webp' | 'avif' | 'jpeg' | 'png')[];
  lazy?: boolean;
  priority?: boolean;
  placeholder?: 'blur' | 'empty' | string;
  onLoad?: () => void;
  onError?: () => void;
  style?: React.CSSProperties;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className = '',
  sizes = { mobile: 480, tablet: 768, desktop: 1200 },
  quality = 85,
  formats = ['avif', 'webp', 'jpeg'],
  lazy = true,
  priority = false,
  placeholder = 'blur',
  onLoad,
  onError,
  style
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(!lazy || priority);
  const imgRef = useRef<HTMLImageElement>(null);
  const pictureRef = useRef<HTMLPictureElement>(null);

  // Generate optimized image configuration
  const imageConfig: ResponsiveImageConfig = {
    src: cdnService.optimizeAssetUrl(src, { quality }),
    alt,
    sizes,
    quality,
    formats,
    lazy: lazy && !priority,
    priority
  };

  // Setup intersection observer for lazy loading
  useEffect(() => {
    if (!lazy || priority || isInView) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.unobserve(entry.target);
          }
        });
      },
      {
        rootMargin: '50px 0px',
        threshold: 0.01
      }
    );

    if (pictureRef.current) {
      observer.observe(pictureRef.current);
    }

    return () => {
      if (pictureRef.current) {
        observer.unobserve(pictureRef.current);
      }
    };
  }, [lazy, priority, isInView]);

  // Generate optimized sources
  const { sources, fallback } = imageOptimizationService.generateOptimizedSources(imageConfig);

  // Handle image load
  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  // Handle image error
  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  // Generate placeholder
  const generatePlaceholder = (): string => {
    if (placeholder === 'empty') {
      return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9InRyYW5zcGFyZW50Ii8+PC9zdmc+';
    }
    
    if (placeholder === 'blur') {
      // Generate a blurred placeholder (simplified version)
      return `data:image/svg+xml;base64,${btoa(`
        <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect width="40" height="40" fill="url(#gradient)"/>
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#f3f4f6;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
            </linearGradient>
          </defs>
        </svg>
      `)}`;
    }
    
    return placeholder;
  };

  // Preload critical images
  useEffect(() => {
    if (priority) {
      imageOptimizationService.preloadCriticalImages([src]);
    }
  }, [src, priority]);

  // Error fallback component
  if (hasError) {
    return (
      <div 
        className={`bg-gray-200 flex items-center justify-center text-gray-500 ${className}`}
        style={style}
      >
        <svg 
          className="w-8 h-8" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" 
          />
        </svg>
      </div>
    );
  }

  return (
    <picture 
      ref={pictureRef}
      className={`block ${className}`}
      style={style}
    >
      {/* Render optimized sources only when in view */}
      {isInView && sources.map((source, index) => (
        <source
          key={index}
          srcSet={source.srcset}
          type={source.type}
          sizes={source.sizes}
        />
      ))}
      
      <img
        ref={imgRef}
        src={isInView ? fallback : generatePlaceholder()}
        alt={alt}
        className={`w-full h-auto transition-opacity duration-300 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        loading={priority ? 'eager' : 'lazy'}
        decoding="async"
        onLoad={handleLoad}
        onError={handleError}
        style={{
          filter: isLoaded ? 'none' : 'blur(5px)',
          transition: 'filter 0.3s ease-out, opacity 0.3s ease-out'
        }}
      />
      
      {/* Loading placeholder overlay */}
      {!isLoaded && isInView && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
      )}
    </picture>
  );
};

// High-performance image component with advanced features
export const AdvancedImage: React.FC<OptimizedImageProps & {
  aspectRatio?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none';
  background?: string;
}> = ({
  aspectRatio,
  objectFit = 'cover',
  background = 'transparent',
  className = '',
  style,
  ...props
}) => {
  const containerStyle: React.CSSProperties = {
    position: 'relative',
    overflow: 'hidden',
    background,
    ...style
  };

  if (aspectRatio) {
    containerStyle.aspectRatio = aspectRatio;
  }

  return (
    <div className={`relative ${className}`} style={containerStyle}>
      <OptimizedImage
        {...props}
        className="absolute inset-0 w-full h-full"
        style={{ objectFit }}
      />
    </div>
  );
};

// Gallery component with optimized loading
export const ImageGallery: React.FC<{
  images: Array<{
    src: string;
    alt: string;
    caption?: string;
  }>;
  columns?: number;
  gap?: number;
  lazy?: boolean;
}> = ({ 
  images, 
  columns = 3, 
  gap = 16, 
  lazy = true 
}) => {
  const [loadedCount, setLoadedCount] = useState(0);

  const handleImageLoad = () => {
    setLoadedCount(prev => prev + 1);
  };

  return (
    <div 
      className="grid"
      style={{
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap: `${gap}px`
      }}
    >
      {images.map((image, index) => (
        <div key={index} className="relative group">
          <AdvancedImage
            src={image.src}
            alt={image.alt}
            aspectRatio="1"
            lazy={lazy && index > 2} // Load first 3 images immediately
            priority={index < 3}
            className="rounded-lg overflow-hidden transition-transform duration-300 group-hover:scale-105"
            onLoad={handleImageLoad}
          />
          
          {image.caption && (
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
              <p className="text-white text-sm">{image.caption}</p>
            </div>
          )}
        </div>
      ))}
      
      {/* Loading progress indicator */}
      {loadedCount < images.length && (
        <div className="col-span-full text-center py-4">
          <div className="inline-flex items-center space-x-2 text-gray-500">
            <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
            <span>Loading images... ({loadedCount}/{images.length})</span>
          </div>
        </div>
      )}
    </div>
  );
};

// Hero image component with advanced optimization
export const HeroImage: React.FC<OptimizedImageProps & {
  overlay?: boolean;
  overlayColor?: string;
  overlayOpacity?: number;
}> = ({
  overlay = false,
  overlayColor = 'black',
  overlayOpacity = 0.4,
  className = '',
  ...props
}) => {
  return (
    <div className={`relative ${className}`}>
      <AdvancedImage
        {...props}
        priority={true}
        lazy={false}
        sizes={{ mobile: 480, tablet: 1024, desktop: 1920 }}
        formats={['avif', 'webp', 'jpeg']}
        quality={90}
        className="w-full h-full"
      />
      
      {overlay && (
        <div 
          className="absolute inset-0"
          style={{
            background: overlayColor,
            opacity: overlayOpacity
          }}
        />
      )}
    </div>
  );
};

export default OptimizedImage;
