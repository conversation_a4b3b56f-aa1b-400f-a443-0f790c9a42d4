import React from 'react';
import { Linkedin, Mail, Heart, ArrowUp, Code, Coffee, Zap, Copy, ExternalLink } from 'lucide-react';
import toast from 'react-hot-toast';
import { EmailOptionsToast, SuccessToast, ErrorToast } from './CustomToast';
import GitHubIcon from './GitHubIcon';
import { useLanguage } from '../contexts/LanguageContext';

const Footer = () => {
  const { t } = useLanguage();
  // Ultimate email handling with custom toasts
  const handleEmailClick = (e: React.MouseEvent) => {
    e.preventDefault();

    // Show custom email options toast
    toast((t) => (
      <EmailOptionsToast
        t={t}
        onCopy={copyEmailToClipboard}
        onGmail={openGmail}
      />
    ), {
      duration: 15000,
    });
  };

  const copyEmailToClipboard = async () => {
    const email = '<EMAIL>';

    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(email);
        toast(() => (
          <SuccessToast
            message={t('footer.email.copied')}
            icon={<Copy className="w-5 h-5 text-green-400" />}
          />
        ));
      } else {
        // Fallback for older browsers
        try {
          const textArea = document.createElement('textarea');
          textArea.value = email;
          textArea.style.position = 'fixed';
          textArea.style.left = '-999999px';
          textArea.style.top = '-999999px';
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          const successful = document.execCommand('copy');
          document.body.removeChild(textArea);

          if (successful) {
            toast(() => (
              <SuccessToast
                message={t('footer.email.copied')}
                icon={<Copy className="w-5 h-5 text-green-400" />}
              />
            ));
          } else {
            throw new Error('Copy command failed');
          }
        } catch (err) {
          toast(() => (
            <ErrorToast message={`${t('footer.email.copyFailed')} ${email}`} />
          ));
        }
      }
    } catch (err) {
      toast(() => (
        <ErrorToast message={`${t('footer.email.copyFailed')} ${email}`} />
      ));
    }
  };

  const openGmail = () => {
    const email = '<EMAIL>';
    window.open(`https://mail.google.com/mail/?view=cm&fs=1&to=${email}`, '_blank');
    toast(() => (
      <SuccessToast
        message={t('footer.email.openingGmail')}
        icon={<ExternalLink className="w-5 h-5 text-green-400" />}
      />
    ));
  };

  const socialLinks = [
    { icon: GitHubIcon, href: 'https://github.com/NuralBhardwaj/', label: t('footer.social.github'), color: 'hover:text-gray-300', isEmail: false },
    { icon: Linkedin, href: 'https://www.linkedin.com/in/nural-bhardwaj/', label: t('footer.social.linkedin'), color: 'hover:text-blue-400', isEmail: false },
    { icon: Mail, href: '#', label: t('footer.social.email'), color: 'hover:text-purple-400', isEmail: true }
  ];

  const quickLinks = [
    { label: t('nav.about'), href: '#about' },
    { label: t('nav.skills'), href: '#skills' },
    { label: t('nav.projects'), href: '#projects' },
    { label: t('nav.contact'), href: '#contact' }
  ];

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId.replace('#', ''));
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <footer className="relative bg-gradient-to-br from-black via-gray-900 to-black py-16 border-t border-white/5">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-1/4 w-64 h-64 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-gradient-to-r from-cyan-600 to-blue-600 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-12 mb-8 sm:mb-12">
          {/* Brand Section */}
          <div className="md:col-span-2 lg:col-span-2 space-y-4 sm:space-y-6">
            <div className="group">
              <button
                onClick={scrollToTop}
                className="text-2xl sm:text-3xl lg:text-4xl font-black bg-gradient-to-r from-purple-400 via-pink-500 to-cyan-400 bg-clip-text text-transparent hover:scale-105 transition-transform duration-300"
              >
                Nuralbhardwaj.me
              </button>
              <div className="w-12 sm:w-16 h-1 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full mt-2"></div>
            </div>

            <p className="text-gray-400 text-base sm:text-lg leading-relaxed max-w-md">
              {t('footer.description')}
            </p>

            {/* Stats */}
            <div className="flex flex-wrap items-center gap-4 sm:gap-6 lg:gap-8 pt-2 sm:pt-4">
              <div className="flex items-center space-x-2 text-gray-400">
                <Code className="w-4 h-4 sm:w-5 sm:h-5 text-purple-400" />
                <span className="text-white font-bold text-sm sm:text-base">50+</span>
                <span className="text-xs sm:text-sm">{t('footer.stats.projects')}</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-400">
                <Coffee className="w-4 h-4 sm:w-5 sm:h-5 text-orange-400" />
                <span className="text-white font-bold text-sm sm:text-base">5+</span>
                <span className="text-xs sm:text-sm">{t('footer.stats.years')}</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-400">
                <Zap className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400" />
                <span className="text-white font-bold text-sm sm:text-base">100%</span>
                <span className="text-xs sm:text-sm">{t('footer.stats.satisfaction')}</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4 sm:space-y-6">
            <h3 className="text-lg sm:text-xl font-bold text-white">{t('footer.quickLinks')}</h3>
            <div className="space-y-2 sm:space-y-3">
              {quickLinks.map((link) => (
                <button
                  key={link.label}
                  onClick={() => scrollToSection(link.href)}
                  className="block text-gray-400 hover:text-purple-400 transition-colors duration-300 text-left hover:translate-x-2 transform transition-transform text-sm sm:text-base"
                >
                  {link.label}
                </button>
              ))}
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-4 sm:space-y-6">
            <h3 className="text-lg sm:text-xl font-bold text-white">{t('footer.getInTouch')}</h3>
            <div className="space-y-3 sm:space-y-4">
              <div className="text-gray-400">
                <div className="font-medium text-white mb-1 text-sm sm:text-base">{t('footer.contact.email')}</div>
                <a href="mailto:<EMAIL>" className="hover:text-purple-400 transition-colors duration-300 text-xs sm:text-sm break-all">
                  <EMAIL>
                </a>
              </div>
              <div className="text-gray-400">
                <div className="font-medium text-white mb-1 text-sm sm:text-base">{t('footer.contact.location')}</div>
                <span className="text-xs sm:text-sm">{t('footer.contact.locationValue')}</span>
              </div>
              <div className="text-gray-400">
                <div className="font-medium text-white mb-1 text-sm sm:text-base">{t('footer.contact.availability')}</div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 font-medium text-xs sm:text-sm">{t('footer.contact.availabilityStatus')}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Social Links */}
        <div className="flex flex-col sm:flex-row items-center justify-between pt-6 sm:pt-8 border-t border-white/10 gap-6 sm:gap-0">
          <div className="flex items-center space-x-4 sm:space-x-6 relative">
            {socialLinks.map(({ icon: Icon, href, label, color, isEmail }) => (
              <div key={label} className="relative">
                <a
                  href={isEmail ? '#' : href}
                  target={isEmail ? '_self' : '_blank'}
                  rel={isEmail ? undefined : 'noopener noreferrer'}
                  onClick={isEmail ? handleEmailClick : undefined}
                  className={`group relative p-3 sm:p-4 bg-white/5 backdrop-blur-sm rounded-xl sm:rounded-2xl hover:bg-white/10 transition-all duration-500 transform hover:scale-110 hover:-translate-y-2 border border-white/10 hover:border-purple-500/30 ${color} block`}
                  aria-label={label}
                >
                  <Icon className="w-5 h-5 sm:w-6 sm:h-6 text-gray-400 group-hover:text-current transition-colors duration-300" />
                  <div className="absolute -top-10 sm:-top-12 left-1/2 transform -translate-x-1/2 bg-black/90 text-white px-2 sm:px-3 py-1 rounded-lg text-xs sm:text-sm opacity-0 group-hover:opacity-100 transition-all duration-300 border border-white/20 whitespace-nowrap">
                    {label}
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/90"></div>
                  </div>
                  <div className="absolute -inset-1 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 opacity-0 group-hover:opacity-100 blur-lg transition-opacity duration-500 rounded-xl sm:rounded-2xl"></div>
                </a>
              </div>
            ))}
          </div>

          {/* Scroll to Top Button */}
          <button
            onClick={scrollToTop}
            className="group relative p-3 sm:p-4 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 backdrop-blur-sm rounded-xl sm:rounded-2xl hover:from-purple-600/30 hover:to-cyan-600/30 transition-all duration-500 transform hover:scale-110 border border-purple-500/30 hover:border-purple-400/50"
            aria-label="Scroll to top"
          >
            <ArrowUp className="w-5 h-5 sm:w-6 sm:h-6 text-purple-400 group-hover:text-white transition-colors duration-300" />
            <div className="absolute -inset-1 bg-gradient-to-r from-purple-600/30 to-cyan-600/30 opacity-0 group-hover:opacity-100 blur-lg transition-opacity duration-500 rounded-xl sm:rounded-2xl"></div>
          </button>
        </div>

        {/* Copyright */}
        <div className="text-center pt-6 sm:pt-8 border-t border-white/5 mt-6 sm:mt-8">
          <p className="text-gray-400 flex flex-wrap items-center justify-center gap-1 sm:gap-2 text-xs sm:text-sm">
            <span>{t('footer.copyright.text')}</span>
            <Heart className="w-3 h-3 sm:w-4 sm:h-4 text-red-400 animate-pulse" />
            <span>{t('footer.copyright.and')}</span>
            <Coffee className="w-3 h-3 sm:w-4 sm:h-4 text-orange-400" />
            <span className="hidden sm:inline">•</span>
            <span>{t('footer.copyright.rights')}</span>
          </p>
          <p className="text-gray-500 text-xs mt-1 sm:mt-2">
            {t('footer.copyright.tagline')}
          </p>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-500/50 to-transparent"></div>
    </footer>
  );
};

export default Footer;