import React, { useEffect, useState, useRef } from 'react';

// Mobile performance optimization utilities
export const useMobileOptimization = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [isLowEndDevice, setIsLowEndDevice] = useState(false);

  useEffect(() => {
    const checkDevice = () => {
      const isMobileDevice = window.innerWidth < 768 || 
        /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      
      setIsMobile(isMobileDevice);

      // Check for low-end device indicators
      const isLowEnd = 
        navigator.hardwareConcurrency <= 2 || // 2 or fewer CPU cores
        navigator.deviceMemory <= 2 || // 2GB or less RAM
        /Android.*Chrome\/[1-6][0-9]/.test(navigator.userAgent); // Older Chrome on Android
      
      setIsLowEndDevice(isLowEnd);
    };

    checkDevice();
    window.addEventListener('resize', checkDevice);
    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  return { isMobile, isLowEndDevice };
};

// Throttled scroll hook for better mobile performance
export const useThrottledScroll = (callback: () => void, delay: number = 16) => {
  const [isScrolling, setIsScrolling] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          callback();
          ticking = false;
        });
        ticking = true;
      }

      setIsScrolling(true);
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        setIsScrolling(false);
      }, delay);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [callback, delay]);

  return isScrolling;
};

// Intersection Observer hook with mobile optimizations
export const useOptimizedIntersection = (
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const ref = useRef<HTMLElement>(null);
  const { isMobile } = useMobileOptimization();

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    // Use larger root margin on mobile for better performance
    const mobileOptions = {
      ...options,
      rootMargin: isMobile ? '50px' : options.rootMargin || '0px',
      threshold: isMobile ? 0.1 : options.threshold || 0.5,
    };

    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting);
      if (entry.isIntersecting && !hasIntersected) {
        setHasIntersected(true);
      }
    }, mobileOptions);

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [options, isMobile, hasIntersected]);

  return { ref, isIntersecting, hasIntersected };
};

// Performance-aware animation component
export const MobileOptimizedAnimation: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  enableOnMobile?: boolean;
}> = ({ children, fallback, enableOnMobile = false }) => {
  const { isMobile, isLowEndDevice } = useMobileOptimization();
  
  // Disable complex animations on mobile/low-end devices unless explicitly enabled
  if ((isMobile || isLowEndDevice) && !enableOnMobile) {
    return <>{fallback || children}</>;
  }

  return <>{children}</>;
};

// Reduced motion preference detection
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};

// Touch-optimized component wrapper
export const TouchOptimized: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => {
  const { isMobile } = useMobileOptimization();

  const touchOptimizedClass = isMobile 
    ? 'touch-manipulation select-none' 
    : '';

  return (
    <div className={`${className} ${touchOptimizedClass}`}>
      {children}
    </div>
  );
};

// Lazy loading component for heavy content
export const LazyContent: React.FC<{
  children: React.ReactNode;
  placeholder?: React.ReactNode;
  delay?: number;
}> = ({ children, placeholder, delay = 100 }) => {
  const [shouldRender, setShouldRender] = useState(false);
  const { isMobile } = useMobileOptimization();

  useEffect(() => {
    const timer = setTimeout(() => {
      setShouldRender(true);
    }, isMobile ? delay * 2 : delay);

    return () => clearTimeout(timer);
  }, [delay, isMobile]);

  if (!shouldRender) {
    return <>{placeholder || <div className="animate-pulse bg-gray-800 rounded-lg h-32" />}</>;
  }

  return <>{children}</>;
};

// Enhanced performance monitoring hook
export const usePerformanceMonitor = () => {
  const [fps, setFps] = useState(60);
  const [memoryUsage, setMemoryUsage] = useState(0);
  const [networkSpeed, setNetworkSpeed] = useState('unknown');
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());

  useEffect(() => {
    let animationId: number;

    const measureFPS = () => {
      frameCount.current++;
      const currentTime = performance.now();

      if (currentTime - lastTime.current >= 1000) {
        setFps(frameCount.current);
        frameCount.current = 0;
        lastTime.current = currentTime;
      }

      animationId = requestAnimationFrame(measureFPS);
    };

    // Measure memory usage
    const measureMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usage = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
        setMemoryUsage(usage);
      }
    };

    // Measure network speed
    const measureNetwork = () => {
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        setNetworkSpeed(connection.effectiveType || 'unknown');
      }
    };

    animationId = requestAnimationFrame(measureFPS);
    measureMemory();
    measureNetwork();

    // Update memory and network info periodically
    const memoryInterval = setInterval(measureMemory, 5000);
    const networkInterval = setInterval(measureNetwork, 10000);

    return () => {
      cancelAnimationFrame(animationId);
      clearInterval(memoryInterval);
      clearInterval(networkInterval);
    };
  }, []);

  return {
    fps,
    memoryUsage,
    networkSpeed,
    isLowPerformance: fps < 30 || memoryUsage > 0.8,
    isSlowNetwork: networkSpeed === 'slow-2g' || networkSpeed === '2g'
  };
};

// Gentle mobile performance optimizer - less aggressive
export const useMobileCriticalOptimization = () => {
  const { isMobile, isLowEndDevice } = useMobileOptimization();
  const { isLowPerformance } = usePerformanceMonitor();

  useEffect(() => {
    // Only apply minimal optimizations to avoid breaking the experience
    if (!isMobile && !isLowPerformance) return;

    // Apply gentle mobile optimizations
    const style = document.createElement('style');
    style.id = 'mobile-critical-optimizations';
    style.textContent = `
      @media (max-width: 768px) and (pointer: coarse) {
        /* Only disable particle systems on mobile - keep all other animations */
        .particle-system,
        .snowflake {
          display: none !important;
        }

        /* Reduce backdrop blur intensity only */
        .backdrop-blur {
          backdrop-filter: blur(3px) !important;
        }

        /* Optimize scroll performance */
        * {
          -webkit-overflow-scrolling: touch;
        }
      }
    `;

    // Remove existing style if present
    const existingStyle = document.getElementById('mobile-critical-optimizations');
    if (existingStyle) {
      existingStyle.remove();
    }

    document.head.appendChild(style);

    return () => {
      const styleToRemove = document.getElementById('mobile-critical-optimizations');
      if (styleToRemove) {
        styleToRemove.remove();
      }
    };
  }, [isMobile, isLowEndDevice, isLowPerformance]);
};
