import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Cloud, 
  CloudOff, 
  Sync, 
  Check, 
  X, 
  AlertTriangle, 
  Settings, 
  Key, 
  Globe, 
  Shield,
  RefreshCw,
  Download,
  Upload,
  Eye,
  EyeOff
} from 'lucide-react';
import { cmsService } from '../../services/cmsService';
import toast from 'react-hot-toast';
import { SuccessToast, ErrorToast, InfoToast } from '../CustomToast';
import GitHubTokenGuide from './GitHubTokenGuide';

interface CloudSyncManagerProps {
  onDataChange: () => void;
}

const CloudSyncManager: React.FC<CloudSyncManagerProps> = ({ onDataChange }) => {
  const [isCloudEnabled, setIsCloudEnabled] = useState(false);
  const [githubToken, setGithubToken] = useState('');
  const [showToken, setShowToken] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'error'>('unknown');
  const [lastSync, setLastSync] = useState<string | null>(null);
  const [showGuide, setShowGuide] = useState(false);

  useEffect(() => {
    // Check if cloud sync is already enabled
    const cloudEnabled = cmsService.isCloudSyncActive();
    setIsCloudEnabled(cloudEnabled);

    // Load saved token (masked)
    const savedToken = localStorage.getItem('github_token');
    if (savedToken) {
      setGithubToken('*'.repeat(20)); // Show masked token
      if (cloudEnabled) {
        setConnectionStatus('connected');
      }
    }

    // Load last sync time
    const lastSyncTime = localStorage.getItem('last_cloud_sync');
    if (lastSyncTime) {
      setLastSync(new Date(lastSyncTime).toLocaleString());
    }
  }, []);

  const validateGitHubToken = (token: string): boolean => {
    // GitHub personal access tokens start with 'ghp_' and are 40+ characters
    const tokenPattern = /^ghp_[A-Za-z0-9]{36,}$/;
    return tokenPattern.test(token);
  };

  const handleEnableCloudSync = async () => {
    if (!githubToken || githubToken.startsWith('*')) {
      toast.custom((t) => (
        <ErrorToast
          message="Please enter a valid GitHub token"
          onClose={() => toast.dismiss(t.id)}
        />
      ));
      return;
    }

    if (!validateGitHubToken(githubToken)) {
      toast.custom((t) => (
        <ErrorToast
          message="Invalid GitHub token format. Token should start with 'ghp_' and be at least 40 characters long."
          onClose={() => toast.dismiss(t.id)}
        />
      ));
      return;
    }

    setIsConnecting(true);
    
    try {
      // Enable cloud sync
      cmsService.enableCloudSync(githubToken);
      
      // Test connection
      const result = await cmsService.testCloudConnection();
      
      if (result.success) {
        setIsCloudEnabled(true);
        setConnectionStatus('connected');
        
        // Sync current data to cloud
        const syncSuccess = await cmsService.syncToCloud();
        if (syncSuccess) {
          setLastSync(new Date().toLocaleString());
          localStorage.setItem('last_cloud_sync', new Date().toISOString());
        }
        
        toast.custom((t) => (
          <SuccessToast
            message="Cloud sync enabled successfully! Your changes will now be visible to all visitors."
            onClose={() => toast.dismiss(t.id)}
          />
        ));
        
        onDataChange();
      } else {
        setConnectionStatus('error');
        toast.custom((t) => (
          <ErrorToast
            message={`Connection failed: ${result.message}`}
            onClose={() => toast.dismiss(t.id)}
          />
        ));
      }
    } catch (error: any) {
      setConnectionStatus('error');
      const errorMessage = error.message || 'Failed to enable cloud sync';
      toast.custom((t) => (
        <ErrorToast
          message={errorMessage}
          onClose={() => toast.dismiss(t.id)}
        />
      ));
      console.error('Cloud sync error:', error);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisableCloudSync = () => {
    cmsService.disableCloudSync();
    setIsCloudEnabled(false);
    setConnectionStatus('unknown');
    setGithubToken('');
    setLastSync(null);
    localStorage.removeItem('last_cloud_sync');
    
    toast.custom((t) => (
      <InfoToast
        message="Cloud sync disabled. Changes will only be visible to you."
        onClose={() => toast.dismiss(t.id)}
      />
    ));
    
    onDataChange();
  };

  const handleManualSync = async () => {
    setIsSyncing(true);
    
    try {
      const success = await cmsService.syncToCloud();
      
      if (success) {
        setLastSync(new Date().toLocaleString());
        localStorage.setItem('last_cloud_sync', new Date().toISOString());
        
        toast.custom((t) => (
          <SuccessToast
            message="Data successfully synced to cloud!"
            onClose={() => toast.dismiss(t.id)}
          />
        ));
      } else {
        toast.custom((t) => (
          <ErrorToast
            message="Failed to sync data to cloud"
            onClose={() => toast.dismiss(t.id)}
          />
        ));
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Error during sync operation';
      toast.custom((t) => (
        <ErrorToast
          message={errorMessage}
          onClose={() => toast.dismiss(t.id)}
        />
      ));
      console.error('Sync error:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  const handleLoadFromCloud = async () => {
    setIsSyncing(true);
    
    try {
      const success = await cmsService.loadFromCloud();
      
      if (success) {
        toast.custom((t) => (
          <SuccessToast
            message="Data successfully loaded from cloud!"
            onClose={() => toast.dismiss(t.id)}
          />
        ));
        onDataChange();
      } else {
        toast.custom((t) => (
          <ErrorToast
            message="Failed to load data from cloud"
            onClose={() => toast.dismiss(t.id)}
          />
        ));
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Error loading from cloud';
      toast.custom((t) => (
        <ErrorToast
          message={errorMessage}
          onClose={() => toast.dismiss(t.id)}
        />
      ));
      console.error('Load from cloud error:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg">
            <Cloud className="w-6 h-6 text-blue-400" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-white">Cloud Sync Manager</h2>
            <p className="text-gray-400 text-sm">Make your changes visible to all visitors worldwide</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {isCloudEnabled ? (
            <div className="flex items-center space-x-2 px-3 py-1 bg-green-500/20 rounded-full">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-green-400 text-sm font-medium">Cloud Enabled</span>
            </div>
          ) : (
            <div className="flex items-center space-x-2 px-3 py-1 bg-gray-500/20 rounded-full">
              <CloudOff className="w-4 h-4 text-gray-400" />
              <span className="text-gray-400 text-sm font-medium">Local Only</span>
            </div>
          )}
        </div>
      </div>

      {/* Status Card */}
      <motion.div
        className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              {connectionStatus === 'connected' ? (
                <Check className="w-8 h-8 text-green-400" />
              ) : connectionStatus === 'error' ? (
                <X className="w-8 h-8 text-red-400" />
              ) : (
                <AlertTriangle className="w-8 h-8 text-yellow-400" />
              )}
            </div>
            <p className="text-sm text-gray-400">Connection Status</p>
            <p className="text-white font-medium">
              {connectionStatus === 'connected' ? 'Connected' : 
               connectionStatus === 'error' ? 'Error' : 'Not Connected'}
            </p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Globe className="w-8 h-8 text-blue-400" />
            </div>
            <p className="text-sm text-gray-400">Global Visibility</p>
            <p className="text-white font-medium">
              {isCloudEnabled ? 'Enabled' : 'Disabled'}
            </p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <RefreshCw className="w-8 h-8 text-purple-400" />
            </div>
            <p className="text-sm text-gray-400">Last Sync</p>
            <p className="text-white font-medium text-xs">
              {lastSync || 'Never'}
            </p>
          </div>
        </div>
      </motion.div>

      {/* Configuration */}
      {!isCloudEnabled && (
        <motion.div
          className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            Setup Cloud Sync
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                GitHub Personal Access Token
              </label>
              <div className="relative">
                <input
                  type={showToken ? 'text' : 'password'}
                  value={githubToken}
                  onChange={(e) => setGithubToken(e.target.value)}
                  placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent pr-12"
                />
                <button
                  type="button"
                  onClick={() => setShowToken(!showToken)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  {showToken ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              <div className="flex items-center justify-between mt-2">
                <p className="text-xs text-gray-400">
                  Create a token at GitHub → Settings → Developer settings → Personal access tokens
                </p>
                <button
                  onClick={() => setShowGuide(true)}
                  className="text-xs text-purple-400 hover:text-purple-300 underline"
                >
                  Need help?
                </button>
              </div>
            </div>
            
            <div className="space-y-3">
              <button
                onClick={handleEnableCloudSync}
                disabled={isConnecting || !githubToken || githubToken.startsWith('*')}
                className="w-full px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {isConnecting ? (
                  <>
                    <RefreshCw className="w-5 h-5 animate-spin" />
                    <span>Testing connection...</span>
                  </>
                ) : (
                  <>
                    <Cloud className="w-5 h-5" />
                    <span>Enable Cloud Sync</span>
                  </>
                )}
              </button>

              <button
                onClick={() => {
                  if (!githubToken || githubToken.startsWith('*')) {
                    toast.custom((t) => (
                      <ErrorToast
                        message="Please enter a valid GitHub token"
                        onClose={() => toast.dismiss(t.id)}
                      />
                    ));
                    return;
                  }

                  // Direct enable without testing
                  cmsService.enableCloudSync(githubToken);
                  setIsCloudEnabled(true);
                  setConnectionStatus('connected');

                  toast.custom((t) => (
                    <SuccessToast
                      message="Cloud sync enabled directly! Your changes will now be visible to all visitors."
                      onClose={() => toast.dismiss(t.id)}
                    />
                  ));

                  onDataChange();
                }}
                disabled={!githubToken || githubToken.startsWith('*')}
                className="w-full px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                <Shield className="w-5 h-5" />
                <span>⚡ Enable Directly (Skip Test)</span>
              </button>
            </div>

            {/* Token validation hint */}
            {githubToken && !githubToken.startsWith('*') && !validateGitHubToken(githubToken) && (
              <div className="mt-2 p-2 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                <p className="text-yellow-400 text-xs">
                  ⚠️ Token format appears invalid. GitHub tokens should start with 'ghp_' and be at least 40 characters long.
                </p>
              </div>
            )}
          </div>
        </motion.div>
      )}

      {/* Cloud Actions */}
      {isCloudEnabled && (
        <motion.div
          className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <h3 className="text-lg font-semibold text-white mb-4">Cloud Actions</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={handleManualSync}
              disabled={isSyncing}
              className="px-4 py-3 bg-green-500/20 border border-green-500/30 text-green-400 rounded-lg hover:bg-green-500/30 transition-all duration-200 disabled:opacity-50 flex items-center justify-center space-x-2"
            >
              {isSyncing ? (
                <RefreshCw className="w-5 h-5 animate-spin" />
              ) : (
                <Upload className="w-5 h-5" />
              )}
              <span>Sync to Cloud</span>
            </button>
            
            <button
              onClick={handleLoadFromCloud}
              disabled={isSyncing}
              className="px-4 py-3 bg-blue-500/20 border border-blue-500/30 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-all duration-200 disabled:opacity-50 flex items-center justify-center space-x-2"
            >
              {isSyncing ? (
                <RefreshCw className="w-5 h-5 animate-spin" />
              ) : (
                <Download className="w-5 h-5" />
              )}
              <span>Load from Cloud</span>
            </button>
            
            <button
              onClick={handleDisableCloudSync}
              className="px-4 py-3 bg-red-500/20 border border-red-500/30 text-red-400 rounded-lg hover:bg-red-500/30 transition-all duration-200 flex items-center justify-center space-x-2"
            >
              <CloudOff className="w-5 h-5" />
              <span>Disable Sync</span>
            </button>
          </div>
        </motion.div>
      )}

      {/* Information */}
      <motion.div
        className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <div className="flex items-start space-x-3">
          <Shield className="w-5 h-5 text-blue-400 mt-0.5" />
          <div>
            <h4 className="text-blue-400 font-medium mb-1">How Cloud Sync Works</h4>
            <ul className="text-sm text-blue-300/80 space-y-1">
              <li>• Your data is stored securely in your GitHub repository</li>
              <li>• Changes made in admin panel are instantly visible to all visitors</li>
              <li>• Data is cached for fast loading and automatically synced</li>
              <li>• Your GitHub token is stored locally and never shared</li>
            </ul>
          </div>
        </div>
      </motion.div>

      {/* GitHub Token Guide Modal */}
      {showGuide && (
        <GitHubTokenGuide onClose={() => setShowGuide(false)} />
      )}
    </div>
  );
};

export default CloudSyncManager;
