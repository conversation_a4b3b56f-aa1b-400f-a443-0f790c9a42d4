import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Palette, 
  Save, 
  Eye, 
  RefreshC<PERSON>,
  Sparkles,
  Sun,
  Moon,
  Monitor,
  Brush,
  Droplets,
  Zap,
  X
} from 'lucide-react';
import toast from 'react-hot-toast';
import { SuccessToast, ErrorToast } from '../CustomToast';

interface ThemeManagerProps {
  onDataChange: () => void;
}

interface ThemeSettings {
  colorScheme: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
  };
  animations: {
    enableParticles: boolean;
    enableSnowflakes: boolean;
    enableHoverEffects: boolean;
    enableScrollAnimations: boolean;
    animationSpeed: number;
  };
  layout: {
    containerMaxWidth: string;
    sectionSpacing: string;
    borderRadius: string;
    shadowIntensity: number;
  };
  typography: {
    fontFamily: string;
    headingWeight: string;
    bodyWeight: string;
    lineHeight: number;
  };
  effects: {
    glassmorphism: boolean;
    gradientBackgrounds: boolean;
    blurEffects: boolean;
    neonGlow: boolean;
  };
}

const ThemeManager: React.FC<ThemeManagerProps> = ({ onDataChange }) => {
  const [themeSettings, setThemeSettings] = useState<ThemeSettings>({
    colorScheme: {
      primary: '#8B5CF6',
      secondary: '#06B6D4',
      accent: '#F59E0B',
      background: '#0F172A',
      surface: '#1E293B',
      text: '#F8FAFC'
    },
    animations: {
      enableParticles: true,
      enableSnowflakes: true,
      enableHoverEffects: true,
      enableScrollAnimations: true,
      animationSpeed: 1
    },
    layout: {
      containerMaxWidth: '1200px',
      sectionSpacing: '6rem',
      borderRadius: '1rem',
      shadowIntensity: 0.3
    },
    typography: {
      fontFamily: 'Inter',
      headingWeight: '700',
      bodyWeight: '400',
      lineHeight: 1.6
    },
    effects: {
      glassmorphism: true,
      gradientBackgrounds: true,
      blurEffects: true,
      neonGlow: false
    }
  });

  const [previewMode, setPreviewMode] = useState(false);

  useEffect(() => {
    loadThemeSettings();
  }, []);

  const loadThemeSettings = () => {
    const saved = localStorage.getItem('theme_settings');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setThemeSettings({ ...themeSettings, ...parsed });
      } catch (error) {
        console.error('Error loading theme settings:', error);
      }
    }
  };

  const saveThemeSettings = () => {
    try {
      localStorage.setItem('theme_settings', JSON.stringify(themeSettings));
      applyThemeToDocument();
      onDataChange();
      
      toast(() => (
        <SuccessToast
          message="Theme settings saved successfully!"
          icon={<Save className="w-5 h-5 text-green-400" />}
        />
      ));
    } catch (error) {
      toast(() => (
        <ErrorToast
          message="Failed to save theme settings"
        />
      ));
    }
  };

  const applyThemeToDocument = () => {
    const root = document.documentElement;
    
    // Apply CSS custom properties
    root.style.setProperty('--color-primary', themeSettings.colorScheme.primary);
    root.style.setProperty('--color-secondary', themeSettings.colorScheme.secondary);
    root.style.setProperty('--color-accent', themeSettings.colorScheme.accent);
    root.style.setProperty('--color-background', themeSettings.colorScheme.background);
    root.style.setProperty('--color-surface', themeSettings.colorScheme.surface);
    root.style.setProperty('--color-text', themeSettings.colorScheme.text);
    
    root.style.setProperty('--animation-speed', themeSettings.animations.animationSpeed.toString());
    root.style.setProperty('--container-max-width', themeSettings.layout.containerMaxWidth);
    root.style.setProperty('--section-spacing', themeSettings.layout.sectionSpacing);
    root.style.setProperty('--border-radius', themeSettings.layout.borderRadius);
    root.style.setProperty('--shadow-intensity', themeSettings.layout.shadowIntensity.toString());
    
    root.style.setProperty('--font-family', themeSettings.typography.fontFamily);
    root.style.setProperty('--heading-weight', themeSettings.typography.headingWeight);
    root.style.setProperty('--body-weight', themeSettings.typography.bodyWeight);
    root.style.setProperty('--line-height', themeSettings.typography.lineHeight.toString());
  };

  const resetToDefaults = () => {
    if (window.confirm('Are you sure you want to reset theme to defaults?')) {
      localStorage.removeItem('theme_settings');
      loadThemeSettings();
      toast(() => (
        <SuccessToast
          message="Theme reset to defaults!"
          icon={<RefreshCw className="w-5 h-5 text-green-400" />}
        />
      ));
    }
  };

  const colorPresets = [
    {
      name: 'Purple Cyan',
      colors: {
        primary: '#8B5CF6',
        secondary: '#06B6D4',
        accent: '#F59E0B'
      }
    },
    {
      name: 'Blue Pink',
      colors: {
        primary: '#3B82F6',
        secondary: '#EC4899',
        accent: '#10B981'
      }
    },
    {
      name: 'Green Orange',
      colors: {
        primary: '#10B981',
        secondary: '#F97316',
        accent: '#8B5CF6'
      }
    },
    {
      name: 'Red Yellow',
      colors: {
        primary: '#EF4444',
        secondary: '#F59E0B',
        accent: '#06B6D4'
      }
    }
  ];

  const applyColorPreset = (preset: typeof colorPresets[0]) => {
    setThemeSettings({
      ...themeSettings,
      colorScheme: {
        ...themeSettings.colorScheme,
        ...preset.colors
      }
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Theme & Appearance</h1>
          <p className="text-gray-400">Customize the visual appearance and animations of your portfolio</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setPreviewMode(!previewMode)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-200 ${
              previewMode 
                ? 'bg-blue-600 text-white' 
                : 'bg-white/10 text-gray-300 hover:bg-white/20'
            }`}
          >
            <Eye className="w-4 h-4" />
            <span>{previewMode ? 'Edit Mode' : 'Preview'}</span>
          </button>
          <button
            onClick={resetToDefaults}
            className="flex items-center space-x-2 px-4 py-2 bg-orange-600/20 text-orange-400 rounded-xl hover:bg-orange-600/30 transition-all duration-200"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Reset</span>
          </button>
          <button
            onClick={saveThemeSettings}
            className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-xl transition-all duration-200"
          >
            <Save className="w-4 h-4" />
            <span>Save Theme</span>
          </button>
        </div>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Color Scheme */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
        >
          <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
            <Palette className="w-5 h-5 text-purple-400" />
            <span>Color Scheme</span>
          </h2>

          <div className="space-y-4">
            {/* Color Presets */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-3">Quick Presets</label>
              <div className="grid grid-cols-2 gap-2">
                {colorPresets.map((preset, index) => (
                  <button
                    key={index}
                    onClick={() => applyColorPreset(preset)}
                    className="flex items-center space-x-2 p-3 bg-white/5 rounded-xl hover:bg-white/10 transition-all duration-200"
                  >
                    <div className="flex space-x-1">
                      <div 
                        className="w-4 h-4 rounded-full" 
                        style={{ backgroundColor: preset.colors.primary }}
                      ></div>
                      <div 
                        className="w-4 h-4 rounded-full" 
                        style={{ backgroundColor: preset.colors.secondary }}
                      ></div>
                      <div 
                        className="w-4 h-4 rounded-full" 
                        style={{ backgroundColor: preset.colors.accent }}
                      ></div>
                    </div>
                    <span className="text-white text-sm">{preset.name}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Individual Colors */}
            <div className="space-y-3">
              {Object.entries(themeSettings.colorScheme).map(([key, value]) => (
                <div key={key}>
                  <label className="block text-sm font-medium text-gray-300 mb-2 capitalize">
                    {key} Color
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="color"
                      value={value}
                      onChange={(e) => setThemeSettings({
                        ...themeSettings,
                        colorScheme: {
                          ...themeSettings.colorScheme,
                          [key]: e.target.value
                        }
                      })}
                      className="w-12 h-10 rounded-lg border border-white/10 bg-transparent cursor-pointer"
                    />
                    <input
                      type="text"
                      value={value}
                      onChange={(e) => setThemeSettings({
                        ...themeSettings,
                        colorScheme: {
                          ...themeSettings.colorScheme,
                          [key]: e.target.value
                        }
                      })}
                      className="flex-1 px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white text-sm"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Animations & Effects */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          {/* Animation Settings */}
          <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
            <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
              <Sparkles className="w-5 h-5 text-cyan-400" />
              <span>Animations</span>
            </h2>

            <div className="space-y-4">
              {Object.entries(themeSettings.animations).map(([key, value]) => {
                if (key === 'animationSpeed') {
                  return (
                    <div key={key}>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Animation Speed: {value}x
                      </label>
                      <input
                        type="range"
                        min="0.5"
                        max="2"
                        step="0.1"
                        value={value}
                        onChange={(e) => setThemeSettings({
                          ...themeSettings,
                          animations: {
                            ...themeSettings.animations,
                            animationSpeed: parseFloat(e.target.value)
                          }
                        })}
                        className="w-full"
                      />
                    </div>
                  );
                }

                return (
                  <div key={key} className="flex items-center justify-between p-3 bg-white/5 rounded-xl">
                    <span className="text-gray-300 capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={value as boolean}
                        onChange={(e) => setThemeSettings({
                          ...themeSettings,
                          animations: {
                            ...themeSettings.animations,
                            [key]: e.target.checked
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                    </label>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Visual Effects */}
          <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
            <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
              <Zap className="w-5 h-5 text-yellow-400" />
              <span>Visual Effects</span>
            </h2>

            <div className="space-y-4">
              {Object.entries(themeSettings.effects).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between p-3 bg-white/5 rounded-xl">
                  <span className="text-gray-300 capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={value}
                      onChange={(e) => setThemeSettings({
                        ...themeSettings,
                        effects: {
                          ...themeSettings.effects,
                          [key]: e.target.checked
                        }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                  </label>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Layout & Typography */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="grid md:grid-cols-2 gap-6"
      >
        {/* Layout Settings */}
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
            <Monitor className="w-5 h-5 text-green-400" />
            <span>Layout</span>
          </h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Container Max Width</label>
              <select
                value={themeSettings.layout.containerMaxWidth}
                onChange={(e) => setThemeSettings({
                  ...themeSettings,
                  layout: { ...themeSettings.layout, containerMaxWidth: e.target.value }
                })}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white"
              >
                <option value="1024px">1024px</option>
                <option value="1200px">1200px</option>
                <option value="1400px">1400px</option>
                <option value="100%">Full Width</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Section Spacing</label>
              <select
                value={themeSettings.layout.sectionSpacing}
                onChange={(e) => setThemeSettings({
                  ...themeSettings,
                  layout: { ...themeSettings.layout, sectionSpacing: e.target.value }
                })}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white"
              >
                <option value="4rem">Compact</option>
                <option value="6rem">Normal</option>
                <option value="8rem">Spacious</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Shadow Intensity: {themeSettings.layout.shadowIntensity}
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={themeSettings.layout.shadowIntensity}
                onChange={(e) => setThemeSettings({
                  ...themeSettings,
                  layout: {
                    ...themeSettings.layout,
                    shadowIntensity: parseFloat(e.target.value)
                  }
                })}
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Typography Settings */}
        <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
          <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
            <Brush className="w-5 h-5 text-pink-400" />
            <span>Typography</span>
          </h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Font Family</label>
              <select
                value={themeSettings.typography.fontFamily}
                onChange={(e) => setThemeSettings({
                  ...themeSettings,
                  typography: { ...themeSettings.typography, fontFamily: e.target.value }
                })}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white"
              >
                <option value="Inter">Inter</option>
                <option value="Poppins">Poppins</option>
                <option value="Roboto">Roboto</option>
                <option value="Open Sans">Open Sans</option>
                <option value="Montserrat">Montserrat</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Heading Weight</label>
              <select
                value={themeSettings.typography.headingWeight}
                onChange={(e) => setThemeSettings({
                  ...themeSettings,
                  typography: { ...themeSettings.typography, headingWeight: e.target.value }
                })}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white"
              >
                <option value="600">Semi Bold</option>
                <option value="700">Bold</option>
                <option value="800">Extra Bold</option>
                <option value="900">Black</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Line Height: {themeSettings.typography.lineHeight}
              </label>
              <input
                type="range"
                min="1.2"
                max="2"
                step="0.1"
                value={themeSettings.typography.lineHeight}
                onChange={(e) => setThemeSettings({
                  ...themeSettings,
                  typography: {
                    ...themeSettings.typography,
                    lineHeight: parseFloat(e.target.value)
                  }
                })}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ThemeManager;
