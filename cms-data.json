{"personalInfo": {"name": "<PERSON><PERSON>", "title": "Full Stack Developer and UI/UX Designer", "email": "<EMAIL>", "phone": "+91-7404814726", "location": "Gurugram, India", "website": "https://nuralbhardwaj.me", "github": "https://github.com/NuralBhardwaj/", "linkedin": "https://www.linkedin.com/in/nural-bhardwaj/", "bio": "Passionate full-stack developer with expertise in modern web technologies. I love creating intuitive user experiences and robust backend solutions.", "avatar": "/Admin.png", "resume": "/NURAL_Bhardwaj_Resume.pdf"}, "projects": [{"id": "1", "title": "Advanced Portfolio Website", "description": "Modern portfolio with 3D animations, particle systems, and multi-language support.", "longDescription": "A cutting-edge portfolio website featuring advanced 3D animations, interactive particle systems, multi-language support for 7 languages, dark theme, responsive design, and optimized performance. Built with modern React ecosystem and advanced animation libraries.", "image": "https://images.pexels.com/photos/196644/pexels-photo-196644.jpeg?auto=compress&cs=tinysrgb&w=800", "technologies": ["React", "TypeScript", "Framer Motion", "Tailwind CSS", "Vite", "EmailJS"], "github": "https://github.com/NuralBhardwaj/", "live": "https://nuralbhardwaj.me", "category": "Full Stack", "featured": true, "stats": {"stars": 145, "forks": 28, "contributors": 1}, "timeline": "3 months", "status": "Completed", "createdAt": "2024-01-15", "updatedAt": "2024-12-15"}], "blogPosts": [{"id": "1", "title": "Building Modern Web Applications with React and TypeScript", "slug": "building-modern-web-apps-react-typescript", "excerpt": "Learn how to build scalable and maintainable web applications using React and TypeScript with best practices and modern tooling.", "content": "# Building Modern Web Applications\n\nIn this comprehensive guide, we'll explore how to build modern web applications...", "image": "https://images.pexels.com/photos/11035380/pexels-photo-11035380.jpeg?auto=compress&cs=tinysrgb&w=800", "author": "<PERSON><PERSON>", "category": "Development", "tags": ["React", "TypeScript", "Web Development", "Frontend"], "published": true, "featured": true, "readTime": 8, "createdAt": "2024-12-01", "updatedAt": "2025-06-14T15:37:33.311Z", "publishedAt": "2025-06-14T15:37:33.311Z"}], "skills": [{"id": "1", "name": "React", "category": "Frontend", "level": 95, "description": "Advanced React development with hooks, context, and modern patterns"}, {"id": "2", "name": "TypeScript", "category": "Frontend", "level": 90, "description": "Type-safe JavaScript development with advanced TypeScript features"}, {"id": "3", "name": "Node.js", "category": "Backend", "level": 85, "description": "Server-side JavaScript development with Express and modern frameworks"}], "experience": [{"id": "1", "company": "Tech Innovations Inc.", "position": "Senior Full Stack Developer", "startDate": "2022-01-01", "current": true, "description": "Leading development of modern web applications using React, Node.js, and cloud technologies.", "technologies": ["React", "Node.js", "TypeScript", "AWS", "MongoDB"], "achievements": ["Increased application performance by 40%", "Led a team of 5 developers", "Implemented CI/CD pipelines"]}], "education": [{"id": "1", "institution": "University of Technology", "degree": "Bachelor of Science", "field": "Computer Science", "startDate": "2018-09-01", "endDate": "2022-06-01", "gpa": "3.8", "description": "Focused on software engineering, algorithms, and web development."}], "settings": {"theme": "dark", "language": "en", "analytics": {"googleAnalytics": "G-V4VD9F3LXK", "hotjar": ""}, "seo": {"title": "<PERSON><PERSON>hardwaj - Full Stack Developer & UI/UX Designer", "description": "Passionate about creating amazing digital experiences with modern technologies and beautiful design.", "keywords": ["Full Stack Developer", "UI/UX Designer", "React", "TypeScript", "Web Development"], "ogImage": "/favicon.svg"}}}