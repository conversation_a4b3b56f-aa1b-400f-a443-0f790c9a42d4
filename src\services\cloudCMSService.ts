// Cloud CMS Service - GitHub-based global data storage
// This service enables admin changes to be visible to all users worldwide

import { CMSData, defaultCMSData } from '../data/cmsData';

interface GitHubConfig {
  owner: string;
  repo: string;
  branch: string;
  token: string;
  dataFile: string;
}

interface GitHubResponse {
  content: string;
  sha: string;
  encoding: string;
}

export class CloudCMSService {
  private static instance: CloudCMSService;
  private config: GitHubConfig;
  private cache: CMSData | null = null;
  private lastFetch: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 1000; // 1 second

  private constructor() {
    this.config = {
      owner: 'NuralBhardwaj',
      repo: 'portfolio',
      branch: 'main',
      token: '', // Will be set securely
      dataFile: 'cms-data.json'
    };

    // Initialize with secure token
    this.initializeSecureToken();
  }

  public static getInstance(): CloudCMSService {
    if (!CloudCMSService.instance) {
      CloudCMSService.instance = new CloudCMSService();
    }
    return CloudCMSService.instance;
  }

  // Initialize secure token
  private initializeSecureToken(): void {
    // Check for token in localStorage first
    const savedToken = localStorage.getItem('github_token');
    if (savedToken) {
      this.config.token = savedToken;
      return;
    }

    // For production, auto-set the secure token
    const secureToken = this.getSecureToken();
    if (secureToken) {
      this.setGitHubToken(secureToken);
    }
  }

  // Get secure token (will be set via environment or secure method)
  private getSecureToken(): string {
    // In production, this would be set via environment variables
    // For now, return empty string to require manual setup
    return '';
  }

  // Set GitHub token (called from admin panel)
  public setGitHubToken(token: string): void {
    this.config.token = token;
    localStorage.setItem('github_token', token);
  }

  // Simple connection test (fallback method)
  public async simpleConnectionTest(): Promise<{ success: boolean; message: string }> {
    try {
      const token = this.getGitHubToken();
      if (!token) {
        return { success: false, message: 'No GitHub token provided' };
      }

      // Try to fetch a simple file from the repository
      const url = `https://api.github.com/repos/${this.config.owner}/${this.config.repo}/contents/README.md`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.github.v3+json'
        }
      });

      if (response.ok || response.status === 404) {
        // 404 is also acceptable - it means we can connect but file doesn't exist
        return { success: true, message: 'GitHub connection successful' };
      } else if (response.status === 401) {
        return { success: false, message: 'Invalid GitHub token' };
      } else if (response.status === 403) {
        return { success: false, message: 'Token permissions insufficient' };
      } else {
        return { success: false, message: `GitHub API error: ${response.status}` };
      }
    } catch (error: any) {
      console.error('Simple connection test failed:', error);
      return { success: false, message: 'Network connection failed' };
    }
  }

  // Get GitHub token
  private getGitHubToken(): string {
    return this.config.token || localStorage.getItem('github_token') || '';
  }

  // Retry helper for API calls
  private async retryRequest<T>(
    requestFn: () => Promise<T>,
    retries: number = this.MAX_RETRIES
  ): Promise<T> {
    try {
      return await requestFn();
    } catch (error: any) {
      if (retries > 0 && (error.name === 'TypeError' || error.message.includes('fetch'))) {
        console.log(`Request failed, retrying... (${this.MAX_RETRIES - retries + 1}/${this.MAX_RETRIES})`);
        await new Promise(resolve => setTimeout(resolve, this.RETRY_DELAY));
        return this.retryRequest(requestFn, retries - 1);
      }
      throw error;
    }
  }

  // Check if cloud sync is enabled
  public isCloudSyncEnabled(): boolean {
    const token = this.getGitHubToken();
    // More strict validation - GitHub tokens start with 'ghp_' and are at least 40 characters
    return !!token && token.startsWith('ghp_') && token.length >= 40;
  }

  // Fetch data from GitHub
  public async fetchCloudData(): Promise<CMSData> {
    const now = Date.now();
    
    // Return cached data if still fresh
    if (this.cache && (now - this.lastFetch) < this.CACHE_DURATION) {
      return this.cache;
    }

    try {
      const token = this.getGitHubToken();
      if (!token || !token.startsWith('ghp_') || token.length < 40) {
        console.log('No valid GitHub token found, using default data');
        return defaultCMSData;
      }

      const url = `https://api.github.com/repos/${this.config.owner}/${this.config.repo}/contents/${this.config.dataFile}`;
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.github.v3+json',
          'User-Agent': 'Portfolio-CMS',
          'X-GitHub-Api-Version': '2022-11-28'
        }
      });

      if (response.status === 404) {
        // File doesn't exist yet, create it with default data
        console.log('CMS data file not found, creating with default data');
        await this.saveCloudData(defaultCMSData);
        return defaultCMSData;
      }

      if (!response.ok) {
        let errorData: any = {};
        try {
          const responseText = await response.text();
          if (responseText) {
            errorData = JSON.parse(responseText);
          }
        } catch (e) {
          // Ignore JSON parsing errors for error responses
        }
        const errorMessage = errorData.message || `HTTP ${response.status}`;
        throw new Error(`GitHub API error: ${errorMessage}`);
      }

      const responseText = await response.text();
      if (!responseText) {
        throw new Error('Empty response from GitHub API');
      }

      const data: GitHubResponse = JSON.parse(responseText);
      if (!data.content) {
        throw new Error('No content found in GitHub response');
      }

      const content = atob(data.content.replace(/\s/g, ''));
      if (!content) {
        throw new Error('Failed to decode GitHub file content');
      }

      const cmsData = JSON.parse(content);

      // Merge with default data to ensure all properties exist
      this.cache = { ...defaultCMSData, ...cmsData };
      this.lastFetch = now;

      return this.cache;
    } catch (error: any) {
      console.error('Error fetching cloud data:', error);
      // Fallback to localStorage or default data
      const localData = localStorage.getItem('nural_portfolio_cms_data');
      if (localData) {
        try {
          return { ...defaultCMSData, ...JSON.parse(localData) };
        } catch {
          return defaultCMSData;
        }
      }
      return defaultCMSData;
    }
  }

  // Save data to GitHub
  public async saveCloudData(data: CMSData): Promise<boolean> {
    try {
      const token = this.getGitHubToken();
      if (!token || !token.startsWith('ghp_') || token.length < 40) {
        console.error('No valid GitHub token available for cloud sync');
        return false;
      }

      // First, get the current file SHA (required for updates)
      const getUrl = `https://api.github.com/repos/${this.config.owner}/${this.config.repo}/contents/${this.config.dataFile}`;
      
      let sha: string | undefined;
      try {
        const getResponse = await fetch(getUrl, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Portfolio-CMS',
            'X-GitHub-Api-Version': '2022-11-28'
          }
        });

        if (getResponse.ok) {
          const fileData: GitHubResponse = await getResponse.json();
          sha = fileData.sha;
        }
      } catch (error) {
        console.log('File does not exist yet, will create new file');
      }

      // Prepare the content
      const content = btoa(JSON.stringify(data, null, 2));
      
      const updateUrl = `https://api.github.com/repos/${this.config.owner}/${this.config.repo}/contents/${this.config.dataFile}`;
      
      const updateData = {
        message: `Update CMS data - ${new Date().toISOString()}`,
        content: content,
        branch: this.config.branch,
        ...(sha && { sha })
      };

      const updateResponse = await fetch(updateUrl, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.github.v3+json',
          'Content-Type': 'application/json',
          'User-Agent': 'Portfolio-CMS',
          'X-GitHub-Api-Version': '2022-11-28'
        },
        body: JSON.stringify(updateData)
      });

      if (!updateResponse.ok) {
        const errorData = await updateResponse.json().catch(() => ({}));
        const errorMessage = errorData.message || `HTTP ${updateResponse.status}`;
        throw new Error(`GitHub API error: ${errorMessage}`);
      }

      // Update cache
      this.cache = data;
      this.lastFetch = Date.now();

      console.log('Successfully saved data to GitHub');

      // Trigger automatic website rebuild
      await this.triggerWebsiteRebuild();

      return true;
    } catch (error: any) {
      console.error('Error saving to cloud:', error);
      return false;
    }
  }

  // Sync local data to cloud
  public async syncToCloud(): Promise<boolean> {
    try {
      const localData = localStorage.getItem('nural_portfolio_cms_data');
      if (!localData) {
        console.log('No local data to sync');
        return false;
      }

      const data = JSON.parse(localData);
      const success = await this.saveCloudData(data);
      
      if (success) {
        console.log('Local data successfully synced to cloud');
      }
      
      return success;
    } catch (error: any) {
      console.error('Error syncing to cloud:', error);
      return false;
    }
  }

  // Get data with cloud fallback
  public async getData(): Promise<CMSData> {
    if (this.isCloudSyncEnabled()) {
      return await this.fetchCloudData();
    } else {
      // Fallback to localStorage
      const localData = localStorage.getItem('nural_portfolio_cms_data');
      if (localData) {
        try {
          return { ...defaultCMSData, ...JSON.parse(localData) };
        } catch {
          return defaultCMSData;
        }
      }
      return defaultCMSData;
    }
  }

  // Clear cache to force refresh
  public clearCache(): void {
    this.cache = null;
    this.lastFetch = 0;
  }

  // Test GitHub connection with multiple methods
  public async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      const token = this.getGitHubToken();
      if (!token) {
        return { success: false, message: 'No GitHub token provided' };
      }

      // Validate token format
      if (!token.startsWith('ghp_') || token.length < 40) {
        return { success: false, message: 'Invalid GitHub token format. Token should start with "ghp_" and be at least 40 characters long.' };
      }

      console.log('Testing GitHub connection...', { tokenLength: token.length });

      // Try multiple connection methods
      const results = await Promise.allSettled([
        this.testRepositoryAccess(token),
        this.testUserAccess(token),
        this.testRateLimit(token)
      ]);

      // Check if any method succeeded
      for (const result of results) {
        if (result.status === 'fulfilled' && result.value.success) {
          return result.value;
        }
      }

      // If all failed, return the most informative error
      const errors = results
        .filter(r => r.status === 'fulfilled')
        .map(r => (r as PromiseFulfilledResult<any>).value.message);

      if (errors.length > 0) {
        return { success: false, message: errors[0] };
      }

      return { success: false, message: 'Unable to connect to GitHub API. Please check your internet connection and try again.' };
    } catch (error: any) {
      console.error('Connection test error:', error);
      return { success: false, message: `Connection error: ${error.message || 'Unknown error'}` };
    }
  }

  // Test repository access
  private async testRepositoryAccess(token: string): Promise<{ success: boolean; message: string }> {
    try {
      const url = `https://api.github.com/repos/${this.config.owner}/${this.config.repo}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.github.v3+json',
          'User-Agent': 'Portfolio-CMS',
          'X-GitHub-Api-Version': '2022-11-28'
        },
        mode: 'cors'
      });

      if (response.ok) {
        const repoData = await response.json();
        console.log('Repository access confirmed:', { name: repoData.name, private: repoData.private });
        return { success: true, message: 'GitHub connection successful - Repository access confirmed' };
      } else {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.message || `HTTP ${response.status}`;

        if (response.status === 401) {
          return { success: false, message: 'Invalid GitHub token. Please check your token and try again.' };
        } else if (response.status === 403) {
          return { success: false, message: 'GitHub token does not have required permissions. Please ensure "repo" scope is selected.' };
        } else if (response.status === 404) {
          return { success: false, message: 'Repository not found. Please check the repository name and token permissions.' };
        }

        return { success: false, message: `GitHub API error: ${errorMessage}` };
      }
    } catch (error: any) {
      console.error('Repository access test failed:', error);
      throw error;
    }
  }

  // Test user access (alternative method)
  private async testUserAccess(token: string): Promise<{ success: boolean; message: string }> {
    try {
      const url = 'https://api.github.com/user';

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.github.v3+json',
          'User-Agent': 'Portfolio-CMS',
          'X-GitHub-Api-Version': '2022-11-28'
        },
        mode: 'cors'
      });

      if (response.ok) {
        const userData = await response.json();
        console.log('User access confirmed:', { login: userData.login });
        return { success: true, message: 'GitHub connection successful - User access confirmed' };
      } else {
        throw new Error(`User API failed: ${response.status}`);
      }
    } catch (error: any) {
      console.error('User access test failed:', error);
      throw error;
    }
  }

  // Test rate limit (minimal API call)
  private async testRateLimit(token: string): Promise<{ success: boolean; message: string }> {
    try {
      const url = 'https://api.github.com/rate_limit';

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.github.v3+json',
          'User-Agent': 'Portfolio-CMS',
          'X-GitHub-Api-Version': '2022-11-28'
        },
        mode: 'cors'
      });

      if (response.ok) {
        const rateLimitData = await response.json();
        console.log('Rate limit check successful:', { remaining: rateLimitData.rate.remaining });
        return { success: true, message: 'GitHub connection successful - Rate limit check passed' };
      } else {
        throw new Error(`Rate limit API failed: ${response.status}`);
      }
    } catch (error: any) {
      console.error('Rate limit test failed:', error);
      throw error;
    }
  }

  // Trigger automatic website rebuild via GitHub Actions
  private async triggerWebsiteRebuild(): Promise<void> {
    try {
      const token = this.getGitHubToken();
      if (!token || !token.startsWith('ghp_') || token.length < 40) {
        console.log('No valid token for triggering rebuild');
        return;
      }

      // Trigger repository dispatch event to start GitHub Actions workflow
      const url = `https://api.github.com/repos/${this.config.owner}/${this.config.repo}/dispatches`;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.github.v3+json',
          'Content-Type': 'application/json',
          'User-Agent': 'Portfolio-CMS',
          'X-GitHub-Api-Version': '2022-11-28'
        },
        body: JSON.stringify({
          event_type: 'cms-update',
          client_payload: {
            source: 'admin-panel',
            timestamp: new Date().toISOString(),
            message: 'CMS data updated - triggering automatic rebuild'
          }
        })
      });

      if (response.ok) {
        console.log('🚀 Website rebuild triggered successfully!');
        console.log('⏰ Your changes will be live in 2-3 minutes');

        // Show user notification
        this.showRebuildNotification();
      } else {
        console.warn('Failed to trigger website rebuild:', response.status);
      }
    } catch (error) {
      console.error('Error triggering website rebuild:', error);
    }
  }

  // Show user notification about rebuild
  private showRebuildNotification(): void {
    // Clear all caches to ensure fresh content
    this.invalidateAllCaches();

    // Create a custom event to notify the UI
    const event = new CustomEvent('websiteRebuildTriggered', {
      detail: {
        message: 'Website rebuild triggered! Changes will be live in 2-3 minutes.',
        timestamp: new Date().toISOString()
      }
    });
    window.dispatchEvent(event);
  }

  // Invalidate all caches for fresh content
  private invalidateAllCaches(): void {
    try {
      // Clear service worker caches
      if ('serviceWorker' in navigator && 'caches' in window) {
        caches.keys().then(cacheNames => {
          cacheNames.forEach(cacheName => {
            caches.delete(cacheName);
          });
        });
      }

      // Clear local storage cache
      this.clearCache();

      // Clear browser cache by adding cache-busting parameters
      const timestamp = Date.now();
      localStorage.setItem('cache_bust_timestamp', timestamp.toString());

      console.log('🗑️ All caches invalidated for fresh content');
    } catch (error) {
      console.error('Error invalidating caches:', error);
    }
  }
}

// Export singleton instance
export const cloudCMSService = CloudCMSService.getInstance();
