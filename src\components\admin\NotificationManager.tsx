import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Bell, 
  Send, 
  Users, 
  Smartphone, 
  Settings, 
  BarChart3,
  CheckCircle,
  AlertCircle,
  Clock,
  Zap
} from 'lucide-react';
import toast from 'react-hot-toast';
import { SuccessToast, ErrorToast } from '../CustomToast';
import { pushNotificationService } from '../../services/pushNotificationService';

interface NotificationTemplate {
  id: string;
  name: string;
  title: string;
  body: string;
  type: 'welcome' | 'project' | 'blog' | 'engagement' | 'custom';
  icon: string;
  actions: Array<{ action: string; title: string }>;
}

const NotificationManager: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'send' | 'templates' | 'analytics' | 'settings'>('send');
  const [notificationData, setNotificationData] = useState({
    title: '',
    body: '',
    url: '/',
    icon: '/icons/icon-192x192.png',
    requireInteraction: false,
    silent: false
  });
  
  const [templates] = useState<NotificationTemplate[]>([
    {
      id: 'welcome',
      name: 'Welcome Message',
      title: 'Welcome to Nural\'s Portfolio! 🎉',
      body: 'Thanks for installing the app! You\'ll receive updates about new projects and blog posts.',
      type: 'welcome',
      icon: '/icons/icon-192x192.png',
      actions: [
        { action: 'explore', title: 'Explore Portfolio' },
        { action: 'projects', title: 'View Projects' }
      ]
    },
    {
      id: 'project-update',
      name: 'New Project',
      title: 'New Project Added! 🚀',
      body: 'Check out my latest project and see what I\'ve been working on.',
      type: 'project',
      icon: '/icons/icon-192x192.png',
      actions: [
        { action: 'view', title: 'View Project' },
        { action: 'share', title: 'Share' }
      ]
    },
    {
      id: 'blog-post',
      name: 'New Blog Post',
      title: 'New Blog Post! 📝',
      body: 'I\'ve published a new article with insights and tutorials.',
      type: 'blog',
      icon: '/icons/icon-192x192.png',
      actions: [
        { action: 'read', title: 'Read Article' },
        { action: 'share', title: 'Share' }
      ]
    },
    {
      id: 'engagement',
      name: 'Engagement Reminder',
      title: 'Miss me? 😊',
      body: 'I\'ve added new projects and updates to my portfolio. Come check them out!',
      type: 'engagement',
      icon: '/icons/icon-192x192.png',
      actions: [
        { action: 'view', title: 'View Updates' },
        { action: 'later', title: 'Maybe Later' }
      ]
    }
  ]);

  const [analytics, setAnalytics] = useState({
    totalSubscribers: 0,
    notificationsSent: 0,
    clickRate: 0,
    engagementScore: 0
  });

  useEffect(() => {
    loadAnalytics();
  }, []);

  const loadAnalytics = () => {
    const metrics = pushNotificationService.getEngagementMetrics();
    setAnalytics({
      totalSubscribers: 1, // Simulated - in real app, get from server
      notificationsSent: metrics.notificationsSent,
      clickRate: metrics.notificationsSent > 0 ? (metrics.notificationsClicked / metrics.notificationsSent) * 100 : 0,
      engagementScore: metrics.engagementScore
    });
  };

  const handleSendNotification = async () => {
    if (!notificationData.title || !notificationData.body) {
      toast(() => (
        <ErrorToast message="Please fill in title and body" />
      ));
      return;
    }

    try {
      await pushNotificationService.sendNotification({
        title: notificationData.title,
        body: notificationData.body,
        url: notificationData.url,
        icon: notificationData.icon,
        requireInteraction: notificationData.requireInteraction,
        silent: notificationData.silent,
        actions: [
          { action: 'view', title: 'View' },
          { action: 'dismiss', title: 'Dismiss' }
        ]
      });

      toast(() => (
        <SuccessToast
          message="Notification sent successfully!"
          icon={<Bell className="w-5 h-5 text-green-400" />}
        />
      ));

      // Reset form
      setNotificationData({
        title: '',
        body: '',
        url: '/',
        icon: '/icons/icon-192x192.png',
        requireInteraction: false,
        silent: false
      });

      loadAnalytics();
    } catch (error) {
      toast(() => (
        <ErrorToast message="Failed to send notification" />
      ));
    }
  };

  const handleUseTemplate = (template: NotificationTemplate) => {
    setNotificationData({
      title: template.title,
      body: template.body,
      url: '/',
      icon: template.icon,
      requireInteraction: false,
      silent: false
    });
    setActiveTab('send');
  };

  const handleTestNotification = async () => {
    try {
      await pushNotificationService.sendNotification({
        title: 'Test Notification 🧪',
        body: 'This is a test notification from the admin panel!',
        tag: 'admin-test',
        actions: [
          { action: 'view', title: 'View Portfolio' },
          { action: 'dismiss', title: 'Dismiss' }
        ]
      });

      toast(() => (
        <SuccessToast
          message="Test notification sent!"
          icon={<Zap className="w-5 h-5 text-green-400" />}
        />
      ));
    } catch (error) {
      toast(() => (
        <ErrorToast message="Failed to send test notification" />
      ));
    }
  };

  const tabs = [
    { id: 'send', label: 'Send Notification', icon: Send },
    { id: 'templates', label: 'Templates', icon: Bell },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Push Notifications</h2>
          <p className="text-gray-400">Manage push notifications and user engagement</p>
        </div>
        <button
          onClick={handleTestNotification}
          className="px-4 py-2 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white rounded-xl transition-all duration-200 flex items-center space-x-2"
        >
          <Zap className="w-4 h-4" />
          <span>Test Notification</span>
        </button>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-blue-900/50 to-blue-800/30 border border-blue-500/30 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-300 text-sm font-medium">Subscribers</p>
              <p className="text-2xl font-bold text-white">{analytics.totalSubscribers}</p>
            </div>
            <Users className="w-8 h-8 text-blue-400" />
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-900/50 to-green-800/30 border border-green-500/30 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-300 text-sm font-medium">Sent</p>
              <p className="text-2xl font-bold text-white">{analytics.notificationsSent}</p>
            </div>
            <Send className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-900/50 to-purple-800/30 border border-purple-500/30 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-300 text-sm font-medium">Click Rate</p>
              <p className="text-2xl font-bold text-white">{analytics.clickRate.toFixed(1)}%</p>
            </div>
            <BarChart3 className="w-8 h-8 text-purple-400" />
          </div>
        </div>

        <div className="bg-gradient-to-br from-orange-900/50 to-orange-800/30 border border-orange-500/30 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-300 text-sm font-medium">Engagement</p>
              <p className="text-2xl font-bold text-white">{analytics.engagementScore}</p>
            </div>
            <Smartphone className="w-8 h-8 text-orange-400" />
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-800/50 rounded-xl p-1">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-purple-600 text-white shadow-lg'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="bg-gray-800/50 rounded-2xl border border-gray-700/50 p-6">
        {activeTab === 'send' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">Send Custom Notification</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Title</label>
                  <input
                    type="text"
                    value={notificationData.title}
                    onChange={(e) => setNotificationData({ ...notificationData, title: e.target.value })}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="Enter notification title..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Message</label>
                  <textarea
                    value={notificationData.body}
                    onChange={(e) => setNotificationData({ ...notificationData, body: e.target.value })}
                    rows={4}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                    placeholder="Enter notification message..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Target URL</label>
                  <input
                    type="text"
                    value={notificationData.url}
                    onChange={(e) => setNotificationData({ ...notificationData, url: e.target.value })}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="/"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Icon URL</label>
                  <input
                    type="text"
                    value={notificationData.icon}
                    onChange={(e) => setNotificationData({ ...notificationData, icon: e.target.value })}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="/icons/icon-192x192.png"
                  />
                </div>

                <div className="space-y-3">
                  <label className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={notificationData.requireInteraction}
                      onChange={(e) => setNotificationData({ ...notificationData, requireInteraction: e.target.checked })}
                      className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
                    />
                    <span className="text-gray-300">Require Interaction</span>
                  </label>

                  <label className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={notificationData.silent}
                      onChange={(e) => setNotificationData({ ...notificationData, silent: e.target.checked })}
                      className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
                    />
                    <span className="text-gray-300">Silent Notification</span>
                  </label>
                </div>

                <div className="pt-4">
                  <button
                    onClick={handleSendNotification}
                    className="w-full px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl transition-all duration-200 flex items-center justify-center space-x-2"
                  >
                    <Send className="w-5 h-5" />
                    <span>Send Notification</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'templates' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">Notification Templates</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {templates.map((template) => (
                <motion.div
                  key={template.id}
                  whileHover={{ scale: 1.02 }}
                  className="bg-gray-700/50 border border-gray-600/50 rounded-xl p-4 cursor-pointer hover:border-purple-500/50 transition-all duration-200"
                  onClick={() => handleUseTemplate(template)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <h4 className="font-semibold text-white">{template.name}</h4>
                    <Bell className="w-5 h-5 text-purple-400" />
                  </div>
                  <p className="text-lg font-medium text-gray-200 mb-2">{template.title}</p>
                  <p className="text-gray-400 text-sm mb-3">{template.body}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-purple-400 bg-purple-500/20 px-2 py-1 rounded-full">
                      {template.type}
                    </span>
                    <button className="text-purple-400 hover:text-purple-300 text-sm font-medium">
                      Use Template
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">Notification Analytics</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="bg-gray-700/50 rounded-xl p-4">
                  <h4 className="font-semibold text-white mb-3">Engagement Metrics</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Total Sent:</span>
                      <span className="text-white font-medium">{analytics.notificationsSent}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Click Rate:</span>
                      <span className="text-white font-medium">{analytics.clickRate.toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Engagement Score:</span>
                      <span className="text-white font-medium">{analytics.engagementScore}/100</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="bg-gray-700/50 rounded-xl p-4">
                  <h4 className="font-semibold text-white mb-3">Recent Activity</h4>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-gray-300 text-sm">Service Worker registered</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Clock className="w-4 h-4 text-blue-400" />
                      <span className="text-gray-300 text-sm">Last notification: Just now</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Users className="w-4 h-4 text-purple-400" />
                      <span className="text-gray-300 text-sm">Active subscribers: {analytics.totalSubscribers}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">Notification Settings</h3>
            
            <div className="space-y-4">
              <div className="bg-gray-700/50 rounded-xl p-4">
                <h4 className="font-semibold text-white mb-3">Push Notification Status</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Service Worker:</span>
                    <span className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-green-400">Active</span>
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Push Manager:</span>
                    <span className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-green-400">Supported</span>
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Permission:</span>
                    <span className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-green-400">Granted</span>
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-700/50 rounded-xl p-4">
                <h4 className="font-semibold text-white mb-3">Automatic Notifications</h4>
                <div className="space-y-3">
                  <label className="flex items-center justify-between">
                    <span className="text-gray-300">Welcome notifications</span>
                    <input type="checkbox" defaultChecked className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500" />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-gray-300">Project updates</span>
                    <input type="checkbox" defaultChecked className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500" />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-gray-300">Blog post notifications</span>
                    <input type="checkbox" defaultChecked className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500" />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-gray-300">Engagement reminders</span>
                    <input type="checkbox" defaultChecked className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500" />
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationManager;
