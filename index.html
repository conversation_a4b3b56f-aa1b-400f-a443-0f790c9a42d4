<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <title>🏆 Nural Bhardwaj - #1 Full Stack Developer India | 50+ Projects | Top Rated ⭐ | Hire Now</title>
    <meta name="description" content="🚀 HIRE THE BEST: Award-winning Full Stack Developer with 5+ years experience. React, Node.js, TypeScript expert. 50+ successful projects, 99% client satisfaction. Available NOW! Free consultation. Top-rated developer in India.">
    <meta name="keywords" content="Nural Bhardwaj, Full Stack Developer, UI/UX Designer, React Developer, Node.js Developer, TypeScript Developer, JavaScript Developer, Web Developer, <PERSON>end Developer, Backend Developer, Full Stack Developer India, React Developer Gurugram, Web Developer Haryana, UI/UX Designer Delhi NCR, Best Full Stack Developer, Custom Web Development, React Application Development, E-commerce Development, Mobile App Development, API Development, Database Design, Cloud Computing, DevOps Services, React.js, Next.js, Vue.js, Angular, Express.js, MongoDB, PostgreSQL, AWS, Docker, Hire Full Stack Developer, Freelance Developer, Remote Developer, Software Engineer Portfolio, Web Development Services">
    <meta name="author" content="Nural Bhardwaj">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="bingbot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <link rel="canonical" href="https://nuralbhardwaj.me">

    <!-- Advanced SEO Meta Tags -->
    <meta name="rating" content="5">
    <meta name="coverage" content="Worldwide">
    <meta name="distribution" content="Global">
    <meta name="target" content="all">
    <meta name="HandheldFriendly" content="True">
    <meta name="MobileOptimized" content="320">
    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Nural Portfolio">
    <meta name="application-name" content="Nural Portfolio">
    <meta name="msapplication-TileColor" content="#8B5CF6">
    <meta name="msapplication-config" content="/browserconfig.xml">

    <!-- PWA Icons -->
    <link rel="icon" sizes="72x72" href="/icons/icon-72x72.svg">
    <link rel="icon" sizes="96x96" href="/icons/icon-96x96.svg">
    <link rel="icon" sizes="128x128" href="/icons/icon-128x128.svg">
    <link rel="icon" sizes="144x144" href="/icons/icon-144x144.svg">
    <link rel="icon" sizes="152x152" href="/icons/icon-152x152.svg">
    <link rel="icon" sizes="192x192" href="/icons/icon-192x192.svg">
    <link rel="icon" sizes="384x384" href="/icons/icon-384x384.svg">
    <link rel="icon" sizes="512x512" href="/icons/icon-512x512.svg">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.svg">
    <link rel="apple-touch-icon" sizes="96x96" href="/icons/icon-96x96.svg">
    <link rel="apple-touch-icon" sizes="128x128" href="/icons/icon-128x128.svg">
    <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.svg">
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.svg">
    <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192x192.svg">
    <link rel="apple-touch-icon" sizes="384x384" href="/icons/icon-384x384.svg">
    <link rel="apple-touch-icon" sizes="512x512" href="/icons/icon-512x512.svg">

    <!-- Geo Tags for Local SEO -->
    <meta name="geo.region" content="IN-HR">
    <meta name="geo.placename" content="Gurugram, Haryana">
    <meta name="geo.position" content="28.4595;77.0266">
    <meta name="ICBM" content="28.4595, 77.0266">

    <!-- Language and Content Tags -->
    <meta httpEquiv="Content-Language" content="en-US">
    <meta name="language" content="English">
    <meta name="content-language" content="en-US">

    <!-- Rich Snippets -->
    <meta name="thumbnail" content="https://nuralbhardwaj.me/og-image-optimized.svg">
    <meta name="subject" content="Full Stack Development & UI/UX Design Services">
    <meta name="copyright" content="Nural Bhardwaj">
    <meta name="abstract" content="Professional portfolio of Nural Bhardwaj, Senior Full Stack Developer & UI/UX Designer with 5+ years experience and 50+ successful projects.">
    <meta name="topic" content="Web Development, Software Engineering, UI/UX Design">
    <meta name="summary" content="Top-rated Full Stack Developer specializing in React, Node.js, TypeScript. Available for hire with 99% client satisfaction rate.">
    <meta name="Classification" content="Business">
    <meta name="designer" content="Nural Bhardwaj">
    <meta name="owner" content="Nural Bhardwaj">
    <meta name="url" content="https://nuralbhardwaj.me">
    <meta name="identifier-URL" content="https://nuralbhardwaj.me">
    <meta name="directory" content="submission">
    <meta name="category" content="Technology, Web Development, Software Engineering">
    <meta name="pagename" content="Nural Bhardwaj - Professional Developer Portfolio">
    <meta name="pagetopic" content="Full Stack Development Services">
    <meta name="page-type" content="profile">

    <!-- Using system fonts for better performance and no 404 errors -->

    <!-- Enhanced Open Graph / Social Media for Maximum Engagement -->
    <meta property="og:type" content="profile">
    <meta property="og:title" content="🏆 Nural Bhardwaj - #1 Full Stack Developer India | 50+ Projects | Top Rated ⭐">
    <meta property="og:description" content="🚀 HIRE THE BEST: Award-winning Full Stack Developer with 5+ years experience. React, Node.js, TypeScript expert. 50+ successful projects, 99% client satisfaction. Available NOW! Free consultation.">
    <meta property="og:image" content="https://nuralbhardwaj.me/og-image-optimized.svg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="Nural Bhardwaj - Professional Full Stack Developer Portfolio">
    <meta property="og:url" content="https://nuralbhardwaj.me">
    <meta property="og:site_name" content="Nural Bhardwaj - Professional Developer Portfolio">
    <meta property="og:locale" content="en_US">
    <meta property="og:locale:alternate" content="hi_IN">
    <meta property="og:locale:alternate" content="en_GB">
    <meta property="profile:first_name" content="Nural">
    <meta property="profile:last_name" content="Bhardwaj">
    <meta property="profile:username" content="nuralbhardwaj">
    <meta property="profile:gender" content="male">
    <meta property="article:author" content="https://nuralbhardwaj.me">
    <meta property="article:publisher" content="https://nuralbhardwaj.me">
    <meta property="business:contact_data:street_address" content="Gurugram, Haryana">
    <meta property="business:contact_data:locality" content="Gurugram">
    <meta property="business:contact_data:region" content="Haryana">
    <meta property="business:contact_data:postal_code" content="122001">
    <meta property="business:contact_data:country_name" content="India">

    <!-- Enhanced Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@nuralbhardwaj">
    <meta name="twitter:creator" content="@nuralbhardwaj">
    <meta name="twitter:title" content="🏆 Nural Bhardwaj - #1 Full Stack Developer | 50+ Projects">
    <meta name="twitter:description" content="⭐ Top-Rated Developer | React, Node.js, TypeScript Expert | 5+ Years Experience | Available for Hire 🚀">
    <meta name="twitter:image" content="https://nuralbhardwaj.me/twitter-card-optimized.jpg">
    <meta name="twitter:image:alt" content="Nural Bhardwaj - Professional Full Stack Developer">
    <meta name="twitter:label1" content="Experience">
    <meta name="twitter:data1" content="5+ Years">
    <meta name="twitter:label2" content="Projects">
    <meta name="twitter:data2" content="50+">

    <!-- Theme color for mobile browsers and PWA -->
    <meta name="theme-color" content="#8B5CF6">
    <meta name="msapplication-navbutton-color" content="#8B5CF6">
    <meta name="apple-mobile-web-app-status-bar-style" content="#8B5CF6">

    <!-- Basic Security Headers (Note: These should ideally be set as HTTP headers) -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">

    <!-- Advanced Search Engine Verification -->
    <meta name="google-site-verification" content="YOUR_GOOGLE_VERIFICATION_CODE">
    <meta name="msvalidate.01" content="YOUR_BING_VERIFICATION_CODE">
    <meta name="yandex-verification" content="YOUR_YANDEX_VERIFICATION_CODE">
    <meta name="p:domain_verify" content="YOUR_PINTEREST_VERIFICATION_CODE">

    <!-- Advanced Analytics and Tracking (Temporarily disabled to prevent network errors) -->
    <!--
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-V4VD9F3LXK"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-V4VD9F3LXK', {
        page_title: 'Nural Bhardwaj - Professional Full Stack Developer',
        page_location: 'https://nuralbhardwaj.me',
        content_group1: 'portfolio',
        content_group2: 'professional',
        content_group3: 'developer',
        custom_map: {
          'dimension1': 'page_type',
          'dimension2': 'user_type',
          'dimension3': 'device_type'
        },
        send_page_view: true,
        anonymize_ip: true,
        allow_google_signals: true,
        allow_ad_personalization_signals: false
      });

      // Enhanced eCommerce tracking for portfolio views
      gtag('event', 'page_view', {
        page_title: 'Portfolio Homepage',
        page_location: window.location.href,
        content_group1: 'portfolio',
        custom_parameters: {
          developer_name: 'Nural Bhardwaj',
          portfolio_type: 'full_stack_developer',
          experience_years: '5+',
          project_count: '50+'
        }
      });
    </script>
    -->

    <!-- JSON-LD Structured Data for Maximum SERP Visibility -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@graph": [
        {
          "@type": "Person",
          "@id": "https://nuralbhardwaj.me#person",
          "name": "Nural Bhardwaj",
          "alternateName": ["Nural", "Bhardwaj", "NuralBhardwaj"],
          "description": "Award-winning Full Stack Developer & UI/UX Designer with 5+ years of experience and 50+ successful projects",
          "url": "https://nuralbhardwaj.me",
          "image": "https://nuralbhardwaj.me/profile-optimized.jpg",
          "sameAs": [
            "https://github.com/NuralBhardwaj",
            "https://www.linkedin.com/in/nural-bhardwaj/",
            "https://twitter.com/nuralbhardwaj"
          ],
          "jobTitle": "Senior Full Stack Developer & UI/UX Designer",
          "worksFor": {
            "@type": "Organization",
            "name": "Freelance",
            "url": "https://nuralbhardwaj.me"
          },
          "address": {
            "@type": "PostalAddress",
            "addressLocality": "Gurugram",
            "addressRegion": "Haryana",
            "addressCountry": "IN"
          },
          "email": "<EMAIL>",
          "knowsAbout": [
            "React.js", "Node.js", "TypeScript", "JavaScript", "Python", "MongoDB",
            "PostgreSQL", "AWS", "Docker", "UI/UX Design", "Web Development"
          ],
          "hasOccupation": {
            "@type": "Occupation",
            "name": "Full Stack Developer",
            "description": "Develops end-to-end web applications using modern technologies"
          }
        },
        {
          "@type": "WebSite",
          "@id": "https://nuralbhardwaj.me#website",
          "name": "Nural Bhardwaj - Professional Developer Portfolio",
          "url": "https://nuralbhardwaj.me",
          "description": "Professional portfolio showcasing 50+ successful full stack development and UI/UX design projects",
          "publisher": {
            "@id": "https://nuralbhardwaj.me#person"
          },
          "potentialAction": {
            "@type": "SearchAction",
            "target": "https://nuralbhardwaj.me/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
          }
        },
        {
          "@type": "ProfessionalService",
          "name": "Full Stack Development & UI/UX Design Services",
          "description": "Professional web development and design services",
          "provider": {
            "@id": "https://nuralbhardwaj.me#person"
          },
          "areaServed": {
            "@type": "Country",
            "name": "India"
          },
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.9",
            "reviewCount": "100+",
            "bestRating": "5"
          }
        }
      ]
    }
    </script>
  </head>
  <body>
    <!-- GitHub Pages SPA routing script -->
    <script>
      (function(l) {
        if (l.search[1] === '/' ) {
          var decoded = l.search.slice(1).split('&').map(function(s) {
            return s.replace(/~and~/g, '&')
          }).join('?');
          window.history.replaceState(null, null,
              l.pathname.slice(0, -1) + decoded + l.hash
          );
        }
      }(window.location))
    </script>

    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>