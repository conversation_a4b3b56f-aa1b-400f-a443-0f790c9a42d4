import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Eye, 
  Download, 
  Mail, 
  Globe, 
  Calendar,
  Clock,
  Star,
  Activity,
  MousePointer
} from 'lucide-react';
import { cmsService } from '../../services/cmsService';

interface AnalyticsManagerProps {
  onDataChange: () => void;
}

const AnalyticsManager: React.FC<AnalyticsManagerProps> = ({ onDataChange }) => {
  const [analytics, setAnalytics] = useState({
    totalProjects: 0,
    totalBlogPosts: 0,
    publishedPosts: 0,
    featuredProjects: 0,
    totalSkills: 0,
    totalExperience: 0,
    totalEducation: 0,
    lastUpdated: new Date().toISOString()
  });

  const [recentActivity, setRecentActivity] = useState<any[]>([]);

  useEffect(() => {
    loadAnalytics();
  }, []);

  const loadAnalytics = () => {
    const projects = cmsService.getProjects();
    const blogPosts = cmsService.getBlogPosts();
    const skills = cmsService.getSkills();
    const experience = cmsService.getExperience();
    const education = cmsService.getEducation();

    const publishedPosts = blogPosts.filter(post => post.published);
    const featuredProjects = projects.filter(project => project.featured);

    setAnalytics({
      totalProjects: projects.length,
      totalBlogPosts: blogPosts.length,
      publishedPosts: publishedPosts.length,
      featuredProjects: featuredProjects.length,
      totalSkills: skills.length,
      totalExperience: experience.length,
      totalEducation: education.length,
      lastUpdated: new Date().toISOString()
    });

    // Generate recent activity
    const activity = [
      ...projects.slice(0, 3).map(project => ({
        type: 'project',
        title: project.title,
        action: 'Updated',
        date: project.updatedAt,
        icon: Eye,
        color: 'text-blue-400'
      })),
      ...blogPosts.slice(0, 3).map(post => ({
        type: 'blog',
        title: post.title,
        action: post.published ? 'Published' : 'Drafted',
        date: post.updatedAt,
        icon: post.published ? Globe : Clock,
        color: post.published ? 'text-green-400' : 'text-yellow-400'
      }))
    ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 8);

    setRecentActivity(activity);
  };

  const statCards = [
    {
      title: 'Total Projects',
      value: analytics.totalProjects,
      icon: Eye,
      color: 'from-blue-500 to-cyan-500',
      bgColor: 'from-blue-500/20 to-cyan-500/20',
      change: '+12%',
      changeType: 'positive'
    },
    {
      title: 'Blog Posts',
      value: analytics.totalBlogPosts,
      icon: Globe,
      color: 'from-green-500 to-emerald-500',
      bgColor: 'from-green-500/20 to-emerald-500/20',
      change: '+8%',
      changeType: 'positive'
    },
    {
      title: 'Published Articles',
      value: analytics.publishedPosts,
      icon: TrendingUp,
      color: 'from-purple-500 to-pink-500',
      bgColor: 'from-purple-500/20 to-pink-500/20',
      change: '+15%',
      changeType: 'positive'
    },
    {
      title: 'Featured Projects',
      value: analytics.featuredProjects,
      icon: Star,
      color: 'from-yellow-500 to-orange-500',
      bgColor: 'from-yellow-500/20 to-orange-500/20',
      change: '+5%',
      changeType: 'positive'
    },
    {
      title: 'Skills',
      value: analytics.totalSkills,
      icon: Activity,
      color: 'from-indigo-500 to-purple-500',
      bgColor: 'from-indigo-500/20 to-purple-500/20',
      change: '+3%',
      changeType: 'positive'
    },
    {
      title: 'Experience',
      value: analytics.totalExperience,
      icon: Users,
      color: 'from-red-500 to-pink-500',
      bgColor: 'from-red-500/20 to-pink-500/20',
      change: '+1%',
      changeType: 'positive'
    }
  ];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Analytics Dashboard</h1>
          <p className="text-gray-400">Overview of your portfolio performance and content</p>
        </div>
        <button
          onClick={loadAnalytics}
          className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-xl transition-all duration-200"
        >
          <BarChart3 className="w-4 h-4" />
          <span>Refresh Data</span>
        </button>
      </div>

      {/* Stats Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`relative p-6 bg-gradient-to-br ${stat.bgColor} backdrop-blur-sm rounded-2xl border border-white/10 hover:border-purple-500/30 transition-all duration-300 group`}
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
              <div className={`text-sm font-medium ${
                stat.changeType === 'positive' ? 'text-green-400' : 'text-red-400'
              }`}>
                {stat.change}
              </div>
            </div>
            
            <div className="space-y-1">
              <h3 className="text-2xl font-bold text-white">{stat.value}</h3>
              <p className="text-gray-400 text-sm">{stat.title}</p>
            </div>

            <div className={`absolute -inset-1 bg-gradient-to-r ${stat.color} opacity-0 group-hover:opacity-20 blur-lg transition-opacity duration-500 rounded-2xl`}></div>
          </motion.div>
        ))}
      </div>

      {/* Content Overview */}
      <div className="grid lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
        >
          <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
            <Activity className="w-5 h-5 text-purple-400" />
            <span>Recent Activity</span>
          </h2>
          
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div
                key={index}
                className="flex items-center space-x-4 p-3 bg-white/5 rounded-xl hover:bg-white/10 transition-all duration-200"
              >
                <div className={`w-10 h-10 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-lg flex items-center justify-center`}>
                  <activity.icon className={`w-5 h-5 ${activity.color}`} />
                </div>
                <div className="flex-1">
                  <p className="text-white font-medium">{activity.title}</p>
                  <p className="text-gray-400 text-sm">{activity.action} • {formatDate(activity.date)}</p>
                </div>
                <span className="text-xs text-gray-500 bg-white/5 px-2 py-1 rounded capitalize">
                  {activity.type}
                </span>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Quick Insights */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
        >
          <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-green-400" />
            <span>Quick Insights</span>
          </h2>
          
          <div className="space-y-4">
            <div className="p-4 bg-gradient-to-r from-blue-600/20 to-cyan-600/20 rounded-xl border border-blue-500/30">
              <h3 className="text-white font-semibold mb-2">Content Distribution</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Projects</span>
                  <span className="text-blue-400">{analytics.totalProjects}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Blog Posts</span>
                  <span className="text-green-400">{analytics.totalBlogPosts}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Skills</span>
                  <span className="text-purple-400">{analytics.totalSkills}</span>
                </div>
              </div>
            </div>

            <div className="p-4 bg-gradient-to-r from-green-600/20 to-emerald-600/20 rounded-xl border border-green-500/30">
              <h3 className="text-white font-semibold mb-2">Publication Status</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Published Posts</span>
                  <span className="text-green-400">{analytics.publishedPosts}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Draft Posts</span>
                  <span className="text-yellow-400">{analytics.totalBlogPosts - analytics.publishedPosts}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Featured Projects</span>
                  <span className="text-orange-400">{analytics.featuredProjects}</span>
                </div>
              </div>
            </div>

            <div className="p-4 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-xl border border-purple-500/30">
              <h3 className="text-white font-semibold mb-2">Portfolio Health</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Completion Rate</span>
                  <span className="text-green-400">95%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">SEO Score</span>
                  <span className="text-blue-400">88%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Last Updated</span>
                  <span className="text-gray-400">{formatDate(analytics.lastUpdated)}</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Performance Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
      >
        <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
          <MousePointer className="w-5 h-5 text-cyan-400" />
          <span>Performance Overview</span>
        </h2>
        
        <div className="grid md:grid-cols-4 gap-4">
          {[
            { label: 'Page Load Time', value: '1.2s', status: 'excellent', color: 'text-green-400' },
            { label: 'SEO Score', value: '95/100', status: 'good', color: 'text-blue-400' },
            { label: 'Accessibility', value: '98%', status: 'excellent', color: 'text-green-400' },
            { label: 'Performance', value: '92%', status: 'good', color: 'text-yellow-400' }
          ].map((metric, index) => (
            <div key={metric.label} className="text-center p-4 bg-white/5 rounded-xl">
              <div className={`text-2xl font-bold ${metric.color} mb-1`}>{metric.value}</div>
              <div className="text-gray-400 text-sm">{metric.label}</div>
              <div className={`text-xs mt-1 px-2 py-1 rounded-full ${
                metric.status === 'excellent' ? 'bg-green-500/20 text-green-400' :
                metric.status === 'good' ? 'bg-blue-500/20 text-blue-400' :
                'bg-yellow-500/20 text-yellow-400'
              }`}>
                {metric.status}
              </div>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default AnalyticsManager;
