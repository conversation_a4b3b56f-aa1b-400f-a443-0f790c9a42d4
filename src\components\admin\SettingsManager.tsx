import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Settings,
  Save,
  Download,
  Upload,
  Trash2,
  RefreshCw,
  Globe,
  Palette,
  BarChart3,
  Shield,
  Database,
  FileText,
  X
} from 'lucide-react';
import { cmsService } from '../../services/cmsService';
import BackupManager from './BackupManager';
import toast from 'react-hot-toast';
import { SuccessToast, ErrorToast } from '../CustomToast';

interface SettingsManagerProps {
  onDataChange: () => void;
}

const SettingsManager: React.FC<SettingsManagerProps> = ({ onDataChange }) => {
  const [settings, setSettings] = useState({
    theme: 'dark' as 'dark' | 'light',
    language: 'en',
    analytics: {
      googleAnalytics: '',
      hotjar: ''
    },
    seo: {
      title: '',
      description: '',
      keywords: [] as string[],
      ogImage: ''
    }
  });
  const [keywordInput, setKeywordInput] = useState('');
  const [activeTab, setActiveTab] = useState<'general' | 'seo' | 'analytics' | 'data' | 'backup'>('general');

  useEffect(() => {
    const currentSettings = cmsService.getSettings();
    setSettings(currentSettings);
  }, []);

  const handleSaveSettings = () => {
    try {
      cmsService.updateSettings(settings);
      onDataChange();
      toast(() => (
        <SuccessToast
          message="Settings saved successfully!"
          icon={<Save className="w-5 h-5 text-green-400" />}
        />
      ));
    } catch (error) {
      toast(() => (
        <ErrorToast
          message="Failed to save settings"
          icon={<X className="w-5 h-5 text-red-400" />}
        />
      ));
    }
  };

  const handleExportData = () => {
    try {
      const data = cmsService.exportData();
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `portfolio-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast(() => (
        <SuccessToast
          message="Data exported successfully!"
          icon={<Download className="w-5 h-5 text-green-400" />}
        />
      ));
    } catch (error) {
      toast(() => (
        <ErrorToast
          message="Failed to export data"
          icon={<Download className="w-5 h-5 text-red-400" />}
        />
      ));
    }
  };

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const jsonData = e.target?.result as string;
        const success = cmsService.importData(jsonData);
        
        if (success) {
          onDataChange();
          setSettings(cmsService.getSettings());
          toast(() => (
            <SuccessToast
              message="Data imported successfully!"
              icon={<Upload className="w-5 h-5 text-green-400" />}
            />
          ));
        } else {
          throw new Error('Invalid data format');
        }
      } catch (error) {
        toast(() => (
          <ErrorToast
            message="Failed to import data. Please check the file format."
            icon={<Upload className="w-5 h-5 text-red-400" />}
          />
        ));
      }
    };
    reader.readAsText(file);
    
    // Reset the input
    event.target.value = '';
  };

  const handleResetData = () => {
    if (window.confirm('Are you sure you want to reset all data to defaults? This action cannot be undone.')) {
      try {
        cmsService.resetToDefaults();
        onDataChange();
        setSettings(cmsService.getSettings());
        toast(() => (
          <SuccessToast
            message="Data reset to defaults successfully!"
            icon={<RefreshCw className="w-5 h-5 text-green-400" />}
          />
        ));
      } catch (error) {
        toast(() => (
          <ErrorToast
            message="Failed to reset data"
            icon={<RefreshCw className="w-5 h-5 text-red-400" />}
          />
        ));
      }
    }
  };

  const addKeyword = () => {
    if (keywordInput.trim() && !settings.seo.keywords.includes(keywordInput.trim())) {
      setSettings({
        ...settings,
        seo: {
          ...settings.seo,
          keywords: [...settings.seo.keywords, keywordInput.trim()]
        }
      });
      setKeywordInput('');
    }
  };

  const removeKeyword = (keyword: string) => {
    setSettings({
      ...settings,
      seo: {
        ...settings.seo,
        keywords: settings.seo.keywords.filter(k => k !== keyword)
      }
    });
  };

  const tabs = [
    { id: 'general', label: 'General', icon: Settings },
    { id: 'seo', label: 'SEO', icon: Globe },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'data', label: 'Data Management', icon: Database },
    { id: 'backup', label: 'Backup & Restore', icon: Shield },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">Settings</h1>
        <p className="text-gray-400">Configure your portfolio settings and preferences</p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-2 bg-white/5 backdrop-blur-sm rounded-2xl p-2 border border-white/10">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-gradient-to-r from-purple-600 to-cyan-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-white/5'
            }`}
          >
            <tab.icon className="w-5 h-5" />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* General Settings */}
      {activeTab === 'general' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10"
        >
          <h2 className="text-2xl font-bold text-white mb-6">General Settings</h2>
          
          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              {/* Theme */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Theme
                </label>
                <select
                  value={settings.theme}
                  onChange={(e) => setSettings({ ...settings, theme: e.target.value as 'dark' | 'light' })}
                  className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                >
                  <option value="dark" className="bg-gray-800">Dark Theme</option>
                  <option value="light" className="bg-gray-800">Light Theme</option>
                </select>
              </div>

              {/* Language */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Default Language
                </label>
                <select
                  value={settings.language}
                  onChange={(e) => setSettings({ ...settings, language: e.target.value })}
                  className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                >
                  <option value="en" className="bg-gray-800">English</option>
                  <option value="es" className="bg-gray-800">Spanish</option>
                  <option value="fr" className="bg-gray-800">French</option>
                  <option value="de" className="bg-gray-800">German</option>
                  <option value="zh" className="bg-gray-800">Chinese</option>
                  <option value="ja" className="bg-gray-800">Japanese</option>
                  <option value="ar" className="bg-gray-800">Arabic</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleSaveSettings}
                className="px-8 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 flex items-center space-x-2"
              >
                <Save className="w-5 h-5" />
                <span>Save Settings</span>
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* SEO Settings */}
      {activeTab === 'seo' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10"
        >
          <h2 className="text-2xl font-bold text-white mb-6">SEO Configuration</h2>
          
          <div className="space-y-6">
            {/* SEO Title */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                SEO Title
              </label>
              <input
                type="text"
                value={settings.seo.title}
                onChange={(e) => setSettings({
                  ...settings,
                  seo: { ...settings.seo, title: e.target.value }
                })}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                placeholder="Your Portfolio - Full Stack Developer"
              />
            </div>

            {/* SEO Description */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Meta Description
              </label>
              <textarea
                value={settings.seo.description}
                onChange={(e) => setSettings({
                  ...settings,
                  seo: { ...settings.seo, description: e.target.value }
                })}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                placeholder="Brief description for search engines"
                rows={3}
              />
            </div>

            {/* Keywords */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                SEO Keywords
              </label>
              <div className="flex space-x-2 mb-3">
                <input
                  type="text"
                  value={keywordInput}
                  onChange={(e) => setKeywordInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addKeyword())}
                  className="flex-1 px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                  placeholder="Add keyword (press Enter)"
                />
                <button
                  type="button"
                  onClick={addKeyword}
                  className="px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-xl transition-all duration-200"
                >
                  Add
                </button>
              </div>
              <div className="flex flex-wrap gap-2">
                {settings.seo.keywords.map((keyword) => (
                  <span
                    key={keyword}
                    className="px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm flex items-center space-x-2 border border-purple-500/30"
                  >
                    <span>{keyword}</span>
                    <button
                      type="button"
                      onClick={() => removeKeyword(keyword)}
                      className="text-purple-400 hover:text-red-400 transition-colors duration-200"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>

            {/* OG Image */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Open Graph Image URL
              </label>
              <input
                type="url"
                value={settings.seo.ogImage}
                onChange={(e) => setSettings({
                  ...settings,
                  seo: { ...settings.seo, ogImage: e.target.value }
                })}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                placeholder="https://example.com/og-image.jpg"
              />
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleSaveSettings}
                className="px-8 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 flex items-center space-x-2"
              >
                <Save className="w-5 h-5" />
                <span>Save SEO Settings</span>
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* Analytics Settings */}
      {activeTab === 'analytics' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10"
        >
          <h2 className="text-2xl font-bold text-white mb-6">Analytics Configuration</h2>
          
          <div className="space-y-6">
            {/* Google Analytics */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Google Analytics Tracking ID
              </label>
              <input
                type="text"
                value={settings.analytics.googleAnalytics}
                onChange={(e) => setSettings({
                  ...settings,
                  analytics: { ...settings.analytics, googleAnalytics: e.target.value }
                })}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                placeholder="G-XXXXXXXXXX"
              />
            </div>

            {/* Hotjar */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Hotjar Site ID
              </label>
              <input
                type="text"
                value={settings.analytics.hotjar}
                onChange={(e) => setSettings({
                  ...settings,
                  analytics: { ...settings.analytics, hotjar: e.target.value }
                })}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                placeholder="1234567"
              />
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleSaveSettings}
                className="px-8 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 flex items-center space-x-2"
              >
                <Save className="w-5 h-5" />
                <span>Save Analytics Settings</span>
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* Data Management */}
      {activeTab === 'data' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10"
        >
          <h2 className="text-2xl font-bold text-white mb-6">Data Management</h2>
          
          <div className="space-y-6">
            {/* Export Data */}
            <div className="bg-white/5 rounded-xl p-6 border border-white/10">
              <div className="flex items-center space-x-3 mb-3">
                <Download className="w-6 h-6 text-green-400" />
                <h3 className="text-lg font-semibold text-white">Export Data</h3>
              </div>
              <p className="text-gray-400 mb-4">
                Download a backup of all your portfolio data including projects, blog posts, and settings.
              </p>
              <button
                onClick={handleExportData}
                className="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl font-medium transition-all duration-200 flex items-center space-x-2"
              >
                <Download className="w-5 h-5" />
                <span>Export All Data</span>
              </button>
            </div>

            {/* Import Data */}
            <div className="bg-white/5 rounded-xl p-6 border border-white/10">
              <div className="flex items-center space-x-3 mb-3">
                <Upload className="w-6 h-6 text-blue-400" />
                <h3 className="text-lg font-semibold text-white">Import Data</h3>
              </div>
              <p className="text-gray-400 mb-4">
                Restore your portfolio data from a previously exported backup file.
              </p>
              <div className="flex items-center space-x-4">
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImportData}
                  className="hidden"
                  id="import-file"
                />
                <label
                  htmlFor="import-file"
                  className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-medium transition-all duration-200 cursor-pointer flex items-center space-x-2"
                >
                  <Upload className="w-5 h-5" />
                  <span>Choose File to Import</span>
                </label>
              </div>
            </div>

            {/* Reset Data */}
            <div className="bg-red-500/10 rounded-xl p-6 border border-red-500/20">
              <div className="flex items-center space-x-3 mb-3">
                <RefreshCw className="w-6 h-6 text-red-400" />
                <h3 className="text-lg font-semibold text-white">Reset to Defaults</h3>
              </div>
              <p className="text-gray-400 mb-4">
                Reset all data to default values. This action cannot be undone.
              </p>
              <button
                onClick={handleResetData}
                className="px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white rounded-xl font-medium transition-all duration-200 flex items-center space-x-2"
              >
                <RefreshCw className="w-5 h-5" />
                <span>Reset All Data</span>
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* Backup Tab */}
      {activeTab === 'backup' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <BackupManager onDataChange={onDataChange} />
        </motion.div>
      )}
    </div>
  );
};

export default SettingsManager;
