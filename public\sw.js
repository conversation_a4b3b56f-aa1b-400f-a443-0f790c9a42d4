// Advanced Service Worker for Nural Bhardwaj Portfolio
// Version 3.1.0 - Enhanced Performance Optimization with Advanced Caching

const VERSION = '3.1.0';
const CACHE_PREFIX = 'nural-portfolio';
const STATIC_CACHE = `${CACHE_PREFIX}-static-v${VERSION}`;
const DYNAMIC_CACHE = `${CACHE_PREFIX}-dynamic-v${VERSION}`;
const IMAGE_CACHE = `${CACHE_PREFIX}-images-v${VERSION}`;
const API_CACHE = `${CACHE_PREFIX}-api-v${VERSION}`;
const FONT_CACHE = `${CACHE_PREFIX}-fonts-v${VERSION}`;
const CDN_CACHE = `${CACHE_PREFIX}-cdn-v${VERSION}`;
const PERFORMANCE_CACHE = `${CACHE_PREFIX}-performance-v${VERSION}`;

// Cache configuration
const CACHE_CONFIG = {
  static: {
    maxAge: 365 * 24 * 60 * 60 * 1000, // 1 year
    maxEntries: 100
  },
  dynamic: {
    maxAge: 7 * 24 * 60 * 60 * 1000, // 1 week
    maxEntries: 50
  },
  images: {
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    maxEntries: 200
  },
  api: {
    maxAge: 5 * 60 * 1000, // 5 minutes
    maxEntries: 20
  },
  fonts: {
    maxAge: 365 * 24 * 60 * 60 * 1000, // 1 year
    maxEntries: 30
  },
  cdn: {
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    maxEntries: 100
  }
};

// Critical resources for immediate caching (only essential files)
const CRITICAL_RESOURCES = [
  '/',
  '/index.html',
  '/manifest.json'
];

// Advanced cache patterns with strategy mapping
const CACHE_STRATEGIES = {
  // Static assets - Cache First
  static: {
    patterns: [
      /\.(?:css|js|woff2?|ttf|eot)$/,
      /\/assets\//,
      /\/static\//
    ],
    strategy: 'cacheFirst',
    cacheName: STATIC_CACHE
  },

  // Images - Cache First with WebP/AVIF support
  images: {
    patterns: [
      /\.(?:png|jpg|jpeg|gif|svg|webp|avif|ico)$/,
      /\/images\//,
      /\/photos\//,
      /unsplash\.com/,
      /placeholder\.com/
    ],
    strategy: 'cacheFirst',
    cacheName: IMAGE_CACHE
  },

  // Fonts - Cache First (long-term)
  fonts: {
    patterns: [
      /fonts\.googleapis\.com/,
      /fonts\.gstatic\.com/,
      /\.(?:woff2?|ttf|eot|otf)$/
    ],
    strategy: 'cacheFirst',
    cacheName: FONT_CACHE
  },

  // CDN Resources - Stale While Revalidate
  cdn: {
    patterns: [
      /cdn\.jsdelivr\.net/,
      /unpkg\.com/,
      /cdnjs\.cloudflare\.com/,
      /ajax\.googleapis\.com/
    ],
    strategy: 'staleWhileRevalidate',
    cacheName: CDN_CACHE
  },

  // API calls - Network First
  api: {
    patterns: [
      /\/api\//,
      /api\.emailjs\.com/,
      /api\.github\.com/,
      /analytics/,
      /ipapi\.co/,
      /googletagmanager\.com/
    ],
    strategy: 'networkFirst',
    cacheName: API_CACHE
  },

  // HTML pages - Network First
  pages: {
    patterns: [
      /\.html$/,
      /\/$/
    ],
    strategy: 'networkFirst',
    cacheName: DYNAMIC_CACHE
  }
};

// Install event - cache critical resources
self.addEventListener('install', (event) => {
  console.log('[SW] Installing Advanced Service Worker v' + VERSION);

  event.waitUntil(
    Promise.allSettled([
      // Cache critical resources immediately with error handling
      caches.open(STATIC_CACHE).then(async cache => {
        console.log('[SW] Caching critical resources');
        try {
          await cache.addAll(CRITICAL_RESOURCES);
          console.log('[SW] Critical resources cached successfully');
        } catch (error) {
          console.warn('[SW] Some critical resources failed to cache:', error);
          // Try to cache resources individually
          for (const resource of CRITICAL_RESOURCES) {
            try {
              const response = await fetch(resource);
              if (response.ok) {
                await cache.put(resource, response);
                console.log('[SW] Cached individually:', resource);
              }
            } catch (err) {
              console.warn('[SW] Failed to cache:', resource, err);
            }
          }
        }
      }),

      // Preload and cache important assets
      preloadCriticalAssets(),

      // Initialize cache metadata
      initializeCacheMetadata()
    ]).then(() => {
      console.log('[SW] Installation complete');
      return self.skipWaiting();
    }).catch((error) => {
      console.error('[SW] Installation failed:', error);
      // Continue anyway - don't block installation
      return self.skipWaiting();
    })
  );
});

// Preload critical assets for better performance
async function preloadCriticalAssets() {
  // Skip preloading non-existent assets to prevent errors
  console.log('[SW] Skipping asset preloading to prevent installation errors');
  return Promise.resolve();
}

// Initialize cache metadata for tracking
async function initializeCacheMetadata() {
  const metadata = {
    version: VERSION,
    timestamp: Date.now(),
    caches: Object.keys(CACHE_STRATEGIES)
  };

  try {
    await self.registration.sync.register('cache-metadata');
    console.log('[SW] Cache metadata initialized');
  } catch (error) {
    console.warn('[SW] Background sync not available');
  }
}

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating Service Worker...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Service Worker activated');
        return self.clients.claim();
      })
  );
});

// Advanced fetch event with multiple caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') return;

  // Skip chrome-extension and other non-http(s) requests
  if (!url.protocol.startsWith('http')) return;

  // Skip external URLs that cause CORS/CSP issues
  if (url.hostname === 'github.com' ||
      url.hostname === 'linkedin.com' ||
      url.hostname === 'www.linkedin.com' ||
      request.url.startsWith('mailto:') ||
      request.url.startsWith('tel:')) {
    return;
  }

  // Debug logging for all document requests
  if (request.destination === 'document') {
    console.log('[SW] Document request:', {
      url: request.url,
      pathname: url.pathname,
      origin: url.origin,
      selfOrigin: self.location.origin,
      isOwnOrigin: url.origin === self.location.origin,
      hasExtension: url.pathname.includes('.'),
      isRoot: url.pathname === '/'
    });
  }

  // Handle SPA routes FIRST - before any caching strategy
  if (request.destination === 'document' && url.origin === self.location.origin) {
    const pathname = url.pathname;

    // Check if it's a SPA route (not a file with extension and not root)
    if (!pathname.includes('.') && pathname !== '/') {
      console.log('[SW] ✅ SPA route detected:', pathname);
      // For SPA routes like /admin, /projects, etc., serve index.html
      event.respondWith(
        caches.match('/index.html')
          .then(cachedIndex => {
            if (cachedIndex) {
              console.log('[SW] ✅ Serving cached index.html for SPA route:', pathname);
              return cachedIndex;
            }
            // If no cached index.html, fetch it and cache it
            console.log('[SW] ⚠️ No cached index.html, fetching fresh for SPA route:', pathname);
            return fetch('/')
              .then(async response => {
                if (response.ok) {
                  console.log('[SW] ✅ Fetched fresh index.html for SPA route:', pathname);
                  // Cache the response for future use
                  const cache = await caches.open(STATIC_CACHE);
                  await cache.put('/index.html', response.clone());
                  await cache.put('/', response.clone());
                  return response;
                }
                throw new Error('Failed to fetch index.html');
              });
          })
          .catch(error => {
            console.error('[SW] ❌ Failed to serve SPA route:', pathname, error);
            // Return a basic HTML response as last resort
            return new Response(
              '<!DOCTYPE html><html><head><title>Loading...</title></head><body><script>window.location.reload();</script></body></html>',
              { headers: { 'Content-Type': 'text/html' } }
            );
          })
      );
      return;
    } else {
      console.log('[SW] ⚠️ Not a SPA route:', pathname, 'hasExtension:', pathname.includes('.'), 'isRoot:', pathname === '/');
    }
  }

  // Determine caching strategy based on request
  const strategy = getCachingStrategy(request);

  if (strategy) {
    event.respondWith(handleRequest(request, strategy));
  }
});

// Determine the appropriate caching strategy for a request
function getCachingStrategy(request) {
  const url = request.url;

  for (const [name, config] of Object.entries(CACHE_STRATEGIES)) {
    if (config.patterns.some(pattern => pattern.test(url))) {
      return { ...config, name };
    }
  }

  // Default strategy for unmatched requests
  return {
    strategy: 'networkFirst',
    cacheName: DYNAMIC_CACHE,
    name: 'default'
  };
}

// Handle request based on caching strategy
async function handleRequest(request, strategy) {
  switch (strategy.strategy) {
    case 'cacheFirst':
      return cacheFirst(request, strategy);
    case 'networkFirst':
      return networkFirst(request, strategy);
    case 'staleWhileRevalidate':
      return staleWhileRevalidate(request, strategy);
    case 'networkOnly':
      return networkOnly(request);
    case 'cacheOnly':
      return cacheOnly(request, strategy);
    default:
      return networkFirst(request, strategy);
  }
}

// Cache First Strategy - Best for static assets
async function cacheFirst(request, strategy) {
  try {
    const cachedResponse = await caches.match(request);

    if (cachedResponse) {
      // Check if cache is still valid
      const cacheTime = await getCacheTime(request, strategy.cacheName);
      const maxAge = CACHE_CONFIG[strategy.name]?.maxAge || CACHE_CONFIG.dynamic.maxAge;

      if (Date.now() - cacheTime < maxAge) {
        console.log(`[SW] Cache hit (${strategy.name}):`, request.url);
        return cachedResponse;
      }
    }

    // Fetch from network and cache
    const networkResponse = await fetch(request);

    if (networkResponse && networkResponse.status === 200) {
      await cacheResponse(request, networkResponse.clone(), strategy);
      console.log(`[SW] Network fetch and cache (${strategy.name}):`, request.url);
    }

    return networkResponse;
  } catch (error) {
    console.error(`[SW] Cache first failed for ${request.url}:`, error);

    // Fallback to cache if network fails
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Return offline fallback for navigation requests
    if (request.destination === 'document') {
      return caches.match('/index.html');
    }

    throw error;
  }
}

// Network First Strategy - Best for dynamic content
async function networkFirst(request, strategy) {
  try {
    const networkResponse = await fetch(request);

    if (networkResponse && networkResponse.status === 200) {
      await cacheResponse(request, networkResponse.clone(), strategy);
      console.log(`[SW] Network first success (${strategy.name}):`, request.url);
      return networkResponse;
    }

    throw new Error('Network response not ok');
  } catch (error) {
    // Check if this is a SPA route that should serve index.html
    const url = new URL(request.url);
    const isOwnOrigin = url.origin === self.location.origin;
    const isSPARoute = isOwnOrigin && !url.pathname.includes('.') && url.pathname !== '/';

    // Only log warnings for important requests, not external APIs or SPA routes
    const isExternalAPI = request.url.includes('googletagmanager.com') ||
                         request.url.includes('api.github.com');

    if (!isExternalAPI && !isSPARoute) {
      console.warn(`[SW] Network first fallback to cache (${strategy.name}):`, request.url);
    }

    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Return offline fallback for navigation requests and SPA routes
    if (request.destination === 'document' || isSPARoute) {
      const indexResponse = await caches.match('/index.html');
      if (indexResponse) {
        return indexResponse;
      }
      // If no cached index.html, try to fetch it
      try {
        const freshIndex = await fetch('/index.html');
        if (freshIndex.ok) {
          return freshIndex;
        }
      } catch (fetchError) {
        console.warn('[SW] Failed to fetch fresh index.html:', fetchError);
      }
    }

    // Don't throw errors for external APIs to prevent console spam
    if (isExternalAPI) {
      return new Response(null, {
        status: 204,
        statusText: 'No Content'
      });
    }

    // For SPA routes, return a basic HTML response as last resort
    if (isSPARoute) {
      return new Response(
        '<!DOCTYPE html><html><head><title>Loading...</title></head><body><script>window.location.reload();</script></body></html>',
        { headers: { 'Content-Type': 'text/html' } }
      );
    }

    throw error;
  }
}

// Stale While Revalidate Strategy - Best for CDN resources
async function staleWhileRevalidate(request, strategy) {
  const cachedResponse = await caches.match(request);

  // Always try to fetch fresh content in background
  const fetchPromise = fetch(request).then(async (networkResponse) => {
    if (networkResponse && networkResponse.status === 200) {
      await cacheResponse(request, networkResponse.clone(), strategy);
      console.log(`[SW] Background update (${strategy.name}):`, request.url);
    }
    return networkResponse;
  }).catch(error => {
    console.warn(`[SW] Background fetch failed (${strategy.name}):`, request.url, error);
  });

  // Return cached version immediately if available
  if (cachedResponse) {
    console.log(`[SW] Stale while revalidate cache hit (${strategy.name}):`, request.url);
    return cachedResponse;
  }

  // If no cache, wait for network
  try {
    return await fetchPromise;
  } catch (error) {
    console.error(`[SW] Stale while revalidate failed (${strategy.name}):`, request.url, error);
    throw error;
  }
}

// Network Only Strategy
async function networkOnly(request) {
  return fetch(request);
}

// Cache Only Strategy
async function cacheOnly(request, strategy) {
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }
  throw new Error('No cached response available');
}

// Push notification event
self.addEventListener('push', (event) => {
  console.log('[SW] Push notification received');
  
  let notificationData = {
    title: 'Nural Bhardwaj Portfolio',
    body: 'Thanks for visiting my portfolio!',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    tag: 'portfolio-notification',
    requireInteraction: false,
    silent: false
  };

  // Parse push data if available
  if (event.data) {
    try {
      const data = event.data.json();
      notificationData = { ...notificationData, ...data };
    } catch (error) {
      console.error('[SW] Error parsing push data:', error);
      notificationData.body = event.data.text() || notificationData.body;
    }
  }

  // Enhanced notification options
  const notificationOptions = {
    body: notificationData.body,
    icon: notificationData.icon,
    badge: notificationData.badge,
    tag: notificationData.tag,
    requireInteraction: notificationData.requireInteraction,
    silent: notificationData.silent,
    vibrate: [200, 100, 200],
    data: {
      url: notificationData.url || '/',
      timestamp: Date.now(),
      source: 'portfolio-pwa'
    },
    actions: [
      {
        action: 'view',
        title: 'View Portfolio',
        icon: '/icons/action-view.png'
      },
      {
        action: 'contact',
        title: 'Contact Me',
        icon: '/icons/action-contact.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/icons/action-dismiss.png'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification(notificationData.title, notificationOptions)
      .then(() => {
        console.log('[SW] Notification displayed successfully');
      })
      .catch((error) => {
        console.error('[SW] Error showing notification:', error);
      })
  );
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  console.log('[SW] Notification clicked:', event.action);
  
  event.notification.close();

  let targetUrl = '/';
  
  // Handle different actions
  switch (event.action) {
    case 'view':
      targetUrl = '/';
      break;
    case 'contact':
      targetUrl = '/#contact';
      break;
    case 'dismiss':
      return; // Just close the notification
    default:
      targetUrl = event.notification.data?.url || '/';
  }

  // Open or focus the app
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        // Check if app is already open
        for (const client of clientList) {
          if (client.url.includes(self.location.origin)) {
            client.focus();
            client.navigate(targetUrl);
            return;
          }
        }
        
        // Open new window if app is not open
        return clients.openWindow(targetUrl);
      })
      .catch((error) => {
        console.error('[SW] Error handling notification click:', error);
      })
  );
});

// Background sync event (for offline actions)
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag);
  
  if (event.tag === 'contact-form-sync') {
    event.waitUntil(syncContactForm());
  } else if (event.tag === 'analytics-sync') {
    event.waitUntil(syncAnalytics());
  }
});

// Sync contact form submissions when back online
async function syncContactForm() {
  try {
    const pendingForms = await getStoredData('pending-contact-forms');
    
    for (const form of pendingForms) {
      try {
        // Attempt to send the form
        await fetch('/api/contact', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(form)
        });
        
        // Remove from pending if successful
        await removeStoredData('pending-contact-forms', form.id);
        
        // Show success notification
        await self.registration.showNotification('Message Sent!', {
          body: 'Your contact form was sent successfully.',
          icon: '/icons/icon-192x192.png',
          tag: 'form-success'
        });
        
      } catch (error) {
        console.error('[SW] Error syncing contact form:', error);
      }
    }
  } catch (error) {
    console.error('[SW] Error in syncContactForm:', error);
  }
}

// Sync analytics data when back online
async function syncAnalytics() {
  try {
    const pendingAnalytics = await getStoredData('pending-analytics');
    
    for (const data of pendingAnalytics) {
      try {
        // Send analytics data
        if (typeof gtag !== 'undefined') {
          gtag('event', data.event, data.parameters);
        }
        
        // Remove from pending
        await removeStoredData('pending-analytics', data.id);
        
      } catch (error) {
        console.error('[SW] Error syncing analytics:', error);
      }
    }
  } catch (error) {
    console.error('[SW] Error in syncAnalytics:', error);
  }
}

// Helper functions for IndexedDB storage
async function getStoredData(storeName) {
  // Simplified storage - in production, use IndexedDB
  const data = localStorage.getItem(storeName);
  return data ? JSON.parse(data) : [];
}

async function removeStoredData(storeName, id) {
  const data = await getStoredData(storeName);
  const filtered = data.filter(item => item.id !== id);
  localStorage.setItem(storeName, JSON.stringify(filtered));
}

// Periodic background sync for engagement
self.addEventListener('periodicsync', (event) => {
  console.log('[SW] Periodic sync triggered:', event.tag);
  
  if (event.tag === 'portfolio-engagement') {
    event.waitUntil(sendEngagementNotification());
  }
});

// Send engagement notifications
async function sendEngagementNotification() {
  const lastVisit = localStorage.getItem('last-visit');
  const now = Date.now();
  const daysSinceVisit = lastVisit ? (now - parseInt(lastVisit)) / (1000 * 60 * 60 * 24) : 0;
  
  // Send notification if user hasn't visited in 3 days
  if (daysSinceVisit >= 3) {
    await self.registration.showNotification('New Projects Added!', {
      body: 'Check out my latest work and updates to my portfolio.',
      icon: '/icons/icon-192x192.png',
      badge: '/icons/icon-72x72.png',
      tag: 'engagement-notification',
      data: { url: '/#projects' },
      actions: [
        { action: 'view', title: 'View Projects' },
        { action: 'dismiss', title: 'Maybe Later' }
      ]
    });
  }
}

// Message event for communication with main thread
self.addEventListener('message', (event) => {
  console.log('[SW] Message received:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  } else if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  } else if (event.data && event.data.type === 'CLEAR_CACHE') {
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
    });
  }
});

// Enhanced cache management helper functions
async function cacheResponse(request, response, strategy) {
  try {
    const startTime = performance.now();
    const cache = await caches.open(strategy.cacheName);

    // Check response size (skip caching if too large)
    const contentLength = response.headers.get('content-length');
    if (contentLength && parseInt(contentLength) > 5 * 1024 * 1024) { // 5MB limit
      console.warn('[SW] Response too large to cache:', request.url, contentLength);
      return;
    }

    // Check if response is cacheable
    if (!response || response.status !== 200 || response.type === 'opaque') {
      console.warn('[SW] Response not cacheable:', request.url, response.status);
      return;
    }

    // Store cache timestamp for expiration checking
    const cacheKey = `${request.url}_timestamp`;
    await cache.put(new Request(cacheKey), new Response(Date.now().toString()));

    // Add performance metadata to response
    const enhancedResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...Object.fromEntries(response.headers.entries()),
        'sw-cached-at': Date.now().toString(),
        'sw-cache-strategy': strategy.name,
        'sw-version': VERSION
      }
    });

    // Cache the enhanced response
    await cache.put(request, enhancedResponse);

    // Enforce cache size limits
    await enforceCacheLimit(strategy.cacheName, strategy.name);

    // Log performance metrics
    const cacheTime = performance.now() - startTime;
    if (cacheTime > 100) { // Log slow cache operations
      console.warn('[SW] Slow cache operation:', request.url, `${cacheTime.toFixed(2)}ms`);
    }

  } catch (error) {
    console.error('[SW] Failed to cache response:', error);
  }
}

async function getCacheTime(request, cacheName) {
  try {
    const cache = await caches.open(cacheName);
    const cacheKey = `${request.url}_timestamp`;
    const timestampResponse = await cache.match(new Request(cacheKey));

    if (timestampResponse) {
      const timestamp = await timestampResponse.text();
      return parseInt(timestamp);
    }
  } catch (error) {
    console.warn('[SW] Failed to get cache time:', error);
  }

  return 0;
}

async function enforceCacheLimit(cacheName, strategyName) {
  try {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    const config = CACHE_CONFIG[strategyName] || CACHE_CONFIG.dynamic;

    // Remove timestamp entries from count
    const actualKeys = keys.filter(key => !key.url.includes('_timestamp'));

    if (actualKeys.length > config.maxEntries) {
      // Sort by cache time and remove oldest entries
      const entriesToRemove = actualKeys.length - config.maxEntries;
      const sortedKeys = await Promise.all(
        actualKeys.map(async (key) => ({
          key,
          time: await getCacheTime(key, cacheName)
        }))
      );

      sortedKeys.sort((a, b) => a.time - b.time);

      for (let i = 0; i < entriesToRemove; i++) {
        await cache.delete(sortedKeys[i].key);
        await cache.delete(new Request(`${sortedKeys[i].key.url}_timestamp`));
      }

      console.log(`[SW] Removed ${entriesToRemove} old entries from ${cacheName}`);
    }
  } catch (error) {
    console.error('[SW] Failed to enforce cache limit:', error);
  }
}

// Advanced cache cleanup with expiration
async function cleanupExpiredCaches() {
  const cacheNames = await caches.keys();

  for (const cacheName of cacheNames) {
    if (!cacheName.startsWith(CACHE_PREFIX)) continue;

    const cache = await caches.open(cacheName);
    const keys = await cache.keys();

    for (const key of keys) {
      if (key.url.includes('_timestamp')) continue;

      const cacheTime = await getCacheTime(key, cacheName);
      const strategyName = getStrategyNameFromCache(cacheName);
      const maxAge = CACHE_CONFIG[strategyName]?.maxAge || CACHE_CONFIG.dynamic.maxAge;

      if (Date.now() - cacheTime > maxAge) {
        await cache.delete(key);
        await cache.delete(new Request(`${key.url}_timestamp`));
        console.log('[SW] Expired cache entry removed:', key.url);
      }
    }
  }
}

function getStrategyNameFromCache(cacheName) {
  if (cacheName.includes('static')) return 'static';
  if (cacheName.includes('images')) return 'images';
  if (cacheName.includes('fonts')) return 'fonts';
  if (cacheName.includes('api')) return 'api';
  if (cacheName.includes('cdn')) return 'cdn';
  return 'dynamic';
}

// Periodic cache maintenance
setInterval(() => {
  cleanupExpiredCaches();
}, 60 * 60 * 1000); // Run every hour

console.log('[SW] Advanced Service Worker v' + VERSION + ' loaded successfully');
