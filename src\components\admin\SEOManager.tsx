import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Globe,
  Target,
  BarChart3,
  CheckCircle,
  AlertTriangle,
  Zap,
  Award,
  Rocket,
  Save,
  Plus,
  Trash2,
  ExternalLink,
  FileText,
  Hash
} from 'lucide-react';
import toast from 'react-hot-toast';
import { SuccessToast, ErrorToast } from '../CustomToast';
import { seoFileGenerator } from '../../utils/seoFileGenerator';

interface SEOManagerProps {
  onDataChange: () => void;
}

interface SEOData {
  meta: {
    title: string;
    description: string;
    keywords: string[];
    author: string;
    robots: string;
    canonical: string;
    language: string;
    viewport: string;
  };
  openGraph: {
    title: string;
    description: string;
    image: string;
    url: string;
    type: string;
    siteName: string;
    locale: string;
  };
  twitter: {
    card: string;
    site: string;
    creator: string;
    title: string;
    description: string;
    image: string;
  };
  structuredData: {
    person: {
      name: string;
      jobTitle: string;
      url: string;
      sameAs: string[];
      worksFor: string;
      email: string;
      telephone: string;
      address: {
        addressLocality: string;
        addressCountry: string;
      };
    };
    website: {
      name: string;
      url: string;
      description: string;
      inLanguage: string;
    };
  };
  performance: {
    enableLazyLoading: boolean;
    enableImageOptimization: boolean;
    enableCaching: boolean;
    enableCompression: boolean;
    enableMinification: boolean;
  };
  analytics: {
    googleAnalyticsId: string;
    googleSearchConsole: string;
    bingWebmasterTools: string;
    yandexWebmaster: string;
  };
  sitemap: {
    autoGenerate: boolean;
    includeImages: boolean;
    changeFrequency: string;
    priority: number;
  };
  localSEO: {
    businessName: string;
    businessType: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    phone: string;
    email: string;
    website: string;
    hours: string;
    priceRange: string;
  };
}

const SEOManager: React.FC<SEOManagerProps> = ({ onDataChange }) => {
  const [seoData, setSeoData] = useState<SEOData>({
    meta: {
      title: 'Nural Bhardwaj - #1 Full Stack Developer & UI/UX Designer | Award-Winning Portfolio 2024',
      description: '🏆 Top-Rated Full Stack Developer & UI/UX Designer with 5+ years experience. Specializing in React, Node.js, TypeScript & cutting-edge web technologies. 50+ successful projects delivered. Hire the best developer for your next project! ⭐ 5-star rated ⭐',
      keywords: [
        // Primary high-value keywords
        'Nural Bhardwaj',
        'Full Stack Developer',
        'UI/UX Designer',
        'React Developer',
        'Node.js Developer',
        'TypeScript Developer',
        'JavaScript Developer',
        'Web Developer',
        'Frontend Developer',
        'Backend Developer',
        'Software Engineer',
        'Web Designer',

        // Location-based keywords
        'Full Stack Developer India',
        'React Developer Gurugram',
        'Web Developer Haryana',
        'UI/UX Designer Delhi NCR',
        'JavaScript Developer India',
        'TypeScript Developer Gurugram',

        // Service-based keywords
        'Custom Web Development',
        'React Application Development',
        'E-commerce Website Development',
        'Mobile App Development',
        'API Development',
        'Database Design',
        'Cloud Computing',
        'DevOps Services',
        'Website Redesign',
        'Performance Optimization',

        // Technology keywords
        'React.js',
        'Next.js',
        'Vue.js',
        'Angular',
        'Express.js',
        'MongoDB',
        'PostgreSQL',
        'AWS',
        'Docker',
        'Kubernetes',
        'GraphQL',
        'REST API',
        'Microservices',
        'Serverless',

        // Industry keywords
        'Fintech Developer',
        'E-commerce Developer',
        'SaaS Developer',
        'Startup Developer',
        'Enterprise Developer',
        'Freelance Developer',
        'Remote Developer',

        // Long-tail keywords
        'Best Full Stack Developer for Hire',
        'Professional Web Development Services',
        'Custom React Application Development',
        'Experienced UI/UX Designer Portfolio',
        'Top Rated JavaScript Developer',
        'Expert TypeScript Development Services',
        'Modern Web Application Development',
        'Responsive Website Design Services',
        'Full Stack Development Consultant',
        'Senior Software Engineer Portfolio'
      ],
      author: 'Nural Bhardwaj',
      robots: 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1',
      canonical: 'https://nuralbhardwaj.me',
      language: 'en-US',
      viewport: 'width=device-width, initial-scale=1.0, viewport-fit=cover'
    },
    openGraph: {
      title: '🏆 Nural Bhardwaj - #1 Full Stack Developer & UI/UX Designer | 50+ Projects Delivered',
      description: '⭐ Top-Rated Full Stack Developer with 5+ years experience. Specializing in React, Node.js, TypeScript. Award-winning portfolio with 50+ successful projects. Available for hire! 🚀',
      image: 'https://nuralbhardwaj.me/og-image-optimized.svg',
      url: 'https://nuralbhardwaj.me',
      type: 'profile',
      siteName: 'Nural Bhardwaj - Professional Developer Portfolio',
      locale: 'en_US'
    },
    twitter: {
      card: 'summary_large_image',
      site: '@nuralbhardwaj',
      creator: '@nuralbhardwaj',
      title: '🏆 Nural Bhardwaj - #1 Full Stack Developer | 50+ Projects',
      description: '⭐ Top-Rated Developer | React, Node.js, TypeScript Expert | 5+ Years Experience | Available for Hire 🚀',
      image: 'https://nuralbhardwaj.me/twitter-card-optimized.jpg'
    },
    structuredData: {
      person: {
        name: 'Nural Bhardwaj',
        jobTitle: 'Senior Full Stack Developer & UI/UX Designer',
        url: 'https://nuralbhardwaj.me',
        sameAs: [
          'https://github.com/NuralBhardwaj',
          'https://linkedin.com/in/nural-bhardwaj',
          'https://twitter.com/nuralbhardwaj',
          'https://stackoverflow.com/users/nuralbhardwaj',
          'https://dev.to/nuralbhardwaj',
          'https://medium.com/@nuralbhardwaj',
          'https://dribbble.com/nuralbhardwaj',
          'https://behance.net/nuralbhardwaj'
        ],
        worksFor: 'Independent Consultant & Freelance Developer',
        email: '<EMAIL>',
        telephone: '+91-9876543210',
        address: {
          addressLocality: 'Gurugram',
          addressCountry: 'India'
        }
      },
      website: {
        name: 'Nural Bhardwaj - Professional Developer Portfolio',
        url: 'https://nuralbhardwaj.me',
        description: 'Award-winning portfolio showcasing 50+ successful full stack development and UI/UX design projects. Featuring cutting-edge web applications, mobile apps, and innovative digital solutions.',
        inLanguage: 'en-US'
      }
    },
    performance: {
      enableLazyLoading: true,
      enableImageOptimization: true,
      enableCaching: true,
      enableCompression: true,
      enableMinification: true
    },
    analytics: {
      googleAnalyticsId: 'G-V4VD9F3LXK',
      googleSearchConsole: '',
      bingWebmasterTools: '',
      yandexWebmaster: ''
    },
    sitemap: {
      autoGenerate: true,
      includeImages: true,
      changeFrequency: 'weekly',
      priority: 1.0
    },
    localSEO: {
      businessName: 'Nural Bhardwaj - Premium Full Stack Development & UI/UX Design Services',
      businessType: 'Software Development & Digital Design Consultant',
      address: 'Sector 14, Gurugram, Haryana',
      city: 'Gurugram',
      state: 'Haryana',
      zipCode: '122001',
      country: 'India',
      phone: '+91-9876543210',
      email: '<EMAIL>',
      website: 'https://nuralbhardwaj.me',
      hours: 'Mon-Fri 9AM-9PM IST, Sat 10AM-6PM IST',
      priceRange: '$$$'
    }
  });

  const [activeTab, setActiveTab] = useState<'meta' | 'social' | 'structured' | 'performance' | 'analytics' | 'local'>('meta');
  const [seoScore, setSeoScore] = useState(0);
  const [seoIssues, setSeoIssues] = useState<string[]>([]);
  const [newKeyword, setNewKeyword] = useState('');

  useEffect(() => {
    loadSEOData();
    calculateSEOScore();
  }, []);

  useEffect(() => {
    calculateSEOScore();
  }, [seoData]);

  const loadSEOData = () => {
    const saved = localStorage.getItem('seo_data');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setSeoData({ ...seoData, ...parsed });
      } catch (error) {
        console.error('Error loading SEO data:', error);
      }
    }
  };

  const saveSEOData = () => {
    try {
      localStorage.setItem('seo_data', JSON.stringify(seoData));
      generateSEOFiles();
      onDataChange();

      toast(() => (
        <SuccessToast
          message="SEO settings saved and files generated!"
          icon={<Save className="w-5 h-5 text-green-400" />}
        />
      ));
    } catch (error) {
      toast(() => (
        <ErrorToast
          message="Failed to save SEO settings"
        />
      ));
    }
  };

  const calculateSEOScore = () => {
    let score = 0;
    const issues: string[] = [];

    // Meta tags scoring
    if (seoData.meta.title && seoData.meta.title.length >= 30 && seoData.meta.title.length <= 60) {
      score += 15;
    } else {
      issues.push('Title should be 30-60 characters');
    }

    if (seoData.meta.description && seoData.meta.description.length >= 120 && seoData.meta.description.length <= 160) {
      score += 15;
    } else {
      issues.push('Meta description should be 120-160 characters');
    }

    if (seoData.meta.keywords.length >= 5) {
      score += 10;
    } else {
      issues.push('Add at least 5 relevant keywords');
    }

    // Open Graph scoring
    if (seoData.openGraph.title && seoData.openGraph.description && seoData.openGraph.image) {
      score += 15;
    } else {
      issues.push('Complete Open Graph tags for social sharing');
    }

    // Twitter Cards scoring
    if (seoData.twitter.card && seoData.twitter.title && seoData.twitter.description) {
      score += 10;
    } else {
      issues.push('Complete Twitter Card tags');
    }

    // Structured Data scoring
    if (seoData.structuredData.person.name && seoData.structuredData.person.jobTitle) {
      score += 15;
    } else {
      issues.push('Complete structured data for better search results');
    }

    // Performance scoring
    const performanceFeatures = Object.values(seoData.performance).filter(Boolean).length;
    score += performanceFeatures * 2;

    // Analytics scoring
    if (seoData.analytics.googleAnalyticsId) {
      score += 5;
    } else {
      issues.push('Set up Google Analytics');
    }

    // Local SEO scoring
    if (seoData.localSEO.businessName && seoData.localSEO.address) {
      score += 10;
    } else {
      issues.push('Complete local SEO information');
    }

    setSeoScore(Math.min(score, 100));
    setSeoIssues(issues);
  };

  const generateSEOFiles = () => {
    try {
      // Map the current SEO data to the format expected by seoFileGenerator
      const mappedData = {
        meta: {
          title: seoData.meta.title,
          description: seoData.meta.description,
          keywords: seoData.meta.keywords,
          author: seoData.meta.author,
          canonicalUrl: seoData.meta.canonical,
          robots: seoData.meta.robots
        },
        openGraph: {
          title: seoData.openGraph.title,
          description: seoData.openGraph.description,
          image: seoData.openGraph.image,
          url: seoData.openGraph.url,
          siteName: seoData.openGraph.siteName,
          type: seoData.openGraph.type,
          locale: seoData.openGraph.locale
        },
        twitter: {
          card: seoData.twitter.card,
          title: seoData.twitter.title,
          description: seoData.twitter.description,
          image: seoData.twitter.image,
          creator: seoData.twitter.creator,
          site: seoData.twitter.site
        },
        structuredData: {
          person: {
            name: seoData.structuredData.person.name,
            jobTitle: seoData.structuredData.person.jobTitle,
            description: `${seoData.structuredData.person.jobTitle} with expertise in modern web technologies`,
            url: seoData.structuredData.person.url,
            email: seoData.structuredData.person.email,
            telephone: seoData.structuredData.person.telephone,
            address: {
              streetAddress: seoData.localSEO.address,
              addressLocality: seoData.structuredData.person.address.addressLocality,
              addressRegion: seoData.localSEO.state,
              postalCode: seoData.localSEO.zipCode,
              addressCountry: seoData.structuredData.person.address.addressCountry
            },
            sameAs: seoData.structuredData.person.sameAs
          },
          website: {
            name: seoData.structuredData.website.name,
            description: seoData.structuredData.website.description,
            url: seoData.structuredData.website.url,
            author: seoData.meta.author,
            publisher: seoData.meta.author,
            inLanguage: seoData.structuredData.website.inLanguage
          },
          localBusiness: {
            name: seoData.localSEO.businessName,
            description: `${seoData.localSEO.businessType} providing professional web development services`,
            url: seoData.localSEO.website,
            telephone: seoData.localSEO.phone,
            email: seoData.localSEO.email,
            address: {
              streetAddress: seoData.localSEO.address,
              addressLocality: seoData.localSEO.city,
              addressRegion: seoData.localSEO.state,
              postalCode: seoData.localSEO.zipCode,
              addressCountry: seoData.localSEO.country
            },
            openingHours: [seoData.localSEO.hours],
            priceRange: seoData.localSEO.priceRange
          }
        },
        analytics: {
          googleAnalytics: seoData.analytics.googleAnalyticsId,
          googleSearchConsole: seoData.analytics.googleSearchConsole,
          bingWebmaster: seoData.analytics.bingWebmasterTools,
          yandexWebmaster: seoData.analytics.yandexWebmaster
        },
        performance: {
          lazyLoading: seoData.performance.enableLazyLoading,
          imageOptimization: seoData.performance.enableImageOptimization,
          caching: seoData.performance.enableCaching,
          compression: seoData.performance.enableCompression
        }
      };

      seoFileGenerator.generateAndDownloadSEOFiles(mappedData);

      toast(() => (
        <SuccessToast
          message="SEO files generated and downloaded successfully!"
          icon={<Save className="w-5 h-5 text-green-400" />}
        />
      ));
    } catch (error) {
      console.error('Error generating SEO files:', error);
      toast(() => (
        <ErrorToast
          message="Failed to generate SEO files"
        />
      ));
    }
  };

  const downloadSEOFiles = () => {
    generateSEOFiles();
  };

  const addKeyword = () => {
    if (newKeyword.trim() && !seoData.meta.keywords.includes(newKeyword.trim())) {
      setSeoData({
        ...seoData,
        meta: {
          ...seoData.meta,
          keywords: [...seoData.meta.keywords, newKeyword.trim()]
        }
      });
      setNewKeyword('');
    }
  };

  const removeKeyword = (keyword: string) => {
    setSeoData({
      ...seoData,
      meta: {
        ...seoData.meta,
        keywords: seoData.meta.keywords.filter(k => k !== keyword)
      }
    });
  };

  const tabs = [
    { id: 'meta', label: 'Meta Tags', icon: Hash },
    { id: 'social', label: 'Social Media', icon: Globe },
    { id: 'structured', label: 'Structured Data', icon: FileText },
    { id: 'performance', label: 'Performance', icon: Zap },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'local', label: 'Local SEO', icon: Target },
  ];

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreGradient = (score: number) => {
    if (score >= 90) return 'from-green-500 to-emerald-500';
    if (score >= 70) return 'from-yellow-500 to-orange-500';
    return 'from-red-500 to-pink-500';
  };

  return (
    <div className="space-y-6">
      {/* Header with SEO Score */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Advanced SEO Manager</h1>
          <p className="text-gray-400">Optimize your website for maximum search engine visibility</p>
        </div>
        <div className="flex items-center space-x-4">
          {/* SEO Score */}
          <div className="text-center">
            <div className={`text-3xl font-bold ${getScoreColor(seoScore)}`}>
              {seoScore}%
            </div>
            <div className="text-sm text-gray-400">SEO Score</div>
          </div>
          <button
            onClick={downloadSEOFiles}
            className="flex items-center space-x-2 px-4 py-3 bg-green-600/20 text-green-400 rounded-xl hover:bg-green-600/30 transition-all duration-200"
          >
            <FileText className="w-5 h-5" />
            <span>Download Files</span>
          </button>
          <button
            onClick={saveSEOData}
            className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-xl transition-all duration-200 transform hover:scale-105"
          >
            <Save className="w-5 h-5" />
            <span>Save & Generate</span>
          </button>
        </div>
      </div>

      {/* SEO Score Dashboard */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
      >
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white flex items-center space-x-2">
            <Award className="w-5 h-5 text-yellow-400" />
            <span>SEO Health Dashboard</span>
          </h2>
          <div className={`px-4 py-2 rounded-xl bg-gradient-to-r ${getScoreGradient(seoScore)} text-white font-bold`}>
            {seoScore >= 90 ? 'Excellent' : seoScore >= 70 ? 'Good' : 'Needs Improvement'}
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {/* Score Visualization */}
          <div className="space-y-4">
            <div className="relative">
              <div className="w-full bg-gray-700 rounded-full h-4">
                <div
                  className={`h-4 rounded-full bg-gradient-to-r ${getScoreGradient(seoScore)} transition-all duration-1000`}
                  style={{ width: `${seoScore}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-sm text-gray-400 mt-2">
                <span>0%</span>
                <span>50%</span>
                <span>100%</span>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="p-3 bg-white/5 rounded-xl">
                <div className="text-2xl font-bold text-green-400">{100 - seoIssues.length}</div>
                <div className="text-xs text-gray-400">Optimized</div>
              </div>
              <div className="p-3 bg-white/5 rounded-xl">
                <div className="text-2xl font-bold text-yellow-400">{seoIssues.length}</div>
                <div className="text-xs text-gray-400">Issues</div>
              </div>
              <div className="p-3 bg-white/5 rounded-xl">
                <div className="text-2xl font-bold text-blue-400">{seoData.meta.keywords.length}</div>
                <div className="text-xs text-gray-400">Keywords</div>
              </div>
            </div>
          </div>

          {/* Issues List */}
          <div>
            <h3 className="text-white font-semibold mb-3 flex items-center space-x-2">
              <AlertTriangle className="w-4 h-4 text-yellow-400" />
              <span>Optimization Opportunities</span>
            </h3>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {seoIssues.length > 0 ? (
                seoIssues.map((issue, index) => (
                  <div key={index} className="flex items-center space-x-2 p-2 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                    <AlertTriangle className="w-4 h-4 text-yellow-400 flex-shrink-0" />
                    <span className="text-yellow-200 text-sm">{issue}</span>
                  </div>
                ))
              ) : (
                <div className="flex items-center space-x-2 p-2 bg-green-500/10 rounded-lg border border-green-500/20">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-green-200 text-sm">All SEO checks passed!</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Tabs */}
      <div className="flex space-x-2 bg-white/5 backdrop-blur-sm rounded-2xl p-2 border border-white/10 overflow-x-auto">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center space-x-2 px-4 py-3 rounded-xl font-medium transition-all duration-200 whitespace-nowrap ${
              activeTab === tab.id
                ? 'bg-gradient-to-r from-purple-600 to-cyan-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-white/5'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="min-h-[600px]">
        {/* Meta Tags Tab */}
        {activeTab === 'meta' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid lg:grid-cols-2 gap-6"
          >
            {/* Basic Meta Tags */}
            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
                <Hash className="w-5 h-5 text-blue-400" />
                <span>Basic Meta Tags</span>
              </h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Page Title ({seoData.meta.title.length}/60)
                  </label>
                  <input
                    type="text"
                    value={seoData.meta.title}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      meta: { ...seoData.meta, title: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Your optimized page title"
                    maxLength={60}
                  />
                  <div className={`text-xs mt-1 ${
                    seoData.meta.title.length >= 30 && seoData.meta.title.length <= 60
                      ? 'text-green-400'
                      : 'text-yellow-400'
                  }`}>
                    Optimal length: 30-60 characters
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Meta Description ({seoData.meta.description.length}/160)
                  </label>
                  <textarea
                    value={seoData.meta.description}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      meta: { ...seoData.meta, description: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Compelling meta description that encourages clicks"
                    rows={3}
                    maxLength={160}
                  />
                  <div className={`text-xs mt-1 ${
                    seoData.meta.description.length >= 120 && seoData.meta.description.length <= 160
                      ? 'text-green-400'
                      : 'text-yellow-400'
                  }`}>
                    Optimal length: 120-160 characters
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Author</label>
                  <input
                    type="text"
                    value={seoData.meta.author}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      meta: { ...seoData.meta, author: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Your name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Canonical URL</label>
                  <input
                    type="url"
                    value={seoData.meta.canonical}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      meta: { ...seoData.meta, canonical: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="https://yourwebsite.com"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Robots Meta</label>
                  <select
                    value={seoData.meta.robots}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      meta: { ...seoData.meta, robots: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                  >
                    <option value="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">Index & Follow (Recommended)</option>
                    <option value="index, nofollow">Index but Don't Follow Links</option>
                    <option value="noindex, follow">Don't Index but Follow Links</option>
                    <option value="noindex, nofollow">Don't Index or Follow</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Keywords Management */}
            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
                <Target className="w-5 h-5 text-green-400" />
                <span>Keywords Strategy</span>
              </h2>

              <div className="space-y-4">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newKeyword}
                    onChange={(e) => setNewKeyword(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && addKeyword()}
                    className="flex-1 px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Add target keyword"
                  />
                  <button
                    onClick={addKeyword}
                    className="px-4 py-3 bg-green-600 hover:bg-green-700 text-white rounded-xl transition-all duration-200"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>

                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {seoData.meta.keywords.map((keyword, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-white/5 rounded-xl border border-white/10"
                    >
                      <span className="text-white">{keyword}</span>
                      <button
                        onClick={() => removeKeyword(keyword)}
                        className="p-1 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded transition-all duration-200"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>

                <div className="p-4 bg-blue-600/10 rounded-xl border border-blue-500/20">
                  <h3 className="text-blue-400 font-semibold mb-2">Keyword Tips:</h3>
                  <ul className="text-sm text-blue-200 space-y-1">
                    <li>• Use long-tail keywords (3-4 words)</li>
                    <li>• Include location-based keywords</li>
                    <li>• Target industry-specific terms</li>
                    <li>• Research competitor keywords</li>
                    <li>• Use keyword variations and synonyms</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Social Media Tab */}
        {activeTab === 'social' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid lg:grid-cols-2 gap-6"
          >
            {/* Open Graph */}
            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
                <Globe className="w-5 h-5 text-blue-400" />
                <span>Open Graph (Facebook)</span>
              </h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">OG Title</label>
                  <input
                    type="text"
                    value={seoData.openGraph.title}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      openGraph: { ...seoData.openGraph, title: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Title for social media sharing"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">OG Description</label>
                  <textarea
                    value={seoData.openGraph.description}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      openGraph: { ...seoData.openGraph, description: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Description for social media sharing"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">OG Image URL</label>
                  <input
                    type="url"
                    value={seoData.openGraph.image}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      openGraph: { ...seoData.openGraph, image: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="https://yoursite.com/og-image.jpg"
                  />
                  <div className="text-xs text-gray-400 mt-1">
                    Recommended: 1200x630px, under 1MB
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Site Name</label>
                  <input
                    type="text"
                    value={seoData.openGraph.siteName}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      openGraph: { ...seoData.openGraph, siteName: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Your website name"
                  />
                </div>
              </div>
            </div>

            {/* Twitter Cards */}
            <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
                <Hash className="w-5 h-5 text-cyan-400" />
                <span>Twitter Cards</span>
              </h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Card Type</label>
                  <select
                    value={seoData.twitter.card}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      twitter: { ...seoData.twitter, card: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                  >
                    <option value="summary_large_image">Large Image Summary</option>
                    <option value="summary">Summary</option>
                    <option value="app">App</option>
                    <option value="player">Player</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Twitter Handle</label>
                  <input
                    type="text"
                    value={seoData.twitter.site}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      twitter: { ...seoData.twitter, site: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="@yourusername"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Twitter Title</label>
                  <input
                    type="text"
                    value={seoData.twitter.title}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      twitter: { ...seoData.twitter, title: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Title for Twitter sharing"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Twitter Description</label>
                  <textarea
                    value={seoData.twitter.description}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      twitter: { ...seoData.twitter, description: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Description for Twitter sharing"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Twitter Image</label>
                  <input
                    type="url"
                    value={seoData.twitter.image}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      twitter: { ...seoData.twitter, image: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="https://yoursite.com/twitter-image.jpg"
                  />
                  <div className="text-xs text-gray-400 mt-1">
                    Recommended: 1200x600px for large image cards
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Performance Tab */}
        {activeTab === 'performance' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
          >
            <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
              <Zap className="w-5 h-5 text-yellow-400" />
              <span>Performance Optimization</span>
            </h2>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-white font-semibold">Core Web Vitals</h3>
                {Object.entries(seoData.performance).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between p-3 bg-white/5 rounded-xl">
                    <span className="text-gray-300 capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={value}
                        onChange={(e) => setSeoData({
                          ...seoData,
                          performance: { ...seoData.performance, [key]: e.target.checked }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                    </label>
                  </div>
                ))}
              </div>

              <div className="space-y-4">
                <h3 className="text-white font-semibold">Performance Tips</h3>
                <div className="space-y-3">
                  <div className="p-3 bg-green-600/10 rounded-xl border border-green-500/20">
                    <div className="flex items-center space-x-2 mb-2">
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-green-400 font-medium">Image Optimization</span>
                    </div>
                    <p className="text-green-200 text-sm">Use WebP format and lazy loading for faster page loads</p>
                  </div>

                  <div className="p-3 bg-blue-600/10 rounded-xl border border-blue-500/20">
                    <div className="flex items-center space-x-2 mb-2">
                      <Zap className="w-4 h-4 text-blue-400" />
                      <span className="text-blue-400 font-medium">Code Splitting</span>
                    </div>
                    <p className="text-blue-200 text-sm">Load only necessary code for each page</p>
                  </div>

                  <div className="p-3 bg-purple-600/10 rounded-xl border border-purple-500/20">
                    <div className="flex items-center space-x-2 mb-2">
                      <Rocket className="w-4 h-4 text-purple-400" />
                      <span className="text-purple-400 font-medium">CDN Usage</span>
                    </div>
                    <p className="text-purple-200 text-sm">Serve static assets from global CDN</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
          >
            <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
              <BarChart3 className="w-5 h-5 text-green-400" />
              <span>Analytics & Tracking</span>
            </h2>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Google Analytics ID</label>
                  <input
                    type="text"
                    value={seoData.analytics.googleAnalyticsId}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      analytics: { ...seoData.analytics, googleAnalyticsId: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="G-XXXXXXXXXX"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Google Search Console</label>
                  <input
                    type="text"
                    value={seoData.analytics.googleSearchConsole}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      analytics: { ...seoData.analytics, googleSearchConsole: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Verification code"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Bing Webmaster Tools</label>
                  <input
                    type="text"
                    value={seoData.analytics.bingWebmasterTools}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      analytics: { ...seoData.analytics, bingWebmasterTools: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Verification code"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-white font-semibold">SEO Tools Integration</h3>
                <div className="space-y-3">
                  <a
                    href="https://search.google.com/search-console"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-between p-3 bg-blue-600/10 rounded-xl border border-blue-500/20 hover:bg-blue-600/20 transition-all duration-200"
                  >
                    <span className="text-blue-400">Google Search Console</span>
                    <ExternalLink className="w-4 h-4 text-blue-400" />
                  </a>

                  <a
                    href="https://analytics.google.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-between p-3 bg-green-600/10 rounded-xl border border-green-500/20 hover:bg-green-600/20 transition-all duration-200"
                  >
                    <span className="text-green-400">Google Analytics</span>
                    <ExternalLink className="w-4 h-4 text-green-400" />
                  </a>

                  <a
                    href="https://www.bing.com/webmasters"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-between p-3 bg-orange-600/10 rounded-xl border border-orange-500/20 hover:bg-orange-600/20 transition-all duration-200"
                  >
                    <span className="text-orange-400">Bing Webmaster Tools</span>
                    <ExternalLink className="w-4 h-4 text-orange-400" />
                  </a>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Local SEO Tab */}
        {activeTab === 'local' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
          >
            <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
              <Target className="w-5 h-5 text-red-400" />
              <span>Local SEO & Business Info</span>
            </h2>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Business Name</label>
                  <input
                    type="text"
                    value={seoData.localSEO.businessName}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      localSEO: { ...seoData.localSEO, businessName: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Your business name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Business Type</label>
                  <input
                    type="text"
                    value={seoData.localSEO.businessType}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      localSEO: { ...seoData.localSEO, businessType: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="e.g., Web Development Agency"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Address</label>
                  <input
                    type="text"
                    value={seoData.localSEO.address}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      localSEO: { ...seoData.localSEO, address: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Street address"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">City</label>
                    <input
                      type="text"
                      value={seoData.localSEO.city}
                      onChange={(e) => setSeoData({
                        ...seoData,
                        localSEO: { ...seoData.localSEO, city: e.target.value }
                      })}
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                      placeholder="City"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">State</label>
                    <input
                      type="text"
                      value={seoData.localSEO.state}
                      onChange={(e) => setSeoData({
                        ...seoData,
                        localSEO: { ...seoData.localSEO, state: e.target.value }
                      })}
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                      placeholder="State"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Phone Number</label>
                  <input
                    type="tel"
                    value={seoData.localSEO.phone}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      localSEO: { ...seoData.localSEO, phone: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="+****************"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Business Hours</label>
                  <input
                    type="text"
                    value={seoData.localSEO.hours}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      localSEO: { ...seoData.localSEO, hours: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                    placeholder="Mon-Fri 9AM-6PM"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Price Range</label>
                  <select
                    value={seoData.localSEO.priceRange}
                    onChange={(e) => setSeoData({
                      ...seoData,
                      localSEO: { ...seoData.localSEO, priceRange: e.target.value }
                    })}
                    className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                  >
                    <option value="$">$ - Budget Friendly</option>
                    <option value="$$">$$ - Moderate</option>
                    <option value="$$$">$$$ - Premium</option>
                    <option value="$$$$">$$$$ - Luxury</option>
                  </select>
                </div>

                <div className="p-4 bg-yellow-600/10 rounded-xl border border-yellow-500/20">
                  <h3 className="text-yellow-400 font-semibold mb-2">Local SEO Benefits:</h3>
                  <ul className="text-sm text-yellow-200 space-y-1">
                    <li>• Appear in "Near me" searches</li>
                    <li>• Google My Business optimization</li>
                    <li>• Local directory listings</li>
                    <li>• Location-based keywords</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default SEOManager;