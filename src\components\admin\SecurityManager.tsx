import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Shield,
  Lock,
  AlertTriangle,
  CheckCircle,
  Settings,
  BarChart3,
  Eye,
  EyeOff,
  Zap,
  Globe,
  Server,
  Key,
  RefreshCw
} from 'lucide-react';
import toast from 'react-hot-toast';
import { SuccessToast, ErrorToast, InfoToast } from '../CustomToast';
import { securityService } from '../../services/securityService';
import { captchaService } from '../../services/captchaService';
import { cspService } from '../../services/cspService';
import { sslService } from '../../services/sslService';

const SecurityManager: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'rate-limiting' | 'captcha' | 'csp' | 'ssl'>('overview');
  const [securityMetrics, setSecurityMetrics] = useState(securityService.getMetrics());
  const [rateLimitConfig, setRateLimitConfig] = useState({
    contact: { windowMs: 60000, maxRequests: 3, blockDuration: 300000 },
    adminLogin: { windowMs: 300000, maxRequests: 5, blockDuration: 900000 },
    general: { windowMs: 60000, maxRequests: 100, blockDuration: 60000 }
  });
  const [captchaConfig, setCaptchaConfig] = useState(captchaService.getConfig());
  const [cspViolations, setCspViolations] = useState(cspService.getViolations(10));
  const [sslMetrics, setSSLMetrics] = useState(sslService.getMetrics());
  const [sslGrade, setSSLGrade] = useState<{ score: number; grade: string }>({ score: 0, grade: 'F' });

  useEffect(() => {
    loadSecurityData();
    const interval = setInterval(loadSecurityData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadSecurityData = async () => {
    setSecurityMetrics(securityService.getMetrics());
    setCspViolations(cspService.getViolations(10));
    setSSLMetrics(sslService.getMetrics());
    
    // Test SSL configuration
    const sslTest = await sslService.testSSLConfiguration();
    setSSLGrade({ score: sslTest.score, grade: sslTest.grade });
  };

  const testRateLimit = async () => {
    const identifier = securityService.getClientIdentifier();
    const allowed = securityService.checkRateLimit(identifier, 'contact');
    
    if (allowed) {
      toast(() => (
        <SuccessToast
          message="Rate limit test passed!"
          icon={<CheckCircle className="w-5 h-5 text-green-400" />}
        />
      ));
    } else {
      toast(() => (
        <ErrorToast
          message="Rate limit exceeded - request blocked!"
          icon={<AlertTriangle className="w-5 h-5 text-red-400" />}
        />
      ));
    }
    
    loadSecurityData();
  };

  const testCaptcha = async () => {
    try {
      const result = await captchaService.execute('test');
      
      if (result.success) {
        toast(() => (
          <SuccessToast
            message="CAPTCHA test successful!"
            icon={<Shield className="w-5 h-5 text-green-400" />}
          />
        ));
      } else {
        toast(() => (
          <ErrorToast
            message={`CAPTCHA test failed: ${result.error}`}
            icon={<AlertTriangle className="w-5 h-5 text-red-400" />}
          />
        ));
      }
    } catch (error) {
      toast(() => (
        <ErrorToast
          message="CAPTCHA test error"
          icon={<AlertTriangle className="w-5 h-5 text-red-400" />}
        />
      ));
    }
  };

  const updateCaptchaProvider = async (provider: 'recaptcha' | 'hcaptcha' | 'custom' | 'turnstile') => {
    try {
      await captchaService.initialize({ provider });
      setCaptchaConfig(captchaService.getConfig());
      
      toast(() => (
        <SuccessToast
          message={`CAPTCHA provider updated to ${provider}`}
          icon={<Shield className="w-5 h-5 text-green-400" />}
        />
      ));
    } catch (error) {
      toast(() => (
        <ErrorToast
          message="Failed to update CAPTCHA provider"
          icon={<AlertTriangle className="w-5 h-5 text-red-400" />}
        />
      ));
    }
  };

  const clearSecurityLogs = () => {
    securityService.resetSecurityData();
    cspService.clearViolations();
    sslService.resetMetrics();
    loadSecurityData();
    
    toast(() => (
      <InfoToast
        message="Security logs cleared"
        icon={<RefreshCw className="w-5 h-5 text-blue-400" />}
      />
    ));
  };

  const getSecurityScore = () => {
    const metrics = securityMetrics;
    let score = 100;
    
    // Deduct points for security issues
    if (metrics.blockedRequests > 0) score -= Math.min(20, metrics.blockedRequests * 2);
    if (metrics.suspiciousActivity > 0) score -= Math.min(30, metrics.suspiciousActivity * 5);
    if (cspViolations.length > 0) score -= Math.min(25, cspViolations.length * 3);
    if (sslMetrics.certificateErrors > 0) score -= Math.min(25, sslMetrics.certificateErrors * 10);
    
    return Math.max(0, score);
  };

  const getSecurityGrade = (score: number) => {
    if (score >= 95) return { grade: 'A+', color: 'text-green-400' };
    if (score >= 85) return { grade: 'A', color: 'text-green-400' };
    if (score >= 75) return { grade: 'B', color: 'text-yellow-400' };
    if (score >= 65) return { grade: 'C', color: 'text-orange-400' };
    if (score >= 50) return { grade: 'D', color: 'text-red-400' };
    return { grade: 'F', color: 'text-red-500' };
  };

  const securityScore = getSecurityScore();
  const securityGradeInfo = getSecurityGrade(securityScore);

  const tabs = [
    { id: 'overview', label: 'Security Overview', icon: Shield },
    { id: 'rate-limiting', label: 'Rate Limiting', icon: Zap },
    { id: 'captcha', label: 'CAPTCHA', icon: Eye },
    { id: 'csp', label: 'Content Security', icon: Globe },
    { id: 'ssl', label: 'SSL/TLS', icon: Lock }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Security Center</h2>
          <p className="text-gray-400">Advanced security monitoring and protection</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <div className={`text-3xl font-bold ${securityGradeInfo.color}`}>
              {securityGradeInfo.grade}
            </div>
            <div className="text-sm text-gray-400">Security Grade</div>
          </div>
          <button
            onClick={clearSecurityLogs}
            className="px-4 py-2 bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white rounded-xl transition-all duration-200 flex items-center space-x-2"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Clear Logs</span>
          </button>
        </div>
      </div>

      {/* Security Score Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-green-900/50 to-green-800/30 border border-green-500/30 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-300 text-sm font-medium">Security Score</p>
              <p className="text-2xl font-bold text-white">{securityScore}/100</p>
            </div>
            <Shield className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-900/50 to-blue-800/30 border border-blue-500/30 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-300 text-sm font-medium">Blocked Requests</p>
              <p className="text-2xl font-bold text-white">{securityMetrics.blockedRequests}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-blue-400" />
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-900/50 to-purple-800/30 border border-purple-500/30 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-300 text-sm font-medium">CSP Violations</p>
              <p className="text-2xl font-bold text-white">{cspViolations.length}</p>
            </div>
            <Globe className="w-8 h-8 text-purple-400" />
          </div>
        </div>

        <div className="bg-gradient-to-br from-orange-900/50 to-orange-800/30 border border-orange-500/30 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-300 text-sm font-medium">SSL Grade</p>
              <p className="text-2xl font-bold text-white">{sslGrade.grade}</p>
            </div>
            <Lock className="w-8 h-8 text-orange-400" />
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-800/50 rounded-xl p-1">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-purple-600 text-white shadow-lg'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="bg-gray-800/50 rounded-2xl border border-gray-700/50 p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">Security Overview</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-white">Security Status</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Rate Limiting:</span>
                    <span className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-green-400">Active</span>
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">CAPTCHA Protection:</span>
                    <span className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-green-400">{captchaConfig.provider}</span>
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Content Security Policy:</span>
                    <span className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-green-400">Enforced</span>
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">SSL/TLS:</span>
                    <span className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-green-400">Grade {sslGrade.grade}</span>
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-white">Recent Activity</h4>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <Shield className="w-4 h-4 text-blue-400" />
                    <span className="text-gray-300 text-sm">Security services initialized</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Zap className="w-4 h-4 text-yellow-400" />
                    <span className="text-gray-300 text-sm">{securityMetrics.rateLimitHits} rate limit hits</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Globe className="w-4 h-4 text-purple-400" />
                    <span className="text-gray-300 text-sm">{cspViolations.length} CSP violations detected</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Lock className="w-4 h-4 text-green-400" />
                    <span className="text-gray-300 text-sm">{sslMetrics.httpsRequests} HTTPS requests</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex space-x-4">
              <button
                onClick={testRateLimit}
                className="px-4 py-2 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white rounded-xl transition-all duration-200"
              >
                Test Rate Limiting
              </button>
              <button
                onClick={testCaptcha}
                className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-xl transition-all duration-200"
              >
                Test CAPTCHA
              </button>
            </div>
          </div>
        )}

        {activeTab === 'rate-limiting' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">Rate Limiting Configuration</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Object.entries(rateLimitConfig).map(([endpoint, config]) => (
                <div key={endpoint} className="bg-gray-700/50 rounded-xl p-4">
                  <h4 className="font-semibold text-white mb-3 capitalize">{endpoint}</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm text-gray-300 mb-1">Window (ms)</label>
                      <input
                        type="number"
                        value={config.windowMs}
                        onChange={(e) => setRateLimitConfig({
                          ...rateLimitConfig,
                          [endpoint]: { ...config, windowMs: parseInt(e.target.value) }
                        })}
                        className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white text-sm"
                      />
                    </div>
                    <div>
                      <label className="block text-sm text-gray-300 mb-1">Max Requests</label>
                      <input
                        type="number"
                        value={config.maxRequests}
                        onChange={(e) => setRateLimitConfig({
                          ...rateLimitConfig,
                          [endpoint]: { ...config, maxRequests: parseInt(e.target.value) }
                        })}
                        className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white text-sm"
                      />
                    </div>
                    <div>
                      <label className="block text-sm text-gray-300 mb-1">Block Duration (ms)</label>
                      <input
                        type="number"
                        value={config.blockDuration}
                        onChange={(e) => setRateLimitConfig({
                          ...rateLimitConfig,
                          [endpoint]: { ...config, blockDuration: parseInt(e.target.value) }
                        })}
                        className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white text-sm"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="bg-gray-700/50 rounded-xl p-4">
              <h4 className="font-semibold text-white mb-3">Rate Limiting Statistics</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{securityMetrics.totalRequests}</div>
                  <div className="text-sm text-gray-400">Total Requests</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-400">{securityMetrics.blockedRequests}</div>
                  <div className="text-sm text-gray-400">Blocked</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-400">{securityMetrics.rateLimitHits}</div>
                  <div className="text-sm text-gray-400">Rate Limit Hits</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-400">{securityMetrics.suspiciousActivity}</div>
                  <div className="text-sm text-gray-400">Suspicious Activity</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'captcha' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">CAPTCHA Configuration</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">CAPTCHA Provider</label>
                  <select
                    value={captchaConfig.provider}
                    onChange={(e) => updateCaptchaProvider(e.target.value as any)}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white"
                  >
                    <option value="custom">Custom Challenge</option>
                    <option value="recaptcha">Google reCAPTCHA</option>
                    <option value="hcaptcha">hCaptcha</option>
                    <option value="turnstile">Cloudflare Turnstile</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Theme</label>
                  <select
                    value={captchaConfig.theme}
                    onChange={(e) => setCaptchaConfig({ ...captchaConfig, theme: e.target.value as any })}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white"
                  >
                    <option value="dark">Dark</option>
                    <option value="light">Light</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Size</label>
                  <select
                    value={captchaConfig.size}
                    onChange={(e) => setCaptchaConfig({ ...captchaConfig, size: e.target.value as any })}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded-xl text-white"
                  >
                    <option value="normal">Normal</option>
                    <option value="compact">Compact</option>
                    <option value="invisible">Invisible</option>
                  </select>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-white">CAPTCHA Statistics</h4>
                <div className="bg-gray-700/50 rounded-xl p-4">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-300">Provider:</span>
                      <span className="text-white capitalize">{captchaConfig.provider}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Verifications:</span>
                      <span className="text-white">{securityMetrics.captchaVerifications}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Success Rate:</span>
                      <span className="text-green-400">98.5%</span>
                    </div>
                  </div>
                </div>

                <div id="captcha-test-container" className="bg-gray-700/50 rounded-xl p-4">
                  <h5 className="font-medium text-white mb-3">Test CAPTCHA</h5>
                  <button
                    onClick={testCaptcha}
                    className="w-full px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl transition-all duration-200"
                  >
                    Test Current Provider
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'csp' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">Content Security Policy</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-white">CSP Status</h4>
                <div className="bg-gray-700/50 rounded-xl p-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">Policy Active:</span>
                      <span className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-400" />
                        <span className="text-green-400">Yes</span>
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">Violations:</span>
                      <span className="text-white">{cspViolations.length}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">Report-Only Mode:</span>
                      <span className="text-red-400">No</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-white">Recent Violations</h4>
                <div className="bg-gray-700/50 rounded-xl p-4 max-h-64 overflow-y-auto">
                  {cspViolations.length > 0 ? (
                    <div className="space-y-2">
                      {cspViolations.map((violation, index) => (
                        <div key={index} className="text-sm">
                          <div className="text-red-400">{violation.directive}</div>
                          <div className="text-gray-400 truncate">{violation.blockedURI}</div>
                          <div className="text-xs text-gray-500">
                            {new Date(violation.timestamp).toLocaleTimeString()}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-gray-400 text-center">No violations detected</div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'ssl' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">SSL/TLS Configuration</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-white">SSL Status</h4>
                <div className="bg-gray-700/50 rounded-xl p-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">Protocol:</span>
                      <span className="text-green-400">{location.protocol.toUpperCase()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">Grade:</span>
                      <span className={`font-bold ${sslGrade.grade === 'A+' ? 'text-green-400' : sslGrade.grade.startsWith('A') ? 'text-green-400' : 'text-yellow-400'}`}>
                        {sslGrade.grade}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">Score:</span>
                      <span className="text-white">{sslGrade.score}/100</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-white">SSL Metrics</h4>
                <div className="bg-gray-700/50 rounded-xl p-4">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-300">HTTPS Requests:</span>
                      <span className="text-white">{sslMetrics.httpsRequests}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">HTTP Redirects:</span>
                      <span className="text-white">{sslMetrics.httpRedirects}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Mixed Content Blocked:</span>
                      <span className="text-white">{sslMetrics.mixedContentBlocked}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Certificate Errors:</span>
                      <span className="text-white">{sslMetrics.certificateErrors}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SecurityManager;
