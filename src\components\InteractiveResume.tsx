import { AnimatePresence, motion } from 'framer-motion';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { Award, Briefcase, Download, Eye, GraduationCap, Languages, Mail, MapPin, Phone } from 'lucide-react';
import React, { useRef, useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';

const InteractiveResume: React.FC = () => {
  const { t } = useLanguage();
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const resumeRef = useRef<HTMLDivElement>(null);

  // Resume data
  const resumeData = {
    personalInfo: {
      name: 'Nural Bhardwaj',
      title: 'Full Stack Developer and UI/UX Designer',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'San Francisco, CA',
      website: 'https://nuralbhardwaj.me',
      github: 'https://github.com/NuralBhardwaj',
      linkedin: 'https://www.linkedin.com/in/nural-bhardwaj',
    },
    summary: 'Passionate Full Stack Developer with 5+ years of experience in creating innovative web applications and user experiences. Expertise in React, Node.js, and modern web technologies.',
    experience: [
      {
        title: 'Senior Full Stack Developer',
        company: 'TechCorp Solutions',
        location: 'San Francisco, CA',
        period: '2022 - Present',
        achievements: [
          'Led development of 5+ enterprise web applications serving 100K+ users',
          'Improved application performance by 40% through optimization techniques',
          'Mentored junior developers and established coding best practices',
          'Implemented CI/CD pipelines reducing deployment time by 60%'
        ]
      },
      {
        title: 'Full Stack Developer',
        company: 'Digital Innovations Inc.',
        location: 'Austin, TX',
        period: '2020 - 2022',
        achievements: [
          'Developed responsive web applications using React and Node.js',
          'Collaborated with design team to implement pixel-perfect UI/UX',
          'Built RESTful APIs and integrated third-party services',
          'Reduced bug reports by 35% through comprehensive testing'
        ]
      },
      {
        title: 'Frontend Developer',
        company: 'StartupXYZ',
        location: 'Remote',
        period: '2019 - 2020',
        achievements: [
          'Created interactive user interfaces for SaaS platform',
          'Implemented responsive design for mobile and desktop',
          'Optimized website loading speed by 50%',
          'Worked closely with UX designers to improve user experience'
        ]
      }
    ],
    education: [
      {
        degree: 'Bachelor of Science in Computer Science',
        school: 'University of California, Berkeley',
        location: 'Berkeley, CA',
        period: '2015 - 2019',
        gpa: '3.8/4.0'
      }
    ],
    skills: {
      frontend: ['React', 'TypeScript', 'Next.js', 'Vue.js', 'HTML5', 'CSS3', 'Tailwind CSS'],
      backend: ['Node.js', 'Express.js', 'Python', 'Django', 'PostgreSQL', 'MongoDB'],
      tools: ['Git', 'Docker', 'AWS', 'Figma', 'Jest', 'Webpack'],
      design: ['UI/UX Design', 'Responsive Design', 'Prototyping', 'User Research']
    },
    certifications: [
      'AWS Certified Developer Associate',
      'Google Cloud Professional Developer',
      'Meta Frontend Developer Certificate'
    ],
    languages: [
      { name: 'English', level: 'Native' },
      { name: 'Spanish', level: 'Conversational' },
      { name: 'French', level: 'Basic' }
    ]
  };

  const downloadPDF = async () => {
    if (!resumeRef.current) return;
    
    setIsDownloading(true);
    
    try {
      const canvas = await html2canvas(resumeRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      });
      
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      const imgWidth = 210;
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save('Nural_Bhardwaj_Resume.pdf');
    } catch (error) {
      console.error('Error generating PDF:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <section id="resume" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      
      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            {t('resume.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {t('resume.subtitle')}
          </p>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <motion.button
            onClick={downloadPDF}
            disabled={isDownloading}
            className="flex items-center justify-center space-x-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <Download className="w-5 h-5" />
            <span>{isDownloading ? t('resume.generating') : t('resume.download')}</span>
          </motion.button>

          <motion.button
            onClick={() => setIsPreviewOpen(true)}
            className="flex items-center justify-center space-x-3 px-8 py-4 bg-white text-gray-700 border-2 border-gray-300 rounded-xl font-semibold hover:border-blue-500 hover:text-blue-600 transition-all duration-300"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <Eye className="w-5 h-5" />
            <span>{t('resume.preview')}</span>
          </motion.button>
        </motion.div>

        {/* Resume Stats */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          {[
            { icon: Briefcase, label: t('resume.experience'), value: '5+ Years' },
            { icon: Award, label: t('resume.certifications'), value: '3 Certs' },
            { icon: GraduationCap, label: t('resume.education'), value: 'CS Degree' },
            { icon: Languages, label: t('resume.languages'), value: '3 Languages' }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="bg-white/80 backdrop-blur-sm rounded-xl p-6 text-center shadow-lg border border-white/50"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5, scale: 1.05 }}
            >
              <stat.icon className="w-8 h-8 text-blue-600 mx-auto mb-3" />
              <h3 className="font-bold text-gray-800 text-lg">{stat.value}</h3>
              <p className="text-gray-600 text-sm">{stat.label}</p>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Resume Preview Modal */}
      <AnimatePresence>
        {isPreviewOpen && (
          <motion.div
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsPreviewOpen(false)}
          >
            <motion.div
              className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Resume Content */}
              <div ref={resumeRef} className="p-8 bg-white">
                {/* Header */}
                <div className="text-center mb-8 border-b-2 border-gray-200 pb-6">
                  <h1 className="text-3xl font-bold text-gray-800 mb-2">{resumeData.personalInfo.name}</h1>
                  <h2 className="text-xl text-blue-600 mb-4">{resumeData.personalInfo.title}</h2>
                  <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Mail className="w-4 h-4" />
                      <span>{resumeData.personalInfo.email}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Phone className="w-4 h-4" />
                      <span>{resumeData.personalInfo.phone}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      <span>{resumeData.personalInfo.location}</span>
                    </div>
                  </div>
                </div>

                {/* Summary */}
                <div className="mb-6">
                  <h3 className="text-lg font-bold text-gray-800 mb-3 border-l-4 border-blue-500 pl-3">{t('resume.summary')}</h3>
                  <p className="text-gray-700 leading-relaxed">{resumeData.summary}</p>
                </div>

                {/* Experience */}
                <div className="mb-6">
                  <h3 className="text-lg font-bold text-gray-800 mb-4 border-l-4 border-blue-500 pl-3">{t('resume.experience')}</h3>
                  {resumeData.experience.map((exp, index) => (
                    <div key={index} className="mb-4 last:mb-0">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="font-semibold text-gray-800">{exp.title}</h4>
                          <p className="text-blue-600">{exp.company} • {exp.location}</p>
                        </div>
                        <span className="text-sm text-gray-500">{exp.period}</span>
                      </div>
                      <ul className="list-disc list-inside text-gray-700 text-sm space-y-1">
                        {exp.achievements.map((achievement, i) => (
                          <li key={i}>{achievement}</li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>

                {/* Skills */}
                <div className="mb-6">
                  <h3 className="text-lg font-bold text-gray-800 mb-4 border-l-4 border-blue-500 pl-3">{t('resume.skills')}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(resumeData.skills).map(([category, skills]) => (
                      <div key={category}>
                        <h4 className="font-semibold text-gray-800 mb-2 capitalize">{category}</h4>
                        <div className="flex flex-wrap gap-2">
                          {skills.map((skill, index) => (
                            <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Education */}
                <div className="mb-6">
                  <h3 className="text-lg font-bold text-gray-800 mb-4 border-l-4 border-blue-500 pl-3">{t('resume.education')}</h3>
                  {resumeData.education.map((edu, index) => (
                    <div key={index} className="mb-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-semibold text-gray-800">{edu.degree}</h4>
                          <p className="text-blue-600">{edu.school} • {edu.location}</p>
                          <p className="text-sm text-gray-600">GPA: {edu.gpa}</p>
                        </div>
                        <span className="text-sm text-gray-500">{edu.period}</span>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Certifications & Languages */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-bold text-gray-800 mb-3 border-l-4 border-blue-500 pl-3">{t('resume.certifications')}</h3>
                    <ul className="space-y-2">
                      {resumeData.certifications.map((cert, index) => (
                        <li key={index} className="text-gray-700 text-sm flex items-center gap-2">
                          <Award className="w-4 h-4 text-blue-600" />
                          {cert}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-bold text-gray-800 mb-3 border-l-4 border-blue-500 pl-3">{t('resume.languages')}</h3>
                    <ul className="space-y-2">
                      {resumeData.languages.map((lang, index) => (
                        <li key={index} className="text-gray-700 text-sm flex justify-between">
                          <span>{lang.name}</span>
                          <span className="text-blue-600">{lang.level}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              {/* Close Button */}
              <div className="p-4 border-t bg-gray-50 rounded-b-2xl">
                <button
                  onClick={() => setIsPreviewOpen(false)}
                  className="w-full py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  {t('common.close')}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default InteractiveResume;
