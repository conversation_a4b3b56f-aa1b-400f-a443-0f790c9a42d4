// Advanced Memory Optimization Service
// Manages memory usage, prevents leaks, and optimizes performance

interface MemoryMetrics {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  memoryUsagePercentage: number;
  isHighMemoryUsage: boolean;
}

interface OptimizationConfig {
  enableMemoryMonitoring: boolean;
  enableGarbageCollection: boolean;
  enableImageCleanup: boolean;
  enableEventListenerCleanup: boolean;
  memoryThreshold: number; // Percentage
  cleanupInterval: number; // milliseconds
}

class MemoryOptimizationService {
  private static instance: MemoryOptimizationService;
  private config: OptimizationConfig;
  private cleanupInterval: number | null = null;
  private observers: Set<IntersectionObserver> = new Set();
  private eventListeners: Map<Element, Array<{ event: string; handler: EventListener }>> = new Map();

  private defaultConfig: OptimizationConfig = {
    enableMemoryMonitoring: true,
    enableGarbageCollection: false, // Disabled for less aggressive optimization
    enableImageCleanup: false, // Disabled for less aggressive optimization
    enableEventListenerCleanup: true,
    memoryThreshold: 90, // 90% memory usage threshold (less aggressive)
    cleanupInterval: 60000 // 60 seconds (less frequent)
  };

  private constructor() {
    this.config = { ...this.defaultConfig };
    this.startMemoryOptimization();
  }

  public static getInstance(): MemoryOptimizationService {
    if (!MemoryOptimizationService.instance) {
      MemoryOptimizationService.instance = new MemoryOptimizationService();
    }
    return MemoryOptimizationService.instance;
  }

  // Start memory optimization
  public startMemoryOptimization(): void {
    if (!this.config.enableMemoryMonitoring) return;

    // Start periodic cleanup
    this.cleanupInterval = window.setInterval(() => {
      this.performMemoryCleanup();
    }, this.config.cleanupInterval);

    // Monitor memory usage
    this.monitorMemoryUsage();

    console.log('🧠 Memory optimization service started');
  }

  // Get current memory metrics
  public getMemoryMetrics(): MemoryMetrics | null {
    if (!('memory' in performance)) {
      return null;
    }

    const memory = (performance as any).memory;
    const usedJSHeapSize = memory.usedJSHeapSize;
    const totalJSHeapSize = memory.totalJSHeapSize;
    const jsHeapSizeLimit = memory.jsHeapSizeLimit;
    const memoryUsagePercentage = (usedJSHeapSize / jsHeapSizeLimit) * 100;

    return {
      usedJSHeapSize,
      totalJSHeapSize,
      jsHeapSizeLimit,
      memoryUsagePercentage,
      isHighMemoryUsage: memoryUsagePercentage > this.config.memoryThreshold
    };
  }

  // Monitor memory usage and trigger cleanup if needed
  private monitorMemoryUsage(): void {
    const checkMemory = () => {
      const metrics = this.getMemoryMetrics();
      if (metrics && metrics.isHighMemoryUsage) {
        console.warn('🚨 High memory usage detected:', metrics.memoryUsagePercentage.toFixed(2) + '%');
        this.performMemoryCleanup();
      }
    };

    // Check memory every 10 seconds
    setInterval(checkMemory, 10000);
  }

  // Perform comprehensive memory cleanup
  public performMemoryCleanup(): void {
    if (this.config.enableImageCleanup) {
      this.cleanupImages();
    }

    if (this.config.enableEventListenerCleanup) {
      this.cleanupEventListeners();
    }

    if (this.config.enableGarbageCollection) {
      this.triggerGarbageCollection();
    }

    this.cleanupObservers();
    this.cleanupUnusedElements();

    console.log('🧹 Memory cleanup performed');
  }

  // Cleanup images that are no longer visible
  private cleanupImages(): void {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      // Remove src from images that are far from viewport
      const rect = img.getBoundingClientRect();
      const isVisible = rect.top < window.innerHeight + 1000 && rect.bottom > -1000;
      
      if (!isVisible && img.src && !img.dataset.originalSrc) {
        img.dataset.originalSrc = img.src;
        img.src = '';
      } else if (isVisible && img.dataset.originalSrc && !img.src) {
        img.src = img.dataset.originalSrc;
        delete img.dataset.originalSrc;
      }
    });
  }

  // Cleanup event listeners from removed elements
  private cleanupEventListeners(): void {
    this.eventListeners.forEach((listeners, element) => {
      if (!document.contains(element)) {
        listeners.forEach(({ event, handler }) => {
          element.removeEventListener(event, handler);
        });
        this.eventListeners.delete(element);
      }
    });
  }

  // Cleanup intersection observers
  private cleanupObservers(): void {
    this.observers.forEach(observer => {
      // Check if observer is still needed
      const targets = observer.takeRecords();
      if (targets.length === 0) {
        observer.disconnect();
        this.observers.delete(observer);
      }
    });
  }

  // Remove unused DOM elements
  private cleanupUnusedElements(): void {
    // Remove empty text nodes
    const walker = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          return node.textContent?.trim() === '' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;
        }
      }
    );

    const emptyNodes: Node[] = [];
    let node;
    while (node = walker.nextNode()) {
      emptyNodes.push(node);
    }

    emptyNodes.forEach(node => {
      if (node.parentNode) {
        node.parentNode.removeChild(node);
      }
    });
  }

  // Trigger garbage collection (if available)
  private triggerGarbageCollection(): void {
    if ('gc' in window) {
      (window as any).gc();
    }
  }

  // Register an observer for cleanup tracking
  public registerObserver(observer: IntersectionObserver): void {
    this.observers.add(observer);
  }

  // Register event listener for cleanup tracking
  public registerEventListener(element: Element, event: string, handler: EventListener): void {
    if (!this.eventListeners.has(element)) {
      this.eventListeners.set(element, []);
    }
    this.eventListeners.get(element)!.push({ event, handler });
    element.addEventListener(event, handler);
  }

  // Stop memory optimization
  public stop(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    // Cleanup all observers
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();

    // Cleanup all tracked event listeners
    this.eventListeners.forEach((listeners, element) => {
      listeners.forEach(({ event, handler }) => {
        element.removeEventListener(event, handler);
      });
    });
    this.eventListeners.clear();

    console.log('🧠 Memory optimization service stopped');
  }

  // Get optimization configuration
  public getConfig(): OptimizationConfig {
    return { ...this.config };
  }

  // Update optimization configuration
  public updateConfig(newConfig: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// Export singleton instance
export const memoryOptimizationService = MemoryOptimizationService.getInstance();
export default MemoryOptimizationService;
