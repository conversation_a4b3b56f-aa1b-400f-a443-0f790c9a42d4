import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Rocket, Clock, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';

interface DeploymentStatus {
  status: 'idle' | 'triggered' | 'building' | 'deploying' | 'success' | 'error';
  message: string;
  timestamp?: string;
  estimatedTime?: number;
}

export const AutoDeployNotification: React.FC = () => {
  const [deployment, setDeployment] = useState<DeploymentStatus>({
    status: 'idle',
    message: ''
  });
  const [isVisible, setIsVisible] = useState(false);
  const [countdown, setCountdown] = useState(0);

  useEffect(() => {
    // Listen for rebuild trigger events
    const handleRebuildTriggered = (event: CustomEvent) => {
      setDeployment({
        status: 'triggered',
        message: 'Website rebuild triggered! Your changes will be live soon.',
        timestamp: event.detail.timestamp,
        estimatedTime: 180 // 3 minutes
      });
      setIsVisible(true);
      setCountdown(180);

      // Simulate deployment phases
      setTimeout(() => {
        setDeployment(prev => ({
          ...prev,
          status: 'building',
          message: 'Building your portfolio with latest changes...'
        }));
      }, 5000);

      setTimeout(() => {
        setDeployment(prev => ({
          ...prev,
          status: 'deploying',
          message: 'Deploying to GitHub Pages...'
        }));
      }, 120000); // 2 minutes

      setTimeout(() => {
        setDeployment(prev => ({
          ...prev,
          status: 'success',
          message: 'Your portfolio has been updated successfully! 🎉'
        }));
        
        // Auto-hide after success
        setTimeout(() => {
          setIsVisible(false);
        }, 5000);
      }, 180000); // 3 minutes
    };

    window.addEventListener('websiteRebuildTriggered', handleRebuildTriggered as EventListener);

    return () => {
      window.removeEventListener('websiteRebuildTriggered', handleRebuildTriggered as EventListener);
    };
  }, []);

  // Countdown timer
  useEffect(() => {
    if (countdown > 0 && deployment.status !== 'success') {
      const timer = setTimeout(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown, deployment.status]);

  const getIcon = () => {
    switch (deployment.status) {
      case 'triggered':
        return <Rocket className="w-5 h-5 text-blue-500" />;
      case 'building':
        return <RefreshCw className="w-5 h-5 text-yellow-500 animate-spin" />;
      case 'deploying':
        return <Clock className="w-5 h-5 text-orange-500" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return null;
    }
  };

  const getProgressColor = () => {
    switch (deployment.status) {
      case 'triggered':
        return 'bg-blue-500';
      case 'building':
        return 'bg-yellow-500';
      case 'deploying':
        return 'bg-orange-500';
      case 'success':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getProgressPercentage = () => {
    if (!deployment.estimatedTime) return 0;
    const elapsed = deployment.estimatedTime - countdown;
    return Math.min((elapsed / deployment.estimatedTime) * 100, 100);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 50, scale: 0.9 }}
          className="fixed bottom-4 right-4 z-50 max-w-sm"
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
            {/* Progress bar */}
            <div className="h-1 bg-gray-200 dark:bg-gray-700">
              <motion.div
                className={`h-full ${getProgressColor()}`}
                initial={{ width: 0 }}
                animate={{ width: `${getProgressPercentage()}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>

            <div className="p-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  {getIcon()}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
                      Auto-Deploy Status
                    </h4>
                    {countdown > 0 && deployment.status !== 'success' && (
                      <span className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                        {formatTime(countdown)}
                      </span>
                    )}
                  </div>
                  
                  <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {deployment.message}
                  </p>

                  {deployment.status === 'success' && (
                    <div className="mt-2">
                      <a
                        href="https://nuralbhardwaj.me"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                      >
                        View Live Site →
                      </a>
                    </div>
                  )}
                </div>

                <button
                  onClick={() => setIsVisible(false)}
                  className="flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <span className="sr-only">Close</span>
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
