// Advanced Security Service for Portfolio Protection
// Implements rate limiting, bot protection, and security monitoring

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  blockDuration: number; // Block duration in milliseconds
}

export interface SecurityMetrics {
  totalRequests: number;
  blockedRequests: number;
  suspiciousActivity: number;
  lastAttack: number | null;
  rateLimitHits: number;
  captchaVerifications: number;
}

export interface RequestLog {
  timestamp: number;
  ip: string;
  userAgent: string;
  endpoint: string;
  blocked: boolean;
  reason?: string;
}

class SecurityService {
  private static instance: SecurityService;
  private requestCounts: Map<string, number[]> = new Map();
  private blockedIPs: Map<string, number> = new Map();
  private requestLogs: RequestLog[] = [];
  private metrics: SecurityMetrics = {
    totalRequests: 0,
    blockedRequests: 0,
    suspiciousActivity: 0,
    lastAttack: null,
    rateLimitHits: 0,
    captchaVerifications: 0
  };

  // Enhanced rate limiting configurations for different endpoints
  private rateLimits: Map<string, RateLimitConfig> = new Map([
    ['contact', { windowMs: 60000, maxRequests: 2, blockDuration: 600000 }], // 2 requests per minute, 10min block
    ['admin-login', { windowMs: 300000, maxRequests: 3, blockDuration: 1800000 }], // 3 attempts per 5min, 30min block
    ['general', { windowMs: 60000, maxRequests: 50, blockDuration: 120000 }], // 50 requests per minute, 2min block
    ['api', { windowMs: 60000, maxRequests: 20, blockDuration: 300000 }], // 20 API calls per minute, 5min block
    ['download', { windowMs: 300000, maxRequests: 10, blockDuration: 600000 }], // 10 downloads per 5min, 10min block
    ['search', { windowMs: 60000, maxRequests: 30, blockDuration: 180000 }], // 30 searches per minute, 3min block
  ]);

  private constructor() {
    this.loadMetrics();
    this.startCleanupInterval();
  }

  public static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService();
    }
    return SecurityService.instance;
  }

  // Rate limiting implementation
  public checkRateLimit(identifier: string, endpoint: string = 'general'): boolean {
    const config = this.rateLimits.get(endpoint) || this.rateLimits.get('general')!;
    const now = Date.now();
    
    // Check if IP is currently blocked
    const blockUntil = this.blockedIPs.get(identifier);
    if (blockUntil && now < blockUntil) {
      this.logRequest(identifier, endpoint, true, 'IP blocked');
      this.metrics.blockedRequests++;
      return false;
    }

    // Remove expired block
    if (blockUntil && now >= blockUntil) {
      this.blockedIPs.delete(identifier);
    }

    // Get request history for this identifier
    const requests = this.requestCounts.get(identifier) || [];
    
    // Remove requests outside the time window
    const windowStart = now - config.windowMs;
    const recentRequests = requests.filter(timestamp => timestamp > windowStart);
    
    // Check if rate limit exceeded
    if (recentRequests.length >= config.maxRequests) {
      // Block the IP
      this.blockedIPs.set(identifier, now + config.blockDuration);
      this.logRequest(identifier, endpoint, true, 'Rate limit exceeded');
      this.metrics.blockedRequests++;
      this.metrics.rateLimitHits++;
      this.metrics.lastAttack = now;
      
      // Alert for suspicious activity
      if (recentRequests.length > config.maxRequests * 2) {
        this.metrics.suspiciousActivity++;
        this.alertSuspiciousActivity(identifier, endpoint, recentRequests.length);
      }
      
      return false;
    }

    // Add current request
    recentRequests.push(now);
    this.requestCounts.set(identifier, recentRequests);
    
    // Log successful request
    this.logRequest(identifier, endpoint, false);
    this.metrics.totalRequests++;
    
    return true;
  }

  // Get client identifier (IP simulation for client-side)
  public getClientIdentifier(): string {
    // In a real application, this would be the client's IP address
    // For client-side demo, we'll use a combination of factors
    const fingerprint = this.generateFingerprint();
    return fingerprint;
  }

  // Generate browser fingerprint for identification
  private generateFingerprint(): string {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Security fingerprint', 2, 2);
    }
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL(),
      navigator.hardwareConcurrency || 0,
      navigator.deviceMemory || 0
    ].join('|');
    
    // Simple hash function
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  // Input sanitization
  public sanitizeInput(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .replace(/script/gi, '') // Remove script references
      .trim();
  }

  // Validate email format
  public validateEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  // Detect suspicious patterns
  public detectSuspiciousContent(content: string): boolean {
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+=/i,
      /eval\(/i,
      /document\.cookie/i,
      /window\.location/i,
      /alert\(/i,
      /confirm\(/i,
      /prompt\(/i,
      /<iframe/i,
      /<object/i,
      /<embed/i,
      /data:text\/html/i,
      /vbscript:/i,
      /expression\(/i
    ];

    return suspiciousPatterns.some(pattern => pattern.test(content));
  }

  // Log security events
  private logRequest(identifier: string, endpoint: string, blocked: boolean, reason?: string): void {
    const log: RequestLog = {
      timestamp: Date.now(),
      ip: identifier,
      userAgent: navigator.userAgent,
      endpoint,
      blocked,
      reason
    };

    this.requestLogs.push(log);
    
    // Keep only last 1000 logs
    if (this.requestLogs.length > 1000) {
      this.requestLogs = this.requestLogs.slice(-1000);
    }

    // Save to localStorage for persistence
    this.saveMetrics();
  }

  // Alert for suspicious activity
  private alertSuspiciousActivity(identifier: string, endpoint: string, requestCount: number): void {
    console.warn(`🚨 Suspicious activity detected:`, {
      identifier,
      endpoint,
      requestCount,
      timestamp: new Date().toISOString()
    });

    // In production, this would send alerts to monitoring systems
    if (typeof gtag !== 'undefined') {
      gtag('event', 'security_alert', {
        event_category: 'Security',
        event_label: 'Suspicious Activity',
        value: requestCount,
        custom_parameters: {
          identifier,
          endpoint,
          request_count: requestCount
        }
      });
    }
  }

  // Get security metrics
  public getMetrics(): SecurityMetrics {
    return { ...this.metrics };
  }

  // Get recent security logs
  public getRecentLogs(limit: number = 50): RequestLog[] {
    return this.requestLogs.slice(-limit);
  }

  // Check if IP is currently blocked
  public isBlocked(identifier: string): boolean {
    const blockUntil = this.blockedIPs.get(identifier);
    return blockUntil ? Date.now() < blockUntil : false;
  }

  // Get remaining block time
  public getBlockTimeRemaining(identifier: string): number {
    const blockUntil = this.blockedIPs.get(identifier);
    if (!blockUntil) return 0;
    
    const remaining = blockUntil - Date.now();
    return Math.max(0, remaining);
  }

  // Manually unblock an IP (admin function)
  public unblockIP(identifier: string): boolean {
    return this.blockedIPs.delete(identifier);
  }

  // Advanced threat detection
  public detectThreat(request: any): { isThreat: boolean; reason?: string; severity: 'low' | 'medium' | 'high' } {
    const threats = [];
    let severity: 'low' | 'medium' | 'high' = 'low';

    // SQL Injection patterns
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(\'|\"|;|--|\*|\/\*|\*\/)/,
      /(\bOR\b|\bAND\b).*(=|<|>)/i
    ];

    // XSS patterns
    const xssPatterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe[^>]*>.*?<\/iframe>/gi,
      /eval\s*\(/gi,
      /document\.cookie/gi
    ];

    // Path traversal patterns
    const pathTraversalPatterns = [
      /\.\.\//g,
      /\.\.\\/g,
      /%2e%2e%2f/gi,
      /%2e%2e%5c/gi
    ];

    // Command injection patterns
    const commandInjectionPatterns = [
      /(\||&|;|\$\(|`)/,
      /(nc|netcat|wget|curl|ping|nslookup)/i,
      /(rm|del|format|fdisk)/i
    ];

    const userAgent = request.userAgent || '';
    const url = request.url || '';
    const body = JSON.stringify(request.body || {});
    const params = JSON.stringify(request.params || {});

    // Check for SQL injection
    const testString = `${url} ${body} ${params}`;
    if (sqlPatterns.some(pattern => pattern.test(testString))) {
      threats.push('SQL Injection attempt detected');
      severity = 'high';
    }

    // Check for XSS
    if (xssPatterns.some(pattern => pattern.test(testString))) {
      threats.push('XSS attempt detected');
      severity = 'high';
    }

    // Check for path traversal
    if (pathTraversalPatterns.some(pattern => pattern.test(testString))) {
      threats.push('Path traversal attempt detected');
      severity = 'medium';
    }

    // Check for command injection
    if (commandInjectionPatterns.some(pattern => pattern.test(testString))) {
      threats.push('Command injection attempt detected');
      severity = 'high';
    }

    // Check for suspicious user agents
    const suspiciousAgents = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /scanner/i, /hack/i, /exploit/i, /injection/i,
      /sqlmap/i, /nikto/i, /nmap/i, /burp/i
    ];

    if (suspiciousAgents.some(pattern => pattern.test(userAgent))) {
      threats.push('Suspicious user agent detected');
      severity = severity === 'high' ? 'high' : 'medium';
    }

    // Check for rapid requests (potential DDoS)
    const identifier = this.getClientIdentifier();
    const recentRequests = this.requestCounts.get(identifier) || [];
    if (recentRequests.length > 20) { // More than 20 requests in window
      threats.push('Potential DDoS attack detected');
      severity = 'high';
    }

    return {
      isThreat: threats.length > 0,
      reason: threats.join(', '),
      severity
    };
  }

  // Enhanced security check with threat detection
  public performSecurityCheck(request: any, endpoint: string = 'general'): { allowed: boolean; reason?: string } {
    const identifier = this.getClientIdentifier();

    // First check rate limiting
    if (!this.checkRateLimit(identifier, endpoint)) {
      return { allowed: false, reason: 'Rate limit exceeded' };
    }

    // Then check for threats
    const threatAnalysis = this.detectThreat(request);
    if (threatAnalysis.isThreat) {
      // Block immediately for high severity threats
      if (threatAnalysis.severity === 'high') {
        this.blockedIPs.set(identifier, Date.now() + 3600000); // 1 hour block
        this.metrics.suspiciousActivity++;
        this.metrics.lastAttack = Date.now();
      }

      this.logRequest(identifier, endpoint, true, threatAnalysis.reason);
      return { allowed: false, reason: threatAnalysis.reason };
    }

    return { allowed: true };
  }

  // Update rate limit configuration
  public updateRateLimit(endpoint: string, config: RateLimitConfig): void {
    this.rateLimits.set(endpoint, config);
  }

  // Cleanup expired entries
  private startCleanupInterval(): void {
    setInterval(() => {
      const now = Date.now();
      
      // Clean up expired request counts
      for (const [identifier, requests] of this.requestCounts.entries()) {
        const validRequests = requests.filter(timestamp => now - timestamp < 3600000); // Keep 1 hour
        if (validRequests.length === 0) {
          this.requestCounts.delete(identifier);
        } else {
          this.requestCounts.set(identifier, validRequests);
        }
      }
      
      // Clean up expired blocks
      for (const [identifier, blockUntil] of this.blockedIPs.entries()) {
        if (now >= blockUntil) {
          this.blockedIPs.delete(identifier);
        }
      }
      
      // Clean up old logs
      this.requestLogs = this.requestLogs.filter(log => now - log.timestamp < 86400000); // Keep 24 hours
      
    }, 300000); // Run every 5 minutes
  }

  // Save metrics to localStorage
  private saveMetrics(): void {
    try {
      localStorage.setItem('security_metrics', JSON.stringify(this.metrics));
      localStorage.setItem('security_logs', JSON.stringify(this.requestLogs.slice(-100))); // Save last 100 logs
    } catch (error) {
      console.error('Failed to save security metrics:', error);
    }
  }

  // Load metrics from localStorage
  private loadMetrics(): void {
    try {
      const savedMetrics = localStorage.getItem('security_metrics');
      if (savedMetrics) {
        this.metrics = { ...this.metrics, ...JSON.parse(savedMetrics) };
      }
      
      const savedLogs = localStorage.getItem('security_logs');
      if (savedLogs) {
        this.requestLogs = JSON.parse(savedLogs);
      }
    } catch (error) {
      console.error('Failed to load security metrics:', error);
    }
  }

  // Reset all security data (admin function)
  public resetSecurityData(): void {
    this.requestCounts.clear();
    this.blockedIPs.clear();
    this.requestLogs = [];
    this.metrics = {
      totalRequests: 0,
      blockedRequests: 0,
      suspiciousActivity: 0,
      lastAttack: null,
      rateLimitHits: 0,
      captchaVerifications: 0
    };
    this.saveMetrics();
  }
}

// Export singleton instance
export const securityService = SecurityService.getInstance();
