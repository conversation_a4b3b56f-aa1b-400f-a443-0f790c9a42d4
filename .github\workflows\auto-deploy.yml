name: 🚀 Auto Deploy Portfolio

on:
  # Trigger on push to main branch
  push:
    branches: [ main ]
    paths:
      - 'cms-data.json'
      - 'src/**'
      - 'public/**'
      - 'index.html'
      - 'package.json'
      - 'vite.config.ts'
  
  # Trigger on manual dispatch
  workflow_dispatch:
    inputs:
      reason:
        description: 'Reason for manual deployment'
        required: false
        default: 'Manual deployment'

  # Trigger on repository dispatch (for webhook)
  repository_dispatch:
    types: [cms-update]

# Set permissions for GitHub token
permissions:
  contents: write
  pages: write
  id-token: write

# Allow only one concurrent deployment
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # Build job
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: 🔧 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: 📦 Install Dependencies
      run: |
        npm ci --legacy-peer-deps
        echo "✅ Dependencies installed successfully"

    - name: 🔍 Verify CMS Data
      run: |
        if [ -f "cms-data.json" ]; then
          echo "✅ CMS data file found"
          echo "📊 CMS data preview:"
          head -20 cms-data.json
        else
          echo "⚠️ No CMS data file found, using defaults"
        fi

    - name: 🏗️ Build Portfolio
      run: |
        echo "🚀 Starting build process..."

        # Set environment variables for build
        export NODE_ENV=production
        export CI=true

        # Run build with error handling
        if npm run build; then
          echo "✅ Build completed successfully"
        else
          echo "❌ Build failed"
          npm run build -- --verbose
          exit 1
        fi

        # Verify build output
        if [ -d "dist" ]; then
          echo "📁 Build directory created"
          echo "📊 Build contents:"
          ls -la dist/
          echo "📊 Build size:"
          du -sh dist/
        else
          echo "❌ Build failed - no dist directory"
          exit 1
        fi

    - name: 🔍 Verify Build Artifacts
      run: |
        echo "🔍 Verifying build artifacts..."
        if [ ! -d "dist" ]; then
          echo "❌ No dist directory found"
          exit 1
        fi

        if [ ! -f "dist/index.html" ]; then
          echo "❌ No index.html found in dist"
          exit 1
        fi

        echo "✅ Build artifacts verified"
        echo "📊 Artifact details:"
        find dist -type f -name "*.html" -o -name "*.js" -o -name "*.css" | head -10

    - name: 📤 Upload Build Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-files
        path: './dist'

  # Deploy job
  deploy:
    runs-on: ubuntu-latest
    needs: build

    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4

    - name: 📥 Download Build Artifacts
      uses: actions/download-artifact@v4
      with:
        name: build-files
        path: ./dist

    - name: 🚀 Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
        publish_branch: gh-pages
        force_orphan: true
        user_name: 'github-actions[bot]'
        user_email: 'github-actions[bot]@users.noreply.github.com'
        commit_message: '🚀 Deploy: ${{ github.event.head_commit.message }}'
        cname: nuralbhardwaj.me

    - name: ✅ Deployment Success
      run: |
        echo "🎉 Portfolio deployed successfully!"
        echo "🌍 Live URL: https://nuralbhardwaj.me"
        echo "⏰ Deployed at: $(date)"

  # Notification job
  notify:
    runs-on: ubuntu-latest
    needs: [build, deploy]
    if: always()
    
    steps:
    - name: 📢 Deployment Status
      run: |
        if [ "${{ needs.deploy.result }}" == "success" ]; then
          echo "✅ AUTO-DEPLOYMENT SUCCESSFUL!"
          echo "🌍 Your portfolio has been automatically updated"
          echo "🔗 Live site: https://nuralbhardwaj.me"
          echo "⚡ Changes are now live worldwide!"
        else
          echo "❌ AUTO-DEPLOYMENT FAILED!"
          echo "🔧 Please check the workflow logs"
        fi
