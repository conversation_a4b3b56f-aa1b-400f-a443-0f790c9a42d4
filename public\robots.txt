# Robots.txt for Nural Bhardwaj - Professional Developer Portfolio
# Generated automatically by Advanced Portfolio CMS
# Optimized for maximum SEO performance

# Allow all search engines to crawl the site
User-agent: *
Allow: /

# Sitemap locations for better indexing
Sitemap: https://nuralbhardwaj.me/sitemap.xml
Sitemap: https://nuralbhardwaj.me/sitemap-images.xml
Sitemap: https://nuralbhardwaj.me/sitemap-videos.xml

# Crawl delay for better server performance
Crawl-delay: 1

# Disallow admin and private areas
Disallow: /admin
Disallow: /admin/*
Disallow: /private/
Disallow: /temp/
Disallow: /.git/
Disallow: /node_modules/
Disallow: /src/
Disallow: /build/
Disallow: /.env
Disallow: /package.json
Disallow: /package-lock.json

# Allow important pages for SEO
Allow: /
Allow: /#about
Allow: /#projects
Allow: /#blog
Allow: /#contact
Allow: /#resume
Allow: /#skills
Allow: /#experience
Allow: /services/
Allow: /technologies/

# Specific rules for major search engines
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Slurp
Allow: /
Crawl-delay: 1

User-agent: DuckDuckBot
Allow: /
Crawl-delay: 1

User-agent: Baiduspider
Allow: /
Crawl-delay: 2

User-agent: YandexBot
Allow: /
Crawl-delay: 1

User-agent: facebookexternalhit
Allow: /

User-agent: Twitterbot
Allow: /

User-agent: LinkedInBot
Allow: /

User-agent: WhatsApp
Allow: /

User-agent: TelegramBot
Allow: /

# Block bad bots and scrapers
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

User-agent: SemrushBot
Disallow: /

User-agent: MajesticSEO
Disallow: /

# Additional sitemap references
Sitemap: https://nuralbhardwaj.me/sitemap.xml
Sitemap: https://nuralbhardwaj.me/rss.xml

# Host directive for preferred domain
Host: nuralbhardwaj.me

# Cache directive for better performance
Cache-delay: 86400
