import React, { useEffect, useState, useRef } from 'react';
import { motion } from 'framer-motion';

interface BeeParticleSystemProps {
  count?: number;
  className?: string;
  showTrails?: boolean;
}

interface BeeParticle {
  id: number;
  x: number;
  y: number;
  targetX: number;
  targetY: number;
  delay: number;
  size: number;
  speed: number;
  direction: number;
  amplitude: number;
  frequency: number;
}

const BeeParticleSystem: React.FC<BeeParticleSystemProps> = ({
  count = 12,
  className = '',
  showTrails = true,
}) => {
  const [bees, setBees] = useState<BeeParticle[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // Initialize bees with independent flight patterns
  useEffect(() => {
    const initialBees: BeeParticle[] = Array.from({ length: count }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
      targetX: Math.random() * window.innerWidth,
      targetY: Math.random() * window.innerHeight,
      delay: i * 0.2,
      size: 0.6 + Math.random() * 0.5,
      speed: 0.5 + Math.random() * 1,
      direction: Math.random() * Math.PI * 2, // Random initial direction
      amplitude: 20 + Math.random() * 40, // Flight path amplitude
      frequency: 0.01 + Math.random() * 0.02, // Flight oscillation frequency
    }));
    setBees(initialBees);
  }, [count]);

  // Independent bee movement - no mouse following
  useEffect(() => {
    const interval = setInterval(() => {
      setBees(prevBees =>
        prevBees.map(bee => {
          // Update direction and position for natural flight
          const time = Date.now() * bee.frequency;

          // Calculate new position with sine wave movement
          let newDirection = bee.direction + 0.02;
          const oscillation = Math.sin(time) * bee.amplitude * 0.01;

          let newX = bee.x + Math.cos(newDirection) * bee.speed + oscillation;
          let newY = bee.y + Math.sin(newDirection) * bee.speed + Math.sin(time * 2) * 0.5;

          // Bounce off screen edges
          if (newX < 0 || newX > window.innerWidth) {
            newDirection = Math.PI - newDirection;
            newX = Math.max(0, Math.min(window.innerWidth, newX));
          }
          if (newY < 0 || newY > window.innerHeight) {
            newDirection = -newDirection;
            newY = Math.max(0, Math.min(window.innerHeight, newY));
          }

          return {
            ...bee,
            x: newX,
            y: newY,
            direction: newDirection,
          };
        })
      );
    }, 16); // ~60fps

    return () => clearInterval(interval);
  }, []);

  return (
    <div ref={containerRef} className={`fixed inset-0 pointer-events-none z-40 ${className}`}>
      {bees.map((bee) => (
        <BeeComponent
          key={bee.id}
          bee={bee}
          showTrail={showTrails}
        />
      ))}
    </div>
  );
};

interface BeeComponentProps {
  bee: BeeParticle;
  showTrail: boolean;
}

const BeeComponent: React.FC<BeeComponentProps> = ({ bee, showTrail }) => {
  // Calculate movement direction for rotation
  const getRotation = () => {
    const deltaX = bee.targetX - bee.x;
    const deltaY = bee.targetY - bee.y;
    return Math.atan2(deltaY, deltaX) * (180 / Math.PI);
  };

  return (
    <motion.div
      className="absolute"
      style={{
        left: bee.x - 15,
        top: bee.y - 15,
        scale: bee.size,
      }}
      animate={{
        rotate: getRotation(),
      }}
      transition={{
        type: "spring",
        stiffness: 100,
        damping: 20,
      }}
    >
      {/* Elegant Trail Effect */}
      {showTrail && (
        <motion.div
          className="absolute inset-0"
          animate={{
            scale: [1, 1.8, 1],
            opacity: [0.3, 0.05, 0.3],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
            delay: bee.delay,
          }}
        >
          <div className="w-8 h-8 bg-gradient-to-r from-yellow-200/25 via-amber-300/20 to-yellow-100/25 rounded-full blur-md"></div>
        </motion.div>
      )}

      {/* Subtle sparkle trail */}
      {showTrail && (
        <motion.div
          className="absolute inset-0"
          animate={{
            rotate: [0, 360],
            scale: [0.6, 1.4, 0.6],
            opacity: [0.15, 0.02, 0.15],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "linear",
            delay: bee.delay * 0.7,
          }}
        >
          <div className="w-10 h-10 bg-gradient-to-r from-white/10 via-yellow-100/15 to-amber-100/10 rounded-full blur-lg"></div>
        </motion.div>
      )}

      {/* Beautiful Realistic Bee */}
      <motion.div
        className="relative w-8 h-8"
        animate={{
          y: [0, -1.5, 0],
          rotate: [0, 2, -2, 0],
        }}
        transition={{
          duration: 2 + bee.delay * 0.3,
          repeat: Infinity,
          ease: "easeInOut",
          delay: bee.delay,
        }}
      >
        {/* Elegant Bee Body */}
        <div className="relative w-6 h-5 mx-auto mt-1.5">
          {/* Soft Body Shadow */}
          <div className="absolute inset-0 bg-black/15 rounded-full transform translate-y-0.5 blur-sm"></div>

          {/* Main Body - Elegant Design */}
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 via-yellow-300 to-amber-400 rounded-full shadow-lg border border-yellow-100/50">
            {/* Refined Black Stripes */}
            <div className="absolute top-1 left-0.5 right-0.5 h-1 bg-gradient-to-r from-gray-800 via-black to-gray-800 rounded-full opacity-80"></div>
            <div className="absolute bottom-1 left-0.5 right-0.5 h-1 bg-gradient-to-r from-gray-800 via-black to-gray-800 rounded-full opacity-80"></div>

            {/* Subtle Body Highlight */}
            <div className="absolute top-0.5 left-1 w-2.5 h-1.5 bg-gradient-to-br from-yellow-100/60 to-transparent rounded-full blur-sm"></div>

            {/* Body Texture */}
            <div className="absolute inset-0.5 bg-gradient-to-br from-yellow-200/30 to-transparent rounded-full"></div>
          </div>

          {/* Refined Head */}
          <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-gradient-to-br from-yellow-200 via-yellow-300 to-amber-400 rounded-full shadow-md border border-yellow-100/50">
            {/* Beautiful Eyes */}
            <div className="absolute top-0.5 left-0.5 w-1 h-1 bg-gradient-to-br from-gray-900 to-black rounded-full shadow-sm">
              <div className="absolute top-0 right-0 w-0.5 h-0.5 bg-white/90 rounded-full"></div>
              <div className="absolute bottom-0 left-0 w-0.5 h-0.5 bg-white/40 rounded-full"></div>
            </div>
            <div className="absolute top-0.5 right-0.5 w-1 h-1 bg-gradient-to-br from-gray-900 to-black rounded-full shadow-sm">
              <div className="absolute top-0 right-0 w-0.5 h-0.5 bg-white/90 rounded-full"></div>
              <div className="absolute bottom-0 left-0 w-0.5 h-0.5 bg-white/40 rounded-full"></div>
            </div>

            {/* Elegant Antennae */}
            <div className="absolute -top-0.5 left-0.5 w-0.5 h-1.5 bg-gradient-to-t from-gray-700 to-gray-600 rounded-full transform -rotate-20"></div>
            <div className="absolute -top-0.5 right-0.5 w-0.5 h-1.5 bg-gradient-to-t from-gray-700 to-gray-600 rounded-full transform rotate-20"></div>
            <div className="absolute -top-1 left-0.5 w-0.5 h-0.5 bg-gray-600 rounded-full"></div>
            <div className="absolute -top-1 right-0.5 w-0.5 h-0.5 bg-gray-600 rounded-full"></div>
          </div>

          {/* Delicate Legs */}
          <div className="absolute -bottom-0.5 left-1 w-0.5 h-1 bg-gradient-to-b from-gray-700 to-gray-800 rounded-full transform -rotate-20"></div>
          <div className="absolute -bottom-0.5 right-1 w-0.5 h-1 bg-gradient-to-b from-gray-700 to-gray-800 rounded-full transform rotate-20"></div>
        </div>

        {/* Beautiful Wings */}
        <motion.div
          className="absolute top-1 left-1/2 transform -translate-x-1/2"
          animate={{
            rotate: [0, 15, -15, 0],
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 0.12,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        >
          {/* Elegant Main Wings */}
          <div className="absolute -left-2 top-0 w-3 h-2 bg-gradient-to-br from-white/85 via-blue-50/60 to-cyan-50/40 rounded-full transform -rotate-15 shadow-md border border-white/40">
            <div className="absolute inset-0.5 bg-gradient-to-br from-white/40 to-transparent rounded-full"></div>
            {/* Wing veins */}
            <div className="absolute top-0.5 left-0.5 right-0.5 h-0.5 bg-gray-200/40 rounded-full"></div>
            <div className="absolute bottom-0.5 left-0.5 right-0.5 h-0.5 bg-gray-200/30 rounded-full"></div>
          </div>

          <div className="absolute -right-2 top-0 w-3 h-2 bg-gradient-to-bl from-white/85 via-blue-50/60 to-cyan-50/40 rounded-full transform rotate-15 shadow-md border border-white/40">
            <div className="absolute inset-0.5 bg-gradient-to-bl from-white/40 to-transparent rounded-full"></div>
            {/* Wing veins */}
            <div className="absolute top-0.5 left-0.5 right-0.5 h-0.5 bg-gray-200/40 rounded-full"></div>
            <div className="absolute bottom-0.5 left-0.5 right-0.5 h-0.5 bg-gray-200/30 rounded-full"></div>
          </div>

          {/* Smaller back wings */}
          <div className="absolute -left-1.5 top-0.5 w-2 h-1.5 bg-gradient-to-br from-white/75 via-blue-50/50 to-cyan-50/30 rounded-full transform -rotate-25 shadow-sm border border-white/30"></div>
          <div className="absolute -right-1.5 top-0.5 w-2 h-1.5 bg-gradient-to-bl from-white/75 via-blue-50/50 to-cyan-50/30 rounded-full transform rotate-25 shadow-sm border border-white/30"></div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default BeeParticleSystem;
