import React from 'react';
import { motion } from 'framer-motion';

// Simple scroll animation variants
export const scrollFadeIn = {
  hidden: { 
    opacity: 0, 
    y: 50 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { 
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export const scrollSlideLeft = {
  hidden: { 
    opacity: 0, 
    x: -50 
  },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { 
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export const scrollSlideRight = {
  hidden: { 
    opacity: 0, 
    x: 50 
  },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { 
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export const scrollZoom = {
  hidden: { 
    opacity: 0, 
    scale: 0.8 
  },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: { 
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export const scrollStagger = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

export const scrollStaggerItem = {
  hidden: { 
    opacity: 0, 
    y: 30 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { 
      duration: 0.5,
      ease: "easeOut"
    }
  }
};

// Simple viewport options
export const viewportOptions = {
  once: false,
  margin: '-100px',
  amount: 0.3
};

// Simple scroll animation component
export const ScrollAnimation: React.FC<{
  children: React.ReactNode;
  animation?: 'fadeIn' | 'slideLeft' | 'slideRight' | 'zoom';
  delay?: number;
  className?: string;
}> = ({ 
  children, 
  animation = 'fadeIn', 
  delay = 0,
  className = '' 
}) => {
  const getVariants = () => {
    switch (animation) {
      case 'slideLeft':
        return scrollSlideLeft;
      case 'slideRight':
        return scrollSlideRight;
      case 'zoom':
        return scrollZoom;
      default:
        return scrollFadeIn;
    }
  };

  const variants = getVariants();
  const delayedVariants = {
    hidden: variants.hidden,
    visible: {
      ...variants.visible,
      transition: {
        ...variants.visible.transition,
        delay: delay
      }
    }
  };

  return (
    <motion.div
      className={className}
      variants={delayedVariants}
      initial="hidden"
      whileInView="visible"
      viewport={viewportOptions}
    >
      {children}
    </motion.div>
  );
};

// Simple stagger animation component
export const ScrollStagger: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => {
  return (
    <motion.div
      className={className}
      variants={scrollStagger}
      initial="hidden"
      whileInView="visible"
      viewport={viewportOptions}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div
          key={index}
          variants={scrollStaggerItem}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};

// Simple floating animation
export const ScrollFloat: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => {
  return (
    <motion.div
      className={className}
      animate={{
        y: [0, -10, 0],
      }}
      transition={{
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    >
      {children}
    </motion.div>
  );
};

// Simple hover scale animation
export const ScrollHover: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = '' }) => {
  return (
    <motion.div
      className={className}
      whileHover={{ 
        scale: 1.05,
        transition: { duration: 0.2 }
      }}
      whileTap={{ scale: 0.95 }}
    >
      {children}
    </motion.div>
  );
};
