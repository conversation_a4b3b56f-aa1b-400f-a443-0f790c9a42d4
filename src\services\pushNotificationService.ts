// Push Notification Service for Nural Bhardwaj Portfolio
// Handles all push notification functionality and engagement

export interface NotificationData {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  url?: string;
  actions?: NotificationAction[];
  requireInteraction?: boolean;
  silent?: boolean;
  vibrate?: number[];
  timestamp?: number;
}

export interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

export interface EngagementMetrics {
  lastVisit: number;
  visitCount: number;
  notificationsSent: number;
  notificationsClicked: number;
  engagementScore: number;
}

class PushNotificationService {
  private swRegistration: ServiceWorkerRegistration | null = null;
  private vapidPublicKey = 'BEl62iUYgUivxIkv69yViEuiBIa40HI80NM9f4LUjqLDkFXaVUAN0QjQjhqHMFfkFKRXCu3iLAi8bEZQUTiS8N8';
  
  constructor() {
    this.initializeService();
  }

  private async initializeService() {
    if ('serviceWorker' in navigator) {
      try {
        this.swRegistration = await navigator.serviceWorker.ready;
        console.log('Push Notification Service initialized');
      } catch (error) {
        console.error('Failed to initialize Push Notification Service:', error);
      }
    }
  }

  // Request notification permission
  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      throw new Error('Notifications not supported');
    }

    const permission = await Notification.requestPermission();
    
    if (permission === 'granted') {
      await this.subscribeToPush();
      this.trackEngagement('permission_granted');
    }
    
    return permission;
  }

  // Subscribe to push notifications
  async subscribeToPush(): Promise<PushSubscription | null> {
    if (!this.swRegistration) {
      console.error('Service Worker not registered');
      return null;
    }

    try {
      const subscription = await this.swRegistration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey)
      });

      // Store subscription
      localStorage.setItem('push-subscription', JSON.stringify(subscription));
      console.log('Push subscription successful:', subscription);
      
      return subscription;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      return null;
    }
  }

  // Send local notification
  async sendNotification(data: NotificationData): Promise<void> {
    if (Notification.permission !== 'granted') {
      console.warn('Notification permission not granted');
      return;
    }

    const options: NotificationOptions = {
      body: data.body,
      icon: data.icon || '/icons/icon-192x192.png',
      badge: data.badge || '/icons/icon-72x72.png',
      tag: data.tag || 'portfolio-notification',
      requireInteraction: data.requireInteraction || false,
      silent: data.silent || false,
      vibrate: data.vibrate || [200, 100, 200],
      data: {
        url: data.url || '/',
        timestamp: Date.now(),
        source: 'portfolio-pwa'
      },
      actions: data.actions || [
        { action: 'view', title: 'View Portfolio' },
        { action: 'dismiss', title: 'Dismiss' }
      ]
    };

    try {
      const notification = new Notification(data.title, options);
      
      notification.onclick = () => {
        this.handleNotificationClick(data.url || '/');
        this.trackEngagement('notification_clicked');
      };

      this.trackEngagement('notification_sent');
      this.updateEngagementMetrics('notificationsSent');
      
    } catch (error) {
      console.error('Failed to send notification:', error);
    }
  }

  // Handle notification click
  private handleNotificationClick(url: string) {
    if (window.focus) {
      window.focus();
    }
    
    if (url && url !== '/') {
      window.location.hash = url.replace('/', '');
    }
  }

  // Send welcome notification
  async sendWelcomeNotification(): Promise<void> {
    await this.sendNotification({
      title: 'Welcome to Nural\'s Portfolio! 🎉',
      body: 'Thanks for installing the app! You\'ll receive updates about new projects and blog posts.',
      tag: 'welcome-notification',
      url: '/',
      actions: [
        { action: 'explore', title: 'Explore Portfolio' },
        { action: 'projects', title: 'View Projects' }
      ]
    });
  }

  // Send project update notification
  async sendProjectUpdateNotification(projectName: string): Promise<void> {
    await this.sendNotification({
      title: 'New Project Added! 🚀',
      body: `Check out my latest project: ${projectName}`,
      tag: 'project-update',
      url: '/#projects',
      actions: [
        { action: 'view', title: 'View Project' },
        { action: 'share', title: 'Share' }
      ]
    });
  }

  // Send blog post notification
  async sendBlogPostNotification(postTitle: string): Promise<void> {
    await this.sendNotification({
      title: 'New Blog Post! 📝',
      body: `New article: ${postTitle}`,
      tag: 'blog-update',
      url: '/#blog',
      actions: [
        { action: 'read', title: 'Read Article' },
        { action: 'share', title: 'Share' }
      ]
    });
  }

  // Send engagement notification
  async sendEngagementNotification(): Promise<void> {
    const metrics = this.getEngagementMetrics();
    const daysSinceLastVisit = (Date.now() - metrics.lastVisit) / (1000 * 60 * 60 * 24);
    
    if (daysSinceLastVisit >= 3) {
      await this.sendNotification({
        title: 'Miss me? 😊',
        body: 'I\'ve added new projects and updates to my portfolio. Come check them out!',
        tag: 'engagement-notification',
        url: '/#projects',
        requireInteraction: true,
        actions: [
          { action: 'view', title: 'View Updates' },
          { action: 'later', title: 'Maybe Later' }
        ]
      });
    }
  }

  // Send skill update notification
  async sendSkillUpdateNotification(skill: string): Promise<void> {
    await this.sendNotification({
      title: 'New Skill Added! 🎯',
      body: `I've mastered a new technology: ${skill}`,
      tag: 'skill-update',
      url: '/#about',
      actions: [
        { action: 'view', title: 'View Skills' },
        { action: 'contact', title: 'Hire Me' }
      ]
    });
  }

  // Send contact form response notification
  async sendContactResponseNotification(): Promise<void> {
    await this.sendNotification({
      title: 'Message Received! 📧',
      body: 'Thanks for reaching out! I\'ll get back to you within 24 hours.',
      tag: 'contact-response',
      url: '/#contact',
      actions: [
        { action: 'view', title: 'View Portfolio' },
        { action: 'linkedin', title: 'Connect on LinkedIn' }
      ]
    });
  }

  // Schedule periodic notifications
  schedulePeriodicNotifications(): void {
    // Schedule engagement notification every 3 days
    setInterval(() => {
      this.sendEngagementNotification();
    }, 3 * 24 * 60 * 60 * 1000); // 3 days

    // Schedule weekly portfolio reminder
    setInterval(() => {
      this.sendNotification({
        title: 'Weekly Portfolio Update 📊',
        body: 'Check out what\'s new in my portfolio this week!',
        tag: 'weekly-update',
        url: '/',
        actions: [
          { action: 'view', title: 'View Updates' },
          { action: 'unsubscribe', title: 'Unsubscribe' }
        ]
      });
    }, 7 * 24 * 60 * 60 * 1000); // 7 days
  }

  // Track engagement metrics
  private trackEngagement(event: string): void {
    const analytics = {
      event: 'notification_engagement',
      parameters: {
        event_category: 'PWA',
        event_label: event,
        timestamp: Date.now()
      }
    };

    // Send to Google Analytics if available
    if (typeof gtag !== 'undefined') {
      gtag('event', analytics.event, analytics.parameters);
    }

    // Store locally for offline sync
    const pendingAnalytics = JSON.parse(localStorage.getItem('pending-analytics') || '[]');
    pendingAnalytics.push({ ...analytics, id: Date.now() });
    localStorage.setItem('pending-analytics', JSON.stringify(pendingAnalytics));
  }

  // Get engagement metrics
  getEngagementMetrics(): EngagementMetrics {
    const stored = localStorage.getItem('engagement-metrics');
    const defaultMetrics: EngagementMetrics = {
      lastVisit: Date.now(),
      visitCount: 1,
      notificationsSent: 0,
      notificationsClicked: 0,
      engagementScore: 0
    };

    return stored ? JSON.parse(stored) : defaultMetrics;
  }

  // Update engagement metrics
  updateEngagementMetrics(metric: keyof EngagementMetrics, value?: number): void {
    const metrics = this.getEngagementMetrics();
    
    if (value !== undefined) {
      (metrics as any)[metric] = value;
    } else {
      (metrics as any)[metric]++;
    }

    // Calculate engagement score
    metrics.engagementScore = this.calculateEngagementScore(metrics);
    
    localStorage.setItem('engagement-metrics', JSON.stringify(metrics));
  }

  // Calculate engagement score
  private calculateEngagementScore(metrics: EngagementMetrics): number {
    const clickRate = metrics.notificationsSent > 0 ? metrics.notificationsClicked / metrics.notificationsSent : 0;
    const visitFrequency = metrics.visitCount / Math.max(1, (Date.now() - metrics.lastVisit) / (1000 * 60 * 60 * 24));
    
    return Math.round((clickRate * 50) + (visitFrequency * 50));
  }

  // Utility function to convert VAPID key
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  // Unsubscribe from notifications
  async unsubscribe(): Promise<void> {
    if (!this.swRegistration) return;

    try {
      const subscription = await this.swRegistration.pushManager.getSubscription();
      if (subscription) {
        await subscription.unsubscribe();
        localStorage.removeItem('push-subscription');
        console.log('Unsubscribed from push notifications');
      }
    } catch (error) {
      console.error('Failed to unsubscribe:', error);
    }
  }

  // Check if notifications are supported
  isSupported(): boolean {
    return 'Notification' in window && 'serviceWorker' in navigator && 'PushManager' in window;
  }

  // Get current permission status
  getPermissionStatus(): NotificationPermission {
    return 'Notification' in window ? Notification.permission : 'denied';
  }
}

// Export singleton instance
export const pushNotificationService = new PushNotificationService();
