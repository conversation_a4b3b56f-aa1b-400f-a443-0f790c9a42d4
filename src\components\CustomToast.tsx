import React from 'react';
import { motion } from 'framer-motion';
import { X, Copy, Mail, ExternalLink, Check, AlertCircle, Info } from 'lucide-react';
import toast from 'react-hot-toast';

interface EmailToastProps {
  t: { id: string };
  onCopy: () => void;
  onGmail: () => void;
}

export const EmailOptionsToast: React.FC<EmailToastProps> = ({ t, onCopy, onGmail }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, y: 50 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.8, y: 50 }}
      className="relative overflow-hidden"
    >
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-black to-cyan-900/20" />
      
      {/* Animated Border */}
      <motion.div
        className="absolute inset-0 rounded-2xl"
        style={{
          background: 'linear-gradient(45deg, #8b5cf6, #06b6d4, #8b5cf6)',
          backgroundSize: '200% 200%',
        }}
        animate={{
          backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: 'linear',
        }}
      />
      
      {/* Content Container */}
      <div className="relative bg-black/90 backdrop-blur-xl m-[1px] rounded-2xl p-6 border border-white/10">
        {/* Close Button */}
        <button
          onClick={() => toast.dismiss(t.id)}
          className="absolute top-3 right-3 text-gray-400 hover:text-white transition-colors p-1 rounded-full hover:bg-white/10"
        >
          <X className="w-4 h-4" />
        </button>

        {/* Header */}
        <div className="text-center mb-4">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-full mb-3"
          >
            <Mail className="w-6 h-6 text-white" />
          </motion.div>
          
          <motion.h3
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-lg font-bold text-white mb-1"
          >
            Contact Me
          </motion.h3>
          
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-sm text-gray-300 font-mono"
          >
            <EMAIL>
          </motion.p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <motion.button
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
            onClick={() => {
              toast.dismiss(t.id);
              onCopy();
            }}
            className="group w-full flex items-center space-x-3 p-4 bg-gradient-to-r from-purple-600/20 to-purple-500/20 hover:from-purple-600/40 hover:to-purple-500/40 border border-purple-500/30 hover:border-purple-400/50 rounded-xl transition-all duration-300 transform hover:scale-[1.02]"
          >
            <div className="flex items-center justify-center w-10 h-10 bg-purple-500/20 rounded-lg group-hover:bg-purple-500/30 transition-colors">
              <Copy className="w-5 h-5 text-purple-400" />
            </div>
            <div className="flex-1 text-left">
              <div className="text-white font-medium">Copy to Clipboard</div>
              <div className="text-gray-400 text-sm">Copy email address</div>
            </div>
            <motion.div
              className="text-purple-400"
              whileHover={{ x: 5 }}
              transition={{ type: "spring", stiffness: 400 }}
            >
              →
            </motion.div>
          </motion.button>

          <motion.button
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6 }}
            onClick={() => {
              toast.dismiss(t.id);
              onGmail();
            }}
            className="group w-full flex items-center space-x-3 p-4 bg-gradient-to-r from-blue-600/20 to-cyan-500/20 hover:from-blue-600/40 hover:to-cyan-500/40 border border-blue-500/30 hover:border-blue-400/50 rounded-xl transition-all duration-300 transform hover:scale-[1.02]"
          >
            <div className="flex items-center justify-center w-10 h-10 bg-blue-500/20 rounded-lg group-hover:bg-blue-500/30 transition-colors">
              <ExternalLink className="w-5 h-5 text-blue-400" />
            </div>
            <div className="flex-1 text-left">
              <div className="text-white font-medium">Open Gmail</div>
              <div className="text-gray-400 text-sm">Compose new email</div>
            </div>
            <motion.div
              className="text-blue-400"
              whileHover={{ x: 5 }}
              transition={{ type: "spring", stiffness: 400 }}
            >
              →
            </motion.div>
          </motion.button>
        </div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.7 }}
          className="mt-4 pt-4 border-t border-white/10 text-center"
        >
          <button
            onClick={() => toast.dismiss(t.id)}
            className="text-gray-500 hover:text-gray-300 text-sm transition-colors"
          >
            Cancel
          </button>
        </motion.div>
      </div>
    </motion.div>
  );
};

interface SuccessToastProps {
  message: string;
  icon?: React.ReactNode;
}

export const SuccessToast: React.FC<SuccessToastProps> = ({ message, icon }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, x: 100 }}
      animate={{ opacity: 1, scale: 1, x: 0 }}
      exit={{ opacity: 0, scale: 0.8, x: 100 }}
      className="flex items-center space-x-3 p-4 bg-gradient-to-r from-green-900/40 to-emerald-900/40 backdrop-blur-xl border border-green-500/30 rounded-xl shadow-2xl"
    >
      <motion.div
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
        className="flex items-center justify-center w-8 h-8 bg-green-500/20 rounded-full"
      >
        {icon || <Check className="w-5 h-5 text-green-400" />}
      </motion.div>
      <div className="text-white font-medium">{message}</div>
    </motion.div>
  );
};

interface ErrorToastProps {
  message: string;
  icon?: React.ReactNode;
}

export const ErrorToast: React.FC<ErrorToastProps> = ({ message, icon }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, x: 100 }}
      animate={{ opacity: 1, scale: 1, x: 0 }}
      exit={{ opacity: 0, scale: 0.8, x: 100 }}
      className="flex items-center space-x-3 p-4 bg-gradient-to-r from-red-900/40 to-pink-900/40 backdrop-blur-xl border border-red-500/30 rounded-xl shadow-2xl max-w-md"
    >
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: [0, 1.2, 1] }}
        transition={{ delay: 0.2, duration: 0.5 }}
        className="flex items-center justify-center w-8 h-8 bg-red-500/20 rounded-full"
      >
        {icon || <AlertCircle className="w-5 h-5 text-red-400" />}
      </motion.div>
      <div className="text-white font-medium text-sm">{message}</div>
    </motion.div>
  );
};

interface InfoToastProps {
  message: string;
  icon?: React.ReactNode;
}

export const InfoToast: React.FC<InfoToastProps> = ({ message, icon }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, x: 100 }}
      animate={{ opacity: 1, scale: 1, x: 0 }}
      exit={{ opacity: 0, scale: 0.8, x: 100 }}
      className="flex items-center space-x-3 p-4 bg-gradient-to-r from-blue-900/40 to-cyan-900/40 backdrop-blur-xl border border-blue-500/30 rounded-xl shadow-2xl"
    >
      <motion.div
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
        className="flex items-center justify-center w-8 h-8 bg-blue-500/20 rounded-full"
      >
        {icon || <Info className="w-5 h-5 text-blue-400" />}
      </motion.div>
      <div className="text-white font-medium">{message}</div>
    </motion.div>
  );
};
