// Real-time data synchronization utility
export class DataSyncManager {
  private static instance: DataSyncManager;
  private listeners: Set<() => void> = new Set();
  private syncInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.startSync();
  }

  public static getInstance(): DataSyncManager {
    if (!DataSyncManager.instance) {
      DataSyncManager.instance = new DataSyncManager();
    }
    return DataSyncManager.instance;
  }

  public subscribe(callback: () => void): () => void {
    this.listeners.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(callback);
    };
  }

  public notify(): void {
    this.listeners.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in data sync callback:', error);
      }
    });
  }

  private startSync(): void {
    // Check for data changes every 5 seconds
    this.syncInterval = setInterval(() => {
      this.checkForUpdates();
    }, 5000);
  }

  private checkForUpdates(): void {
    // Check if localStorage data has been modified
    const lastModified = localStorage.getItem('portfolio_last_modified');
    const currentTime = Date.now().toString();
    
    if (lastModified && lastModified !== currentTime) {
      this.notify();
    }
  }

  public markAsUpdated(): void {
    localStorage.setItem('portfolio_last_modified', Date.now().toString());
    this.notify();
  }

  public destroy(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    this.listeners.clear();
  }
}

// React hook for data synchronization
import { useEffect, useState } from 'react';

export function useDataSync() {
  const [syncKey, setSyncKey] = useState(0);

  useEffect(() => {
    const syncManager = DataSyncManager.getInstance();
    
    const unsubscribe = syncManager.subscribe(() => {
      setSyncKey(prev => prev + 1);
    });

    return unsubscribe;
  }, []);

  const triggerSync = () => {
    DataSyncManager.getInstance().markAsUpdated();
  };

  return { syncKey, triggerSync };
}

// Auto-save functionality
export class AutoSaveManager {
  private static instance: AutoSaveManager;
  private saveQueue: Map<string, any> = new Map();
  private saveTimeout: NodeJS.Timeout | null = null;
  private readonly SAVE_DELAY = 2000; // 2 seconds

  private constructor() {}

  public static getInstance(): AutoSaveManager {
    if (!AutoSaveManager.instance) {
      AutoSaveManager.instance = new AutoSaveManager();
    }
    return AutoSaveManager.instance;
  }

  public queueSave(key: string, data: any, saveFunction: (data: any) => void): void {
    this.saveQueue.set(key, { data, saveFunction });
    
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }

    this.saveTimeout = setTimeout(() => {
      this.processSaveQueue();
    }, this.SAVE_DELAY);
  }

  private processSaveQueue(): void {
    this.saveQueue.forEach(({ data, saveFunction }, key) => {
      try {
        saveFunction(data);
        console.log(`Auto-saved: ${key}`);
      } catch (error) {
        console.error(`Auto-save failed for ${key}:`, error);
      }
    });

    this.saveQueue.clear();
    DataSyncManager.getInstance().markAsUpdated();
  }

  public forceSave(): void {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
    }
    this.processSaveQueue();
  }
}

// Performance monitoring
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();

  private constructor() {}

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  public startTiming(operation: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (!this.metrics.has(operation)) {
        this.metrics.set(operation, []);
      }
      
      const operationMetrics = this.metrics.get(operation)!;
      operationMetrics.push(duration);
      
      // Keep only last 100 measurements
      if (operationMetrics.length > 100) {
        operationMetrics.shift();
      }
      
      console.log(`${operation}: ${duration.toFixed(2)}ms`);
    };
  }

  public getAverageTime(operation: string): number {
    const metrics = this.metrics.get(operation);
    if (!metrics || metrics.length === 0) return 0;
    
    const sum = metrics.reduce((acc, time) => acc + time, 0);
    return sum / metrics.length;
  }

  public getAllMetrics(): Record<string, { average: number; count: number; latest: number }> {
    const result: Record<string, { average: number; count: number; latest: number }> = {};
    
    this.metrics.forEach((times, operation) => {
      if (times.length > 0) {
        const average = times.reduce((acc, time) => acc + time, 0) / times.length;
        result[operation] = {
          average: Math.round(average * 100) / 100,
          count: times.length,
          latest: Math.round(times[times.length - 1] * 100) / 100
        };
      }
    });
    
    return result;
  }
}

// Data validation utilities
export class DataValidator {
  public static validateProject(project: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!project.title || typeof project.title !== 'string') {
      errors.push('Title is required and must be a string');
    }
    
    if (!project.description || typeof project.description !== 'string') {
      errors.push('Description is required and must be a string');
    }
    
    if (!project.category || typeof project.category !== 'string') {
      errors.push('Category is required and must be a string');
    }
    
    if (!Array.isArray(project.technologies)) {
      errors.push('Technologies must be an array');
    }
    
    if (project.github && typeof project.github !== 'string') {
      errors.push('GitHub URL must be a string');
    }
    
    if (project.live && typeof project.live !== 'string') {
      errors.push('Live URL must be a string');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  public static validateBlogPost(post: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!post.title || typeof post.title !== 'string') {
      errors.push('Title is required and must be a string');
    }
    
    if (!post.excerpt || typeof post.excerpt !== 'string') {
      errors.push('Excerpt is required and must be a string');
    }
    
    if (!post.content || typeof post.content !== 'string') {
      errors.push('Content is required and must be a string');
    }
    
    if (!post.category || typeof post.category !== 'string') {
      errors.push('Category is required and must be a string');
    }
    
    if (!Array.isArray(post.tags)) {
      errors.push('Tags must be an array');
    }
    
    if (typeof post.published !== 'boolean') {
      errors.push('Published status must be a boolean');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  public static validatePersonalInfo(info: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!info.name || typeof info.name !== 'string') {
      errors.push('Name is required and must be a string');
    }
    
    if (!info.title || typeof info.title !== 'string') {
      errors.push('Professional title is required and must be a string');
    }
    
    if (!info.email || typeof info.email !== 'string') {
      errors.push('Email is required and must be a string');
    }
    
    if (info.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(info.email)) {
      errors.push('Email must be a valid email address');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Export all utilities
export default {
  DataSyncManager,
  AutoSaveManager,
  PerformanceMonitor,
  DataValidator,
  useDataSync
};
