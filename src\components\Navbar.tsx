import { BookOpen, Briefcase, Code, FileText, Home, Mail, Menu, User, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import LanguageSelector from './LanguageSelector';

const Navbar = () => {
  const { t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [activeSection, setActiveSection] = useState('home');

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
      
      // Update active section based on scroll position
      const sections = ['home', 'about', 'skills', 'projects', 'blog', 'resume', 'contact'];
      const current = sections.find(section => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top <= 100 && rect.bottom >= 100;
        }
        return false;
      });
      if (current) setActiveSection(current);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { id: 'home', label: t('nav.home'), icon: Home },
    { id: 'about', label: t('nav.about'), icon: User },
    { id: 'skills', label: t('nav.skills'), icon: Code },
    { id: 'projects', label: t('nav.projects'), icon: Briefcase },
    { id: 'blog', label: t('nav.blog'), icon: BookOpen },
    { id: 'resume', label: t('nav.resume'), icon: FileText },
    { id: 'contact', label: t('nav.contact'), icon: Mail }
  ];

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    element?.scrollIntoView({ behavior: 'smooth' });
    setIsOpen(false);
  };

  return (
    <>
      {/* Main Navbar - Fixed z-index and mobile spacing */}
      <nav className={`navbar fixed top-0 w-full z-50 transition-all duration-700 ${
        scrolled
          ? 'bg-black/10 backdrop-blur-2xl border-b border-white/5 shadow-2xl shadow-purple-500/10'
          : 'bg-transparent'
      }`}>
        <div className="container mx-auto px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            {/* Logo with 3D effect - Fixed mobile positioning */}
            <div className="relative group ml-0 sm:ml-0">
              <div className="absolute -inset-2 bg-gradient-to-r from-purple-600 via-pink-600 to-cyan-600 rounded-xl blur-lg opacity-30 group-hover:opacity-60 transition-opacity duration-500"></div>
              <div className="relative text-lg sm:text-xl md:text-2xl font-black bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent transform group-hover:scale-110 transition-transform duration-300">
                <span className="hidden sm:inline">Nuralbhardwaj.me</span>
                <span className="sm:hidden">NB</span>
              </div>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-2">
              {navItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className={`group relative px-6 py-3 rounded-2xl font-medium transition-all duration-500 transform hover:scale-105 ${
                    activeSection === item.id
                      ? 'text-white bg-gradient-to-r from-purple-600/20 to-cyan-600/20 border border-purple-500/30'
                      : 'text-gray-300 hover:text-white hover:bg-white/5'
                  }`}
                >
                  <div className="flex items-center navbar-item" style={{ wordSpacing: 'normal !important', letterSpacing: 'normal !important', gap: '0.5rem' }}>
                    <item.icon className="w-4 h-4" />
                    <span className="navbar-item" style={{ wordSpacing: 'normal !important', letterSpacing: 'normal !important', whiteSpace: 'nowrap' }}>{item.label}</span>
                  </div>
                  {activeSection === item.id && (
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-cyan-600/10 rounded-2xl animate-pulse"></div>
                  )}
                </button>
              ))}

              {/* Language Selector */}
              <div className="ml-4">
                <LanguageSelector />
              </div>
            </div>

            {/* Mobile Menu Button */}
            <button
              className="lg:hidden relative p-3 text-white group"
              onClick={() => setIsOpen(!isOpen)}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 rounded-xl blur-sm group-hover:blur-md transition-all duration-300"></div>
              <div className="relative">
                {isOpen ? <X size={24} /> : <Menu size={24} />}
              </div>
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation Overlay */}
      <div className={`lg:hidden fixed inset-0 z-40 transition-all duration-500 ${
        isOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
      }`}>
        <div className="absolute inset-0 bg-black/80 backdrop-blur-2xl" onClick={() => setIsOpen(false)}></div>
        <div className={`absolute right-0 top-0 h-full w-80 bg-gradient-to-br from-gray-900/95 via-purple-900/20 to-gray-900/95 backdrop-blur-2xl border-l border-white/10 transform transition-transform duration-500 ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}>
          <div className="p-8 pt-24">
            <div className="space-y-4">
              {navItems.map((item, index) => (
                <button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className={`group relative w-full p-4 rounded-2xl font-medium transition-all duration-500 transform hover:scale-105 ${
                    activeSection === item.id
                      ? 'text-white bg-gradient-to-r from-purple-600/20 to-cyan-600/20 border border-purple-500/30'
                      : 'text-gray-300 hover:text-white hover:bg-white/5'
                  }`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="flex items-center navbar-item" style={{ wordSpacing: 'normal !important', letterSpacing: 'normal !important', gap: '0.75rem' }}>
                    <div className="p-2 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 rounded-lg">
                      <item.icon className="w-5 h-5" />
                    </div>
                    <span className="text-lg navbar-item" style={{ wordSpacing: 'normal !important', letterSpacing: 'normal !important', whiteSpace: 'nowrap' }}>{item.label}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Floating Navigation Indicator */}
      <div className="fixed right-8 top-1/2 transform -translate-y-1/2 z-40 hidden xl:block">
        <div className="space-y-3">
          {navItems.map((item) => (
            <button
              key={item.id}
              onClick={() => scrollToSection(item.id)}
              className={`group relative block w-3 h-3 rounded-full transition-all duration-500 ${
                activeSection === item.id
                  ? 'bg-gradient-to-r from-purple-500 to-cyan-500 scale-150'
                  : 'bg-white/20 hover:bg-white/40 hover:scale-125'
              }`}
            >
              <div className="absolute right-6 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="bg-black/80 text-white px-3 py-1 rounded-lg text-sm whitespace-nowrap">
                  {item.label}
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>
    </>
  );
};

export default Navbar;