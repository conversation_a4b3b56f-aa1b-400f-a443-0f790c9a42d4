<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            line-height: 1.6;
            color: #00ff41;
            max-width: 650px;
            margin: 0 auto;
            padding: 20px;
            background: #000000;
            background-image: 
                radial-gradient(circle at 25% 25%, #1a0033 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #001a33 0%, transparent 50%),
                linear-gradient(135deg, #000000 0%, #0a0a0a 100%);
            min-height: 100vh;
        }
        
        .matrix-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            z-index: -1;
            background-image: 
                repeating-linear-gradient(0deg, transparent, transparent 2px, #00ff41 2px, #00ff41 4px),
                repeating-linear-gradient(90deg, transparent, transparent 2px, #00ff41 2px, #00ff41 4px);
            background-size: 20px 20px;
        }
        
        .container {
            background: rgba(0, 0, 0, 0.95);
            border-radius: 0;
            padding: 0;
            border: 2px solid #00ff41;
            box-shadow: 
                0 0 20px #00ff41,
                inset 0 0 20px rgba(0, 255, 65, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00ff41, transparent);
            animation: scan 3s infinite;
        }
        
        @keyframes scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .header {
            background: linear-gradient(135deg, #001100 0%, #003300 50%, #001100 100%);
            color: #00ff41;
            padding: 30px;
            text-align: center;
            border-bottom: 2px solid #00ff41;
            position: relative;
        }
        
        .header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: #00ff41;
            box-shadow: 0 0 10px #00ff41;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 0 0 10px #00ff41;
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { text-shadow: 0 0 10px #00ff41; }
            to { text-shadow: 0 0 20px #00ff41, 0 0 30px #00ff41; }
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 12px;
            opacity: 0.8;
            letter-spacing: 1px;
        }
        
        .terminal-line {
            font-family: 'Fira Code', 'Consolas', monospace;
            color: #00ff41;
            margin: 5px 0;
            padding: 5px 0;
        }
        
        .section {
            margin: 0;
            padding: 25px;
            background: rgba(0, 20, 0, 0.3);
            border-bottom: 1px solid #003300;
        }
        
        .section h3 {
            margin: 0 0 20px 0;
            color: #00ff41;
            font-size: 16px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-bottom: 1px solid #003300;
            padding-bottom: 10px;
        }
        
        .terminal-output {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #003300;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Fira Code', 'Consolas', monospace;
            font-size: 14px;
        }
        
        .data-field {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px dotted #003300;
        }
        
        .data-field:last-child {
            border-bottom: none;
        }
        
        .field-label {
            color: #00aa00;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.5px;
        }
        
        .field-value {
            color: #00ff41;
            font-weight: 400;
            text-align: right;
            max-width: 60%;
            word-break: break-word;
        }
        
        .message-terminal {
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #003300;
            padding: 20px;
            margin: 15px 0;
            font-family: 'JetBrains Mono', monospace;
            color: #00ff41;
            white-space: pre-wrap;
            line-height: 1.6;
            box-shadow: inset 0 0 10px rgba(0, 255, 65, 0.1);
        }
        
        .status-bar {
            background: rgba(0, 50, 0, 0.8);
            padding: 15px;
            border-top: 2px solid #00ff41;
            text-align: center;
            font-size: 11px;
            color: #00aa00;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #00ff41;
            border-radius: 50%;
            margin: 0 5px;
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        
        .command-prompt {
            color: #00aa00;
            font-weight: 600;
        }
        
        .highlight {
            color: #00ff41;
            font-weight: 700;
            text-shadow: 0 0 5px #00ff41;
        }
        
        .error-text {
            color: #ff0040;
            text-shadow: 0 0 5px #ff0040;
        }
        
        .warning-text {
            color: #ffaa00;
            text-shadow: 0 0 5px #ffaa00;
        }
        
        .success-text {
            color: #00ff41;
            text-shadow: 0 0 5px #00ff41;
        }
    </style>
</head>
<body>
    <div class="matrix-bg"></div>
    
    <div class="container">
        <div class="header">
            <h1>⚡ INCOMING TRANSMISSION ⚡</h1>
            <p>[ SECURE CHANNEL ESTABLISHED ]</p>
        </div>

        <div class="section">
            <h3>🔐 CLIENT AUTHENTICATION</h3>
            <div class="terminal-output">
                <div class="terminal-line"><span class="command-prompt">root@portfolio:~$</span> decrypt_client_data.sh</div>
                <div class="terminal-line">Initializing secure handshake...</div>
                <div class="terminal-line"><span class="success-text">[SUCCESS]</span> Authentication complete</div>
                <br>
                <div class="data-field">
                    <span class="field-label">IDENTITY:</span>
                    <span class="field-value highlight">{{from_name}}</span>
                </div>
                <div class="data-field">
                    <span class="field-label">CONTACT_VECTOR:</span>
                    <span class="field-value highlight">{{from_email}}</span>
                </div>
                <div class="data-field">
                    <span class="field-label">REPLY_CHANNEL:</span>
                    <span class="field-value highlight">{{reply_to}}</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>📊 PROJECT PARAMETERS</h3>
            <div class="terminal-output">
                <div class="terminal-line"><span class="command-prompt">root@portfolio:~$</span> analyze_project_specs.py</div>
                <div class="terminal-line">Parsing project requirements...</div>
                <div class="terminal-line"><span class="success-text">[SUCCESS]</span> Analysis complete</div>
                <br>
                <div class="data-field">
                    <span class="field-label">MISSION_TITLE:</span>
                    <span class="field-value highlight">{{subject}}</span>
                </div>
                <div class="data-field">
                    <span class="field-label">BUDGET_RANGE:</span>
                    <span class="field-value success-text">{{budget}}</span>
                </div>
                <div class="data-field">
                    <span class="field-label">TIME_FRAME:</span>
                    <span class="field-value warning-text">{{timeline}}</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>💾 MESSAGE PAYLOAD</h3>
            <div class="terminal-output">
                <div class="terminal-line"><span class="command-prompt">root@portfolio:~$</span> extract_message.sh</div>
                <div class="terminal-line">Decrypting message content...</div>
                <div class="terminal-line"><span class="success-text">[SUCCESS]</span> Message extracted</div>
            </div>
            <div class="message-terminal">{{message}}</div>
        </div>

        <div class="status-bar">
            <div class="terminal-line">
                <span class="status-indicator"></span>
                <span class="success-text">TRANSMISSION RECEIVED</span>
                <span class="status-indicator"></span>
            </div>
            <div class="terminal-line">
                SOURCE: <span class="highlight">NURAL.BHARDWAJ.PORTFOLIO.SYS</span> | 
                TIMESTAMP: <span class="highlight">{{sent_date}} {{sent_time}}</span>
            </div>
            <div class="terminal-line">
                STATUS: <span class="success-text">SECURE</span> | 
                ENCRYPTION: <span class="success-text">AES-256</span> | 
                PRIORITY: <span class="warning-text">HIGH</span>
            </div>
            <br>
            <div class="terminal-line">
                <span class="command-prompt">NEXT_ACTION:</span> 
                <span class="highlight">REVIEW → RESPOND → EXECUTE [24H WINDOW]</span>
            </div>
        </div>
    </div>
</body>
</html>
