import React from 'react';
import { motion } from 'framer-motion';
import { 
  Github, 
  Key, 
  Settings, 
  Copy, 
  ExternalLink, 
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';

interface GitHubTokenGuideProps {
  onClose: () => void;
}

const GitHubTokenGuide: React.FC<GitHubTokenGuideProps> = ({ onClose }) => {
  const steps = [
    {
      title: "Go to GitHub Settings",
      description: "Navigate to your GitHub account settings",
      action: "Click on your profile picture → Settings",
      icon: Settings,
      color: "from-blue-500 to-blue-600"
    },
    {
      title: "Developer Settings",
      description: "Access the developer settings section",
      action: "Scroll down and click 'Developer settings' in the left sidebar",
      icon: Github,
      color: "from-purple-500 to-purple-600"
    },
    {
      title: "Personal Access Tokens",
      description: "Create a new personal access token",
      action: "Click 'Personal access tokens' → 'Tokens (classic)' → 'Generate new token'",
      icon: Key,
      color: "from-green-500 to-green-600"
    },
    {
      title: "Configure Token",
      description: "Set up the token with required permissions",
      action: "Give it a name, set expiration, and select 'repo' scope",
      icon: CheckCircle,
      color: "from-orange-500 to-orange-600"
    }
  ];

  const requiredScopes = [
    { name: "repo", description: "Full control of private repositories" },
    { name: "public_repo", description: "Access public repositories" },
    { name: "repo:status", description: "Access commit status" },
    { name: "repo_deployment", description: "Access deployment status" }
  ];

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-gray-900 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-gray-700"
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-lg">
                <Github className="w-6 h-6 text-purple-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">GitHub Token Setup Guide</h2>
                <p className="text-gray-400">Create a personal access token for cloud sync</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white transition-colors"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-8">
          {/* Important Notice */}
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5" />
              <div>
                <h3 className="text-yellow-400 font-medium mb-1">Important Security Notice</h3>
                <p className="text-yellow-300/80 text-sm">
                  Your GitHub token will be stored locally in your browser and never shared. 
                  It's used only to sync your portfolio data to your GitHub repository.
                </p>
              </div>
            </div>
          </div>

          {/* Steps */}
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-white mb-4">Step-by-Step Instructions</h3>
            
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-start space-x-4 p-4 bg-gray-800/50 rounded-xl border border-gray-700/50"
              >
                <div className={`p-3 bg-gradient-to-r ${step.color} rounded-lg flex-shrink-0`}>
                  <step.icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-sm font-bold text-purple-400">Step {index + 1}</span>
                    <h4 className="text-lg font-semibold text-white">{step.title}</h4>
                  </div>
                  <p className="text-gray-400 mb-2">{step.description}</p>
                  <p className="text-sm text-blue-300 font-medium">{step.action}</p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Required Scopes */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-white">Required Permissions (Scopes)</h3>
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4">
              <div className="flex items-start space-x-3 mb-3">
                <Info className="w-5 h-5 text-blue-400 mt-0.5" />
                <div>
                  <h4 className="text-blue-400 font-medium">Select these scopes when creating your token:</h4>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {requiredScopes.map((scope) => (
                  <div key={scope.name} className="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <div>
                      <span className="text-white font-medium">{scope.name}</span>
                      <p className="text-gray-400 text-xs">{scope.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-white">Quick Links</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <a
                href="https://github.com/settings/tokens"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-3 p-4 bg-gradient-to-r from-purple-600/20 to-blue-600/20 border border-purple-500/30 rounded-xl hover:border-purple-400/50 transition-all duration-200 group"
              >
                <Github className="w-6 h-6 text-purple-400" />
                <div className="flex-1">
                  <h4 className="text-white font-medium">GitHub Tokens Page</h4>
                  <p className="text-gray-400 text-sm">Direct link to create tokens</p>
                </div>
                <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-white" />
              </a>

              <a
                href="https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-3 p-4 bg-gradient-to-r from-green-600/20 to-emerald-600/20 border border-green-500/30 rounded-xl hover:border-green-400/50 transition-all duration-200 group"
              >
                <Key className="w-6 h-6 text-green-400" />
                <div className="flex-1">
                  <h4 className="text-white font-medium">GitHub Documentation</h4>
                  <p className="text-gray-400 text-sm">Official token creation guide</p>
                </div>
                <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-white" />
              </a>
            </div>
          </div>

          {/* Example Token Format */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-white">Token Format</h3>
            <div className="bg-gray-800/50 border border-gray-700/50 rounded-xl p-4">
              <p className="text-gray-400 text-sm mb-2">Your token will look like this:</p>
              <div className="flex items-center space-x-2 p-3 bg-gray-900/50 rounded-lg font-mono text-sm">
                <span className="text-green-400">ghp_</span>
                <span className="text-gray-300">xxxxxxxxxxxxxxxxxxxx</span>
                <button
                  onClick={() => navigator.clipboard.writeText('ghp_xxxxxxxxxxxxxxxxxxxx')}
                  className="p-1 text-gray-400 hover:text-white transition-colors"
                  title="Copy example format"
                >
                  <Copy className="w-4 h-4" />
                </button>
              </div>
              <p className="text-gray-500 text-xs mt-2">
                * The actual token will be longer and contain random characters
              </p>
            </div>
          </div>

          {/* Security Tips */}
          <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5" />
              <div>
                <h3 className="text-red-400 font-medium mb-2">Security Best Practices</h3>
                <ul className="text-red-300/80 text-sm space-y-1">
                  <li>• Never share your token with anyone</li>
                  <li>• Set an expiration date for your token</li>
                  <li>• Only grant the minimum required permissions</li>
                  <li>• Regenerate tokens periodically</li>
                  <li>• Delete tokens you no longer use</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-700">
          <div className="flex items-center justify-between">
            <p className="text-gray-400 text-sm">
              Need help? Check the GitHub documentation or contact support.
            </p>
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-medium rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-200"
            >
              Got it!
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default GitHubTokenGuide;
