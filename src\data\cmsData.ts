// CMS Data Management System
export interface Project {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  image: string;
  technologies: string[];
  github: string;
  live: string;
  category: string;
  featured: boolean;
  stats: {
    stars: number;
    forks: number;
    contributors: number;
  };
  timeline: string;
  status: 'Completed' | 'In Progress' | 'Planning';
  createdAt: string;
  updatedAt: string;
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  image: string;
  author: string;
  category: string;
  tags: string[];
  published: boolean;
  featured: boolean;
  readTime: number;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

export interface PersonalInfo {
  name: string;
  title: string;
  email: string;
  phone: string;
  location: string;
  website: string;
  github: string;
  linkedin: string;
  bio: string;
  avatar: string;
  resume: string;
}

export interface Skill {
  id: string;
  name: string;
  category: string;
  level: number;
  icon?: string;
  description?: string;
}

export interface Experience {
  id: string;
  company: string;
  position: string;
  startDate: string;
  endDate?: string;
  current: boolean;
  description: string;
  technologies: string[];
  achievements: string[];
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  startDate: string;
  endDate: string;
  gpa?: string;
  description?: string;
}

export interface CMSData {
  personalInfo: PersonalInfo;
  projects: Project[];
  blogPosts: BlogPost[];
  skills: Skill[];
  experience: Experience[];
  education: Education[];
  settings: {
    theme: 'dark' | 'light';
    language: string;
    analytics: {
      googleAnalytics?: string;
      hotjar?: string;
    };
    seo: {
      title: string;
      description: string;
      keywords: string[];
      ogImage: string;
    };
  };
}

// Default data structure
export const defaultCMSData: CMSData = {
  personalInfo: {
    name: 'Nural Bhardwaj',
    title: 'Full Stack Developer & UI/UX Designer',
    email: '<EMAIL>',
    phone: '+91-7404814726',
    location: 'Gurugram, India',
    website: 'https://nuralbhardwaj.me',
    github: 'https://github.com/NuralBhardwaj/',
    linkedin: 'https://www.linkedin.com/in/nural-bhardwaj/',
    bio: 'Passionate full-stack developer with expertise in modern web technologies. I love creating intuitive user experiences and robust backend solutions.',
    avatar: '/Admin.png',
    resume: '/NURAL_Bhardwaj_Resume.pdf'
  },
  projects: [
    {
      id: '1',
      title: 'Advanced Portfolio Website',
      description: 'Modern portfolio with 3D animations, particle systems, and multi-language support.',
      longDescription: 'A cutting-edge portfolio website featuring advanced 3D animations, interactive particle systems, multi-language support for 7 languages, dark theme, responsive design, and optimized performance. Built with modern React ecosystem and advanced animation libraries.',
      image: 'https://images.pexels.com/photos/196644/pexels-photo-196644.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['React', 'TypeScript', 'Framer Motion', 'Tailwind CSS', 'Vite', 'EmailJS'],
      github: 'https://github.com/NuralBhardwaj/',
      live: 'https://nuralbhardwaj.me',
      category: 'Full Stack',
      featured: true,
      stats: { stars: 145, forks: 28, contributors: 1 },
      timeline: '3 months',
      status: 'Completed',
      createdAt: '2024-01-15',
      updatedAt: '2024-12-15'
    }
  ],
  blogPosts: [
    {
      id: '1',
      title: 'Building Modern Web Applications with React and TypeScript',
      slug: 'building-modern-web-apps-react-typescript',
      excerpt: 'Learn how to build scalable and maintainable web applications using React and TypeScript with best practices and modern tooling.',
      content: '# Building Modern Web Applications\n\nIn this comprehensive guide, we\'ll explore how to build modern web applications...',
      image: 'https://images.pexels.com/photos/11035380/pexels-photo-11035380.jpeg?auto=compress&cs=tinysrgb&w=800',
      author: 'Nural Bhardwaj',
      category: 'Development',
      tags: ['React', 'TypeScript', 'Web Development', 'Frontend'],
      published: true,
      featured: true,
      readTime: 8,
      createdAt: '2024-12-01',
      updatedAt: '2024-12-01',
      publishedAt: '2024-12-01'
    }
  ],
  skills: [
    {
      id: '1',
      name: 'React',
      category: 'Frontend',
      level: 95,
      description: 'Advanced React development with hooks, context, and modern patterns'
    },
    {
      id: '2',
      name: 'TypeScript',
      category: 'Frontend',
      level: 90,
      description: 'Type-safe JavaScript development with advanced TypeScript features'
    },
    {
      id: '3',
      name: 'Node.js',
      category: 'Backend',
      level: 85,
      description: 'Server-side JavaScript development with Express and modern frameworks'
    }
  ],
  experience: [
    {
      id: '1',
      company: 'Tech Innovations Inc.',
      position: 'Senior Full Stack Developer',
      startDate: '2022-01-01',
      current: true,
      description: 'Leading development of modern web applications using React, Node.js, and cloud technologies.',
      technologies: ['React', 'Node.js', 'TypeScript', 'AWS', 'MongoDB'],
      achievements: [
        'Increased application performance by 40%',
        'Led a team of 5 developers',
        'Implemented CI/CD pipelines'
      ]
    }
  ],
  education: [
    {
      id: '1',
      institution: 'University of Technology',
      degree: 'Bachelor of Science',
      field: 'Computer Science',
      startDate: '2018-09-01',
      endDate: '2022-06-01',
      gpa: '3.8',
      description: 'Focused on software engineering, algorithms, and web development.'
    }
  ],
  settings: {
    theme: 'dark',
    language: 'en',
    analytics: {
      googleAnalytics: 'G-V4VD9F3LXK',
      hotjar: ''
    },
    seo: {
      title: 'Nural Bhardwaj - Full Stack Developer & UI/UX Designer',
      description: 'Passionate about creating amazing digital experiences with modern technologies and beautiful design.',
      keywords: ['Full Stack Developer', 'UI/UX Designer', 'React', 'TypeScript', 'Web Development'],
      ogImage: '/favicon.svg' // TODO: Create proper OG image (1200x630px) and update path
    }
  }
};
