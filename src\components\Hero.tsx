import { motion } from 'framer-motion';
import { ArrowDown, Copy, Download, ExternalLink, Linkedin, Mail, Play } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useLanguage } from '../contexts/LanguageContext';
import { PersonalInfo } from '../data/cmsData';
import { cmsService } from '../services/cmsService';
import {
    fadeInUp,
    staggerContainer,
    staggerItem
} from '../utils/animations';
import AnimatedButton from './AnimatedButton';
import { MagneticElement, ScrollProgress } from './AnimatedSection';
import { EmailOptionsToast, ErrorToast, SuccessToast } from './CustomToast';
import GitHubIcon from './GitHubIcon';
import SnowflakeParticleSystem from './SnowflakeParticleSystem';

const Hero: React.FC = () => {
  const { t } = useLanguage();
  const [text, setText] = useState('');
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [personalInfo, setPersonalInfo] = useState<PersonalInfo | null>(null);

  useEffect(() => {
    const info = cmsService.getPersonalInfo();
    setPersonalInfo(info);
  }, []);

  const phrases = [
    { text: 'Full Stack Developer', style: 'bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400' },
    { text: 'UI/UX Designer', style: 'bg-gradient-to-r from-pink-400 via-rose-500 to-orange-400' },
    { text: 'Creative Thinker', style: 'bg-gradient-to-r from-green-400 via-emerald-500 to-teal-400' },
    { text: 'Problem Solver', style: 'bg-gradient-to-r from-yellow-400 via-amber-500 to-red-400' }
  ];

  // Ultimate email handling with custom toasts
  const handleEmailClick = (e: React.MouseEvent) => {
    e.preventDefault();

    // Show custom email options toast
    toast((t) => (
      <EmailOptionsToast
        t={t}
        onCopy={copyEmailToClipboard}
        onGmail={openGmail}
      />
    ), {
      duration: 15000,
    });
  };

  const copyEmailToClipboard = async () => {
    const email = personalInfo?.email || '<EMAIL>';

    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(email);
        toast(() => (
          <SuccessToast
            message="Email copied to clipboard!"
            icon={<Copy className="w-5 h-5 text-green-400" />}
          />
        ));
      } else {
        // Fallback for older browsers
        try {
          // Modern clipboard API
          if (navigator.clipboard && window.isSecureContext) {
            await (navigator.clipboard as any).writeText(email);
          } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = email;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            (document as any).execCommand('copy');
            document.body.removeChild(textArea);
          }

          toast(() => (
            <SuccessToast
              message="Email copied to clipboard!"
              icon={<Copy className="w-5 h-5 text-green-400" />}
            />
          ));
        } catch (err) {
          toast(() => (
            <ErrorToast message={`Copy failed. Please copy manually: ${email}`} />
          ));
        }
      }
    } catch (err) {
      toast(() => (
        <ErrorToast message={`Copy failed. Please copy manually: ${email}`} />
      ));
    }
  };

  const openGmail = () => {
    const email = personalInfo?.email || '<EMAIL>';
    window.open(`https://mail.google.com/mail/?view=cm&fs=1&to=${email}`, '_blank');
    toast(() => (
      <SuccessToast
        message="Opening Gmail..."
        icon={<ExternalLink className="w-5 h-5 text-green-400" />}
      />
    ));
  };

  // Enhanced CV Download functionality
  const handleDownloadCV = async () => {
    try {
      const resumeUrl = personalInfo?.resume || '/NURAL_Bhardwaj_Resume.pdf';
      const fileName = personalInfo?.name ?
        `${personalInfo.name.replace(/\s+/g, '_')}_Resume.pdf` :
        'NURAL_Bhardwaj_Resume.pdf';

      // Check if file exists first
      const response = await fetch(resumeUrl, { method: 'HEAD' });

      if (!response.ok) {
        throw new Error('File not found');
      }

      // Create download link
      const link = document.createElement('a');
      link.href = resumeUrl;
      link.download = fileName;
      link.style.display = 'none';

      // Add to DOM, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Show success toast
      toast(() => (
        <SuccessToast
          message="📄 CV download started! Check your downloads folder."
          icon={<Download className="w-5 h-5 text-green-400" />}
        />
      ));

      // Analytics tracking (optional)
      if (typeof (window as any).gtag !== 'undefined') {
        (window as any).gtag('event', 'download', {
          event_category: 'CV',
          event_label: fileName
        });
      }

    } catch (error) {
      console.error('Download error:', error);

      // Fallback: Open in new tab
      const fallbackUrl = personalInfo?.resume || '/NURAL_Bhardwaj_Resume.pdf';
      window.open(fallbackUrl, '_blank');

      // Show fallback toast
      toast(() => (
        <SuccessToast
          message="📄 CV opened in new tab. Right-click to save!"
          icon={<ExternalLink className="w-5 h-5 text-green-400" />}
        />
      ));
    }
  };

  useEffect(() => {
    const currentPhrase = phrases[currentPhraseIndex].text;

    if (!isDeleting) {
      if (text.length < currentPhrase.length) {
        const timeout = setTimeout(() => {
          setText(currentPhrase.slice(0, text.length + 1));
        }, 35); // Ultra fast typing speed
        return () => clearTimeout(timeout);
      } else {
        // Phrase is complete, wait before deleting
        const timeout = setTimeout(() => {
          setIsDeleting(true);
        }, 800); // Ultra short wait time - 0.8 seconds
        return () => clearTimeout(timeout);
      }
    } else {
      if (text.length > 0) {
        const timeout = setTimeout(() => {
          setText(text.slice(0, -1));
        }, 20); // Ultra fast deleting
        return () => clearTimeout(timeout);
      } else {
        // Deletion complete, move to next phrase
        setIsDeleting(false);
        setCurrentPhraseIndex((prev) => (prev + 1) % phrases.length);
      }
    }
  }, [text, isDeleting, currentPhraseIndex, phrases]);



  return (
    <>
      <ScrollProgress />
      
      <motion.section
        id="home"
        className="hero-section min-h-screen flex items-center justify-center relative overflow-hidden pt-16 sm:pt-0"
        variants={staggerContainer}
        initial="hidden"
        animate="visible"
      >
        {/* Clean Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-900" />
        
        {/* Static Grid for better performance */}
        <div className="absolute inset-0 opacity-3">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
                linear-gradient(rgba(139, 92, 246, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(139, 92, 246, 0.05) 1px, transparent 1px)
              `,
              backgroundSize: '60px 60px',
            }}
          />
        </div>
        
        {/* Optimized Interactive Particles */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-purple-400/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -20, 0],
                opacity: [0.1, 0.6, 0.1],
              }}
              transition={{
                duration: 8 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 4,
                ease: "easeInOut"
              }}
            />
          ))}

          {/* Simplified Floating Shapes */}
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={`shape-${i}`}
              className="absolute w-6 h-6 border border-cyan-400/15 rounded-full"
              style={{
                left: `${30 + Math.random() * 40}%`,
                top: `${30 + Math.random() * 40}%`,
              }}
              animate={{
                rotate: [0, 360],
              }}
              transition={{
                duration: 15 + Math.random() * 10,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          ))}
        </div>

        {/* Beautiful Independent Snowflake System - Optimized for Mobile */}
        <SnowflakeParticleSystem
          count={15}
          showTrails={true}
          className="opacity-60"
        />

        {/* Main Content - Fixed mobile spacing */}
        <div className="hero-content container mx-auto px-4 sm:px-6 text-center relative z-10 pt-16 sm:pt-0">
          <div className="space-y-6 sm:space-y-8">


            {/* Clean Title - Fixed mobile spacing */}
            <motion.div
              className="relative"
              variants={fadeInUp}
            >
              <motion.h1
                className="hero-title text-5xl sm:text-6xl md:text-7xl lg:text-8xl xl:text-9xl font-black text-white leading-tight sm:leading-none tracking-tight"
                variants={staggerContainer}
                initial="hidden"
                animate="visible"
              >
                <motion.span className="block mb-0" variants={staggerItem} style={{ wordSpacing: 'normal', letterSpacing: 'normal' }}>
                  <span className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl" style={{ wordSpacing: 'normal', letterSpacing: 'normal' }}>{t('hero.greeting')}</span>
                  <span style={{ marginLeft: '0.25rem', wordSpacing: 'normal', letterSpacing: 'normal' }}>
                    <MagneticElement className="relative inline-block">
                      <motion.span
                        className="bg-gradient-to-r from-purple-400 via-pink-500 to-cyan-400 bg-clip-text text-transparent"
                        whileHover={{ scale: 1.05 }}
                        transition={{ type: "spring", stiffness: 300 }}
                        style={{ wordSpacing: 'normal', letterSpacing: 'normal' }}
                      >
                        Nural
                      </motion.span>
                    </MagneticElement>
                  </span>
                </motion.span>
                <motion.span
                  className="block mt-0 bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-400 bg-clip-text text-transparent"
                  variants={staggerItem}
                  style={{ wordSpacing: 'normal', letterSpacing: 'normal' }}
                >
                  Bhardwaj
                </motion.span>
              </motion.h1>
            </motion.div>

            {/* Enhanced Dynamic Typing Effect - Fixed mobile spacing */}
            <motion.div
              className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl text-gray-300 min-h-[80px] sm:min-h-[90px] md:min-h-[100px] flex items-center justify-center px-4"
              variants={fadeInUp}
            >
              <div className="text-center text-spacing-fix">
                <span className="mr-2 sm:mr-4 text-spacing-fix" style={{ wordSpacing: 'normal', letterSpacing: 'normal' }}>A </span>
                <div className="relative inline-block min-w-[200px] sm:min-w-[300px] md:min-w-[400px] text-left">
                  <motion.span
                    className={`text-transparent ${phrases[currentPhraseIndex].style} bg-clip-text font-bold text-spacing-fix`}
                    key={`${currentPhraseIndex}-${text}`}
                    initial={{ opacity: 0.8 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.2 }}
                    style={{
                      display: 'inline-block',
                      minHeight: '1.2em',
                      wordSpacing: 'normal !important',
                      letterSpacing: 'normal !important',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {text}
                  </motion.span>
                  <motion.span
                    className="font-thin ml-1"
                    style={{
                      background: phrases[currentPhraseIndex].style.replace('bg-gradient-to-r', 'linear-gradient(to right'),
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text',
                      color: 'transparent'
                    }}
                    animate={{ opacity: [1, 0, 1] }}
                    transition={{ duration: 1.2, repeat: Infinity }}
                  >
                    |
                  </motion.span>
                </div>
              </div>
            </motion.div>

            {/* Simple Description - Fixed mobile spacing */}
            <motion.p
              className="text-lg sm:text-xl md:text-xl lg:text-2xl text-gray-400 max-w-4xl mx-auto leading-relaxed font-light px-4 mt-6"
              variants={fadeInUp}
            >
              {t('hero.description')}
            </motion.p>
            
            {/* Clean CTA Buttons - Fixed mobile spacing */}
            <motion.div
              className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 pt-8 sm:pt-12 px-4"
              variants={fadeInUp}
            >
              <AnimatedButton
                onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}
                variant="primary"
                size="lg"
                icon={<Play className="w-5 h-5 sm:w-6 sm:h-6" />}
                magnetic={true}
                ripple={true}
              >
                {t('hero.cta.projects')}
              </AnimatedButton>

              <AnimatedButton
                onClick={handleDownloadCV}
                variant="outline"
                size="lg"
                icon={<Download className="w-5 h-5 sm:w-6 sm:h-6" />}
                magnetic={true}
                ripple={true}
              >
                Download CV
              </AnimatedButton>
            </motion.div>
            
            {/* Clean Social Links - Fixed mobile spacing */}
            <motion.div
              className="flex justify-center space-x-4 sm:space-x-6 md:space-x-8 pt-8 sm:pt-12 relative px-4"
              variants={fadeInUp}
            >
              {[
                { Icon: GitHubIcon, href: personalInfo?.github || 'https://github.com/NuralBhardwaj/', label: 'GitHub', isEmail: false },
                { Icon: Linkedin, href: personalInfo?.linkedin || 'https://www.linkedin.com/in/nural-bhardwaj/', label: 'LinkedIn', isEmail: false },
                { Icon: Mail, href: '#', label: 'Email', isEmail: true }
              ].map(({ Icon, href, label, isEmail }) => (
                <motion.div key={label} className="relative">
                  <motion.a
                    href={isEmail ? '#' : href}
                    target={isEmail ? '_self' : '_blank'}
                    rel={isEmail ? undefined : 'noopener noreferrer'}
                    onClick={isEmail ? handleEmailClick : undefined}
                    className="group relative p-3 sm:p-4 bg-white/5 backdrop-blur-sm rounded-xl hover:bg-white/10 transition-all duration-300 border border-white/10 hover:border-purple-500/30 block"
                    aria-label={label}
                    variants={staggerItem}
                    whileHover={{
                      scale: 1.1,
                      y: -5,
                      transition: { duration: 0.2 }
                    }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Icon className="w-5 h-5 sm:w-6 sm:h-6 text-white group-hover:text-purple-400 transition-colors duration-300" />
                  </motion.a>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
        
        {/* Scroll Indicator - Enhanced mobile positioning and made clickable */}
        <motion.div
          className="absolute bottom-4 left-4 sm:bottom-6 sm:left-6 md:bottom-12 md:left-12 cursor-pointer z-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.5, duration: 0.8 }}
          onClick={() => {
            const aboutSection = document.getElementById('about');
            if (aboutSection) {
              aboutSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
          }}
        >
          <motion.div
            className="flex flex-col items-center space-y-2 sm:space-y-3 md:space-y-4 hover:scale-110 transition-transform duration-300 p-2 sm:p-0"
            animate={{ y: [0, -10, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.div
              className="text-gray-300 text-xs sm:text-sm md:text-base font-medium text-center hover:text-purple-400 transition-colors duration-300 px-2 drop-shadow-lg"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <span className="hidden sm:inline">Scroll to explore</span>
              <span className="sm:hidden">Tap to explore</span>
            </motion.div>
            <motion.div className="w-4 h-7 sm:w-5 sm:h-8 md:w-6 md:h-10 lg:w-7 lg:h-12 border-2 border-white/40 hover:border-purple-400/60 rounded-full flex justify-center transition-colors duration-300 bg-black/20 backdrop-blur-sm">
              <motion.div
                className="w-0.5 h-1.5 sm:w-1 sm:h-2 md:w-1.5 md:h-3 lg:h-4 bg-gradient-to-b from-purple-400 to-cyan-400 rounded-full mt-1 sm:mt-1.5 md:mt-2 lg:mt-3"
                animate={{ y: [0, 15, 0], opacity: [1, 0, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </motion.div>
            <ArrowDown className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 lg:w-7 lg:h-7 text-white/60 hover:text-purple-400/80 transition-colors duration-300" />
          </motion.div>
        </motion.div>
      </motion.section>
    </>
  );
};

export default Hero;
