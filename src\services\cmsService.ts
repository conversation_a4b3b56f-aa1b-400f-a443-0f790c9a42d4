import { CMSData, Project, BlogPost, PersonalInfo, Skill, Experience, Education, defaultCMSData } from '../data/cmsData';
import { cloudCMSService } from './cloudCMSService';

const CMS_STORAGE_KEY = 'nural_portfolio_cms_data';
const ADMIN_AUTH_KEY = 'nural_portfolio_admin_auth';

// Secure authentication - credentials are now private
const ADMIN_CREDENTIALS = {
  username: '<EMAIL>',
  password: 'nural@23#3*admin', // This is now hidden from public view
};

export class CMSService {
  private static instance: CMSService;
  private data: CMSData;
  private isCloudSyncEnabled: boolean = false;

  private constructor() {
    this.data = this.loadData();
    this.initializeCloudSync();
  }

  public static getInstance(): CMSService {
    if (!CMSService.instance) {
      CMSService.instance = new CMSService();
    }
    return CMSService.instance;
  }

  // Authentication
  public authenticate(username: string, password: string): boolean {
    const isValid = username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password;

    if (isValid) {
      localStorage.setItem(ADMIN_AUTH_KEY, 'authenticated');
    }

    return isValid;
  }

  public isAuthenticated(): boolean {
    return localStorage.getItem(ADMIN_AUTH_KEY) === 'authenticated';
  }

  public logout(): void {
    localStorage.removeItem(ADMIN_AUTH_KEY);
  }

  // Cloud Sync Initialization
  private async initializeCloudSync(): Promise<void> {
    // Only enable cloud sync if we have a valid token
    this.isCloudSyncEnabled = cloudCMSService.isCloudSyncEnabled();

    if (this.isCloudSyncEnabled) {
      try {
        // Double-check token validity before attempting cloud operations
        const token = localStorage.getItem('github_token');
        if (!token || !token.startsWith('ghp_') || token.length < 40) {
          console.log('Invalid GitHub token detected, disabling cloud sync');
          this.isCloudSyncEnabled = false;
          return;
        }

        // Load data from cloud on startup only if token is valid
        const cloudData = await cloudCMSService.fetchCloudData();
        this.data = cloudData;

        // Also save to localStorage as backup
        localStorage.setItem(CMS_STORAGE_KEY, JSON.stringify(cloudData));

        console.log('Successfully loaded data from cloud');
      } catch (error: any) {
        console.error('Error loading from cloud, using local data:', error);
        // Disable cloud sync if there's an authentication error
        if (error.message && (error.message.includes('Bad credentials') || error.message.includes('401'))) {
          this.isCloudSyncEnabled = false;
          console.log('Cloud sync disabled due to authentication error');
        }
      }
    } else {
      console.log('Cloud sync not enabled - no valid GitHub token');
    }
  }

  // Data Management
  private loadData(): CMSData {
    try {
      const stored = localStorage.getItem(CMS_STORAGE_KEY);
      if (stored) {
        const parsedData = JSON.parse(stored);
        // Merge with default data to ensure all properties exist
        return { ...defaultCMSData, ...parsedData };
      }
    } catch (error) {
      console.error('Error loading CMS data:', error);
    }
    return defaultCMSData;
  }

  private async saveData(): Promise<void> {
    try {
      // Always save to localStorage first
      localStorage.setItem(CMS_STORAGE_KEY, JSON.stringify(this.data));

      // If cloud sync is enabled, also save to cloud
      if (this.isCloudSyncEnabled && cloudCMSService.isCloudSyncEnabled()) {
        // Double-check token validity before attempting cloud save
        const token = localStorage.getItem('github_token');
        if (!token || !token.startsWith('ghp_') || token.length < 40) {
          console.log('Invalid GitHub token detected, disabling cloud sync');
          this.isCloudSyncEnabled = false;
          return;
        }

        try {
          const success = await cloudCMSService.saveCloudData(this.data);
          if (success) {
            console.log('Data successfully synced to cloud');
          } else {
            console.warn('Failed to sync data to cloud, saved locally only');
          }
        } catch (error: any) {
          console.error('Error syncing to cloud:', error);
          // Disable cloud sync if there's an authentication error
          if (error.message && (error.message.includes('Bad credentials') || error.message.includes('401'))) {
            this.isCloudSyncEnabled = false;
            console.log('Cloud sync disabled due to authentication error');
          }
        }
      }
    } catch (error) {
      console.error('Error saving CMS data:', error);
    }
  }

  // Public API
  public getAllData(): CMSData {
    return { ...this.data };
  }

  // Projects
  public getProjects(): Project[] {
    return [...this.data.projects];
  }

  public getFeaturedProjects(): Project[] {
    return this.data.projects.filter(p => p.featured);
  }

  public getProjectById(id: string): Project | undefined {
    return this.data.projects.find(p => p.id === id);
  }

  public addProject(project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Project {
    const newProject: Project = {
      ...project,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    this.data.projects.push(newProject);
    this.saveData();
    return newProject;
  }

  public updateProject(id: string, updates: Partial<Project>): Project | null {
    const index = this.data.projects.findIndex(p => p.id === id);
    if (index === -1) return null;

    this.data.projects[index] = {
      ...this.data.projects[index],
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    this.saveData();
    return this.data.projects[index];
  }

  public deleteProject(id: string): boolean {
    const index = this.data.projects.findIndex(p => p.id === id);
    if (index === -1) return false;

    this.data.projects.splice(index, 1);
    this.saveData();
    return true;
  }

  // Blog Posts
  public getBlogPosts(): BlogPost[] {
    return [...this.data.blogPosts];
  }

  public getPublishedBlogPosts(): BlogPost[] {
    return this.data.blogPosts.filter(p => p.published);
  }

  public getFeaturedBlogPosts(): BlogPost[] {
    return this.data.blogPosts.filter(p => p.featured && p.published);
  }

  public getBlogPostById(id: string): BlogPost | undefined {
    return this.data.blogPosts.find(p => p.id === id);
  }

  public getBlogPostBySlug(slug: string): BlogPost | undefined {
    return this.data.blogPosts.find(p => p.slug === slug);
  }

  public addBlogPost(post: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt'>): BlogPost {
    const newPost: BlogPost = {
      ...post,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      publishedAt: post.published ? new Date().toISOString() : undefined,
    };
    this.data.blogPosts.push(newPost);
    this.saveData();
    return newPost;
  }

  public updateBlogPost(id: string, updates: Partial<BlogPost>): BlogPost | null {
    const index = this.data.blogPosts.findIndex(p => p.id === id);
    if (index === -1) return null;

    const wasPublished = this.data.blogPosts[index].published;
    const isNowPublished = updates.published !== undefined ? updates.published : wasPublished;

    this.data.blogPosts[index] = {
      ...this.data.blogPosts[index],
      ...updates,
      updatedAt: new Date().toISOString(),
      publishedAt: !wasPublished && isNowPublished ? new Date().toISOString() : this.data.blogPosts[index].publishedAt,
    };
    this.saveData();
    return this.data.blogPosts[index];
  }

  public deleteBlogPost(id: string): boolean {
    const index = this.data.blogPosts.findIndex(p => p.id === id);
    if (index === -1) return false;

    this.data.blogPosts.splice(index, 1);
    this.saveData();
    return true;
  }

  // Personal Info
  public getPersonalInfo(): PersonalInfo {
    return { ...this.data.personalInfo };
  }

  public updatePersonalInfo(updates: Partial<PersonalInfo>): PersonalInfo {
    this.data.personalInfo = { ...this.data.personalInfo, ...updates };
    this.saveData();
    return this.data.personalInfo;
  }

  // Skills
  public getSkills(): Skill[] {
    return [...this.data.skills];
  }

  public addSkill(skill: Omit<Skill, 'id'>): Skill {
    const newSkill: Skill = {
      ...skill,
      id: Date.now().toString(),
    };
    this.data.skills.push(newSkill);
    this.saveData();
    return newSkill;
  }

  public updateSkill(id: string, updates: Partial<Skill>): Skill | null {
    const index = this.data.skills.findIndex(s => s.id === id);
    if (index === -1) return null;

    this.data.skills[index] = { ...this.data.skills[index], ...updates };
    this.saveData();
    return this.data.skills[index];
  }

  public deleteSkill(id: string): boolean {
    const index = this.data.skills.findIndex(s => s.id === id);
    if (index === -1) return false;

    this.data.skills.splice(index, 1);
    this.saveData();
    return true;
  }

  // Experience
  public getExperience(): Experience[] {
    return [...this.data.experience];
  }

  public addExperience(experience: Omit<Experience, 'id'>): Experience {
    const newExperience: Experience = {
      ...experience,
      id: Date.now().toString(),
    };
    this.data.experience.push(newExperience);
    this.saveData();
    return newExperience;
  }

  public updateExperience(id: string, updates: Partial<Experience>): Experience | null {
    const index = this.data.experience.findIndex(e => e.id === id);
    if (index === -1) return null;

    this.data.experience[index] = { ...this.data.experience[index], ...updates };
    this.saveData();
    return this.data.experience[index];
  }

  public deleteExperience(id: string): boolean {
    const index = this.data.experience.findIndex(e => e.id === id);
    if (index === -1) return false;

    this.data.experience.splice(index, 1);
    this.saveData();
    return true;
  }

  // Education
  public getEducation(): Education[] {
    return [...this.data.education];
  }

  public addEducation(education: Omit<Education, 'id'>): Education {
    const newEducation: Education = {
      ...education,
      id: Date.now().toString(),
    };
    this.data.education.push(newEducation);
    this.saveData();
    return newEducation;
  }

  public updateEducation(id: string, updates: Partial<Education>): Education | null {
    const index = this.data.education.findIndex(e => e.id === id);
    if (index === -1) return null;

    this.data.education[index] = { ...this.data.education[index], ...updates };
    this.saveData();
    return this.data.education[index];
  }

  public deleteEducation(id: string): boolean {
    const index = this.data.education.findIndex(e => e.id === id);
    if (index === -1) return false;

    this.data.education.splice(index, 1);
    this.saveData();
    return true;
  }

  // Settings
  public getSettings() {
    return { ...this.data.settings };
  }

  public updateSettings(updates: Partial<typeof this.data.settings>) {
    this.data.settings = { ...this.data.settings, ...updates };
    this.saveData();
    return this.data.settings;
  }

  // Utility methods
  public exportData(): string {
    return JSON.stringify(this.data, null, 2);
  }

  public importData(jsonData: string): boolean {
    try {
      const importedData = JSON.parse(jsonData);
      this.data = { ...defaultCMSData, ...importedData };
      this.saveData();
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }

  public resetToDefaults(): void {
    this.data = { ...defaultCMSData };
    this.saveData();
  }

  // Cloud Sync Management
  public enableCloudSync(githubToken: string): void {
    cloudCMSService.setGitHubToken(githubToken);
    this.isCloudSyncEnabled = true;
    console.log('Cloud sync enabled');
  }

  public disableCloudSync(): void {
    this.isCloudSyncEnabled = false;
    localStorage.removeItem('github_token');
    cloudCMSService.clearCache();
    console.log('Cloud sync disabled and cache cleared');
  }

  public isCloudSyncActive(): boolean {
    return this.isCloudSyncEnabled;
  }

  public async testCloudConnection(): Promise<{ success: boolean; message: string }> {
    return await cloudCMSService.testConnection();
  }

  public async syncToCloud(): Promise<boolean> {
    if (!this.isCloudSyncEnabled) {
      console.warn('Cloud sync is not enabled');
      return false;
    }

    try {
      const success = await cloudCMSService.saveCloudData(this.data);
      if (success) {
        console.log('Manual sync to cloud successful');
      }
      return success;
    } catch (error: any) {
      console.error('Error during manual sync:', error);
      return false;
    }
  }

  public async loadFromCloud(): Promise<boolean> {
    if (!this.isCloudSyncEnabled) {
      console.warn('Cloud sync is not enabled');
      return false;
    }

    try {
      const cloudData = await cloudCMSService.fetchCloudData();
      this.data = cloudData;
      localStorage.setItem(CMS_STORAGE_KEY, JSON.stringify(cloudData));
      console.log('Successfully loaded data from cloud');
      return true;
    } catch (error: any) {
      console.error('Error loading from cloud:', error);
      return false;
    }
  }

  public clearCloudCache(): void {
    cloudCMSService.clearCache();
  }
}

// Export singleton instance
export const cmsService = CMSService.getInstance();
