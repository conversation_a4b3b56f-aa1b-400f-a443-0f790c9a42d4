import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Star, 
  Calendar, 
  Clock, 
  Tag,
  Save,
  X,
  BookOpen,
  FileText,
  Globe,
  EyeOff
} from 'lucide-react';
import { cmsService } from '../../services/cmsService';
import { BlogPost } from '../../data/cmsData';
import toast from 'react-hot-toast';
import { SuccessToast, ErrorToast } from '../CustomToast';

interface BlogManagerProps {
  onDataChange: () => void;
}

const BlogManager: React.FC<BlogManagerProps> = ({ onDataChange }) => {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [editingPost, setEditingPost] = useState<BlogPost | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    image: '',
    author: 'Nural Bhardwaj',
    category: '',
    tags: [] as string[],
    published: false,
    featured: false,
    readTime: 5
  });
  const [tagInput, setTagInput] = useState('');

  useEffect(() => {
    loadBlogPosts();
  }, []);

  const loadBlogPosts = () => {
    const postsData = cmsService.getBlogPosts();
    setBlogPosts(postsData);
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const calculateReadTime = (content: string) => {
    const wordsPerMinute = 200;
    const words = content.split(/\s+/).length;
    return Math.max(1, Math.ceil(words / wordsPerMinute));
  };

  const resetForm = () => {
    setFormData({
      title: '',
      slug: '',
      excerpt: '',
      content: '',
      image: '',
      author: 'Nural Bhardwaj',
      category: '',
      tags: [],
      published: false,
      featured: false,
      readTime: 5
    });
    setTagInput('');
    setEditingPost(null);
  };

  const openModal = (post?: BlogPost) => {
    if (post) {
      setEditingPost(post);
      setFormData({
        title: post.title,
        slug: post.slug,
        excerpt: post.excerpt,
        content: post.content,
        image: post.image,
        author: post.author,
        category: post.category,
        tags: [...post.tags],
        published: post.published,
        featured: post.featured,
        readTime: post.readTime
      });
    } else {
      resetForm();
    }
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    resetForm();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.excerpt || !formData.content || !formData.category) {
      toast(() => (
        <ErrorToast
          message="Please fill in all required fields"
          icon={<X className="w-5 h-5 text-red-400" />}
        />
      ));
      return;
    }

    const slug = formData.slug || generateSlug(formData.title);
    const readTime = calculateReadTime(formData.content);

    try {
      const postData = {
        ...formData,
        slug,
        readTime
      };

      if (editingPost) {
        cmsService.updateBlogPost(editingPost.id, postData);
        toast(() => (
          <SuccessToast
            message="Blog post updated successfully!"
            icon={<Save className="w-5 h-5 text-green-400" />}
          />
        ));
      } else {
        cmsService.addBlogPost(postData);
        toast(() => (
          <SuccessToast
            message="Blog post created successfully!"
            icon={<Plus className="w-5 h-5 text-green-400" />}
          />
        ));
      }
      
      loadBlogPosts();
      onDataChange();
      closeModal();
    } catch (error) {
      toast(() => (
        <ErrorToast
          message="Failed to save blog post"
          icon={<X className="w-5 h-5 text-red-400" />}
        />
      ));
    }
  };

  const handleDelete = (id: string, title: string) => {
    if (window.confirm(`Are you sure you want to delete "${title}"?`)) {
      try {
        cmsService.deleteBlogPost(id);
        loadBlogPosts();
        onDataChange();
        toast(() => (
          <SuccessToast
            message="Blog post deleted successfully!"
            icon={<Trash2 className="w-5 h-5 text-green-400" />}
          />
        ));
      } catch (error) {
        toast(() => (
          <ErrorToast
            message="Failed to delete blog post"
            icon={<X className="w-5 h-5 text-red-400" />}
          />
        ));
      }
    }
  };

  const togglePublish = (post: BlogPost) => {
    try {
      cmsService.updateBlogPost(post.id, { published: !post.published });
      loadBlogPosts();
      onDataChange();
      toast(() => (
        <SuccessToast
          message={`Post ${!post.published ? 'published' : 'unpublished'} successfully!`}
          icon={!post.published ? <Globe className="w-5 h-5 text-green-400" /> : <EyeOff className="w-5 h-5 text-green-400" />}
        />
      ));
    } catch (error) {
      toast(() => (
        <ErrorToast
          message="Failed to update post status"
          icon={<X className="w-5 h-5 text-red-400" />}
        />
      ));
    }
  };

  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData({
        ...formData,
        tags: [...formData.tags, tagInput.trim()]
      });
      setTagInput('');
    }
  };

  const removeTag = (tag: string) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(t => t !== tag)
    });
  };

  const categories = ['Development', 'Design', 'Technology', 'Tutorial', 'Opinion', 'News', 'Career', 'Tools'];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Blog Management</h1>
          <p className="text-gray-400">Create and manage your blog articles</p>
        </div>
        <button
          onClick={() => openModal()}
          className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-xl transition-all duration-200 transform hover:scale-105"
        >
          <Plus className="w-5 h-5" />
          <span>New Article</span>
        </button>
      </div>

      {/* Blog Posts Grid */}
      <div className="grid gap-6">
        {blogPosts.map((post) => (
          <motion.div
            key={post.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 hover:border-purple-500/30 transition-all duration-300"
          >
            <div className="flex items-start justify-between">
              <div className="flex space-x-4 flex-1">
                <img
                  src={post.image}
                  alt={post.title}
                  className="w-20 h-20 object-cover rounded-xl"
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-xl font-bold text-white">{post.title}</h3>
                    {post.featured && (
                      <span className="px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded-full text-xs font-medium flex items-center space-x-1">
                        <Star className="w-3 h-3" />
                        <span>Featured</span>
                      </span>
                    )}
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      post.published 
                        ? 'bg-green-500/20 text-green-400'
                        : 'bg-gray-500/20 text-gray-400'
                    }`}>
                      {post.published ? 'Published' : 'Draft'}
                    </span>
                  </div>
                  <p className="text-gray-400 mb-3 line-clamp-2">{post.excerpt}</p>
                  <div className="flex items-center space-x-4 text-sm text-gray-400 mb-3">
                    <div className="flex items-center space-x-1">
                      <Tag className="w-4 h-4" />
                      <span>{post.category}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{post.readTime} min read</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(post.updatedAt)}</span>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {post.tags.slice(0, 4).map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-gray-700/50 text-gray-300 rounded text-xs"
                      >
                        #{tag}
                      </span>
                    ))}
                    {post.tags.length > 4 && (
                      <span className="px-2 py-1 bg-purple-600/20 text-purple-300 rounded text-xs">
                        +{post.tags.length - 4} more
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex space-x-2 ml-4">
                <button
                  onClick={() => togglePublish(post)}
                  className={`p-2 rounded-lg transition-all duration-200 ${
                    post.published
                      ? 'text-green-400 hover:text-green-300 hover:bg-green-500/10'
                      : 'text-gray-400 hover:text-gray-300 hover:bg-gray-500/10'
                  }`}
                  title={post.published ? 'Unpublish' : 'Publish'}
                >
                  {post.published ? <Globe className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                </button>
                <button
                  onClick={() => openModal(post)}
                  className="p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-500/10 rounded-lg transition-all duration-200"
                  title="Edit"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDelete(post.id, post.title)}
                  className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-all duration-200"
                  title="Delete"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {blogPosts.length === 0 && (
        <div className="text-center py-16">
          <BookOpen className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-400 mb-2">No blog posts yet</h3>
          <p className="text-gray-500 mb-6">Create your first article to get started</p>
          <button
            onClick={() => openModal()}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 text-white rounded-xl hover:from-purple-700 hover:to-cyan-700 transition-all duration-200"
          >
            Write Your First Article
          </button>
        </div>
      )}

      {/* Blog Post Modal */}
      <AnimatePresence>
        {showModal && (
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="bg-gradient-to-br from-gray-900/95 to-black/95 backdrop-blur-xl rounded-3xl max-w-5xl w-full max-h-[90vh] overflow-y-auto border border-purple-500/30"
            >
              <div className="p-8">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-3xl font-bold text-white">
                    {editingPost ? 'Edit Article' : 'New Article'}
                  </h2>
                  <button
                    onClick={closeModal}
                    className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    {/* Title */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Article Title *
                      </label>
                      <input
                        type="text"
                        value={formData.title}
                        onChange={(e) => {
                          const title = e.target.value;
                          setFormData({ 
                            ...formData, 
                            title,
                            slug: generateSlug(title)
                          });
                        }}
                        className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                        placeholder="Enter article title"
                        required
                      />
                    </div>

                    {/* Slug */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        URL Slug
                      </label>
                      <input
                        type="text"
                        value={formData.slug}
                        onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
                        className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                        placeholder="url-friendly-slug"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    {/* Category */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Category *
                      </label>
                      <select
                        value={formData.category}
                        onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                        className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                        required
                      >
                        <option value="">Select category</option>
                        {categories.map((cat) => (
                          <option key={cat} value={cat} className="bg-gray-800">
                            {cat}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Author */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Author
                      </label>
                      <input
                        type="text"
                        value={formData.author}
                        onChange={(e) => setFormData({ ...formData, author: e.target.value })}
                        className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                        placeholder="Author name"
                      />
                    </div>
                  </div>

                  {/* Image URL */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Featured Image URL
                    </label>
                    <input
                      type="url"
                      value={formData.image}
                      onChange={(e) => setFormData({ ...formData, image: e.target.value })}
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                      placeholder="https://example.com/image.jpg"
                    />
                  </div>

                  {/* Excerpt */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Excerpt *
                    </label>
                    <textarea
                      value={formData.excerpt}
                      onChange={(e) => setFormData({ ...formData, excerpt: e.target.value })}
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                      placeholder="Brief description of the article"
                      rows={3}
                      required
                    />
                  </div>

                  {/* Content */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Content * (Markdown supported)
                    </label>
                    <textarea
                      value={formData.content}
                      onChange={(e) => {
                        const content = e.target.value;
                        setFormData({ 
                          ...formData, 
                          content,
                          readTime: calculateReadTime(content)
                        });
                      }}
                      className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                      placeholder="Write your article content here..."
                      rows={12}
                      required
                    />
                    <p className="text-sm text-gray-400 mt-2">
                      Estimated read time: {formData.readTime} minutes
                    </p>
                  </div>

                  {/* Tags */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Tags
                    </label>
                    <div className="flex space-x-2 mb-3">
                      <input
                        type="text"
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                        className="flex-1 px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20"
                        placeholder="Add tag (press Enter)"
                      />
                      <button
                        type="button"
                        onClick={addTag}
                        className="px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-xl transition-all duration-200"
                      >
                        Add
                      </button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {formData.tags.map((tag) => (
                        <span
                          key={tag}
                          className="px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm flex items-center space-x-2 border border-purple-500/30"
                        >
                          <span>#{tag}</span>
                          <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="text-purple-400 hover:text-red-400 transition-colors duration-200"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Options */}
                  <div className="flex items-center space-x-6">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="published"
                        checked={formData.published}
                        onChange={(e) => setFormData({ ...formData, published: e.target.checked })}
                        className="w-5 h-5 text-purple-600 bg-white/5 border-white/10 rounded focus:ring-purple-500/20 focus:ring-2"
                      />
                      <label htmlFor="published" className="text-gray-300 font-medium">
                        Publish Article
                      </label>
                    </div>

                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="featured"
                        checked={formData.featured}
                        onChange={(e) => setFormData({ ...formData, featured: e.target.checked })}
                        className="w-5 h-5 text-purple-600 bg-white/5 border-white/10 rounded focus:ring-purple-500/20 focus:ring-2"
                      />
                      <label htmlFor="featured" className="text-gray-300 font-medium">
                        Mark as Featured
                      </label>
                    </div>
                  </div>

                  {/* Submit Buttons */}
                  <div className="flex space-x-4 pt-6">
                    <button
                      type="submit"
                      className="flex-1 px-6 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105"
                    >
                      {editingPost ? 'Update Article' : 'Create Article'}
                    </button>
                    <button
                      type="button"
                      onClick={closeModal}
                      className="px-6 py-3 bg-white/10 hover:bg-white/20 text-white rounded-xl font-medium transition-all duration-200"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default BlogManager;
