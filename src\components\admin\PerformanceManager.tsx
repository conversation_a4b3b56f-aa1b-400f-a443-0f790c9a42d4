import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Zap, 
  Image, 
  Globe, 
  Code, 
  BarChart3, 
  Settings, 
  RefreshCw,
  TrendingUp,
  Clock,
  HardDrive,
  Wifi,
  Monitor
} from 'lucide-react';
import toast from 'react-hot-toast';
import { SuccessToast, ErrorToast, InfoToast } from '../CustomToast';
import { imageOptimizationService } from '../../services/imageOptimizationService';
import { codeSplittingService } from '../../services/codeSplittingService';
import { cdnService } from '../../services/cdnService';

const PerformanceManager: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'images' | 'code-splitting' | 'cdn' | 'caching'>('overview');
  const [imageMetrics, setImageMetrics] = useState(imageOptimizationService.getMetrics());
  const [chunkMetrics, setChunkMetrics] = useState(codeSplittingService.getChunkMetrics());
  const [cdnMetrics, setCdnMetrics] = useState(cdnService.getMetrics());
  const [performanceScore, setPerformanceScore] = useState(0);
  const [webVitals, setWebVitals] = useState({
    lcp: 0, // Largest Contentful Paint
    fid: 0, // First Input Delay
    cls: 0, // Cumulative Layout Shift
    fcp: 0, // First Contentful Paint
    ttfb: 0 // Time to First Byte
  });

  useEffect(() => {
    loadPerformanceData();
    measureWebVitals();
    const interval = setInterval(loadPerformanceData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadPerformanceData = () => {
    setImageMetrics(imageOptimizationService.getMetrics());
    setChunkMetrics(codeSplittingService.getChunkMetrics());
    setCdnMetrics(cdnService.getMetrics());
    calculatePerformanceScore();
  };

  const measureWebVitals = () => {
    // Measure Core Web Vitals
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        setWebVitals(prev => ({ ...prev, lcp: lastEntry.startTime }));
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          setWebVitals(prev => ({ ...prev, fid: entry.processingStart - entry.startTime }));
        });
      }).observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift
      new PerformanceObserver((list) => {
        let clsValue = 0;
        list.getEntries().forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        setWebVitals(prev => ({ ...prev, cls: clsValue }));
      }).observe({ entryTypes: ['layout-shift'] });
    }

    // First Contentful Paint and TTFB
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      setWebVitals(prev => ({
        ...prev,
        fcp: navigation.responseStart - navigation.fetchStart,
        ttfb: navigation.responseStart - navigation.requestStart
      }));
    }
  };

  const calculatePerformanceScore = () => {
    let score = 100;

    // Image optimization score (30%)
    const imageOptimizationRate = imageMetrics.totalImages > 0 
      ? (imageMetrics.optimizedImages / imageMetrics.totalImages) * 100 
      : 100;
    score -= (100 - imageOptimizationRate) * 0.3;

    // Code splitting score (25%)
    const codeSplittingRate = chunkMetrics.totalChunks > 0 
      ? (chunkMetrics.loadedChunks / chunkMetrics.totalChunks) * 100 
      : 100;
    score -= (100 - codeSplittingRate) * 0.25;

    // CDN performance score (25%)
    const cdnPerformanceScore = cdnMetrics.avgResponseTime < 1000 ? 100 : 
      Math.max(0, 100 - ((cdnMetrics.avgResponseTime - 1000) / 50));
    score -= (100 - cdnPerformanceScore) * 0.25;

    // Cache hit rate score (20%)
    const cacheScore = cdnMetrics.cacheHitRate;
    score -= (100 - cacheScore) * 0.2;

    setPerformanceScore(Math.max(0, Math.round(score)));
  };

  const getPerformanceGrade = (score: number) => {
    if (score >= 90) return { grade: 'A+', color: 'text-green-400' };
    if (score >= 80) return { grade: 'A', color: 'text-green-400' };
    if (score >= 70) return { grade: 'B', color: 'text-yellow-400' };
    if (score >= 60) return { grade: 'C', color: 'text-orange-400' };
    if (score >= 50) return { grade: 'D', color: 'text-red-400' };
    return { grade: 'F', color: 'text-red-500' };
  };

  const getWebVitalScore = (metric: string, value: number) => {
    switch (metric) {
      case 'lcp':
        if (value <= 2500) return 'good';
        if (value <= 4000) return 'needs-improvement';
        return 'poor';
      case 'fid':
        if (value <= 100) return 'good';
        if (value <= 300) return 'needs-improvement';
        return 'poor';
      case 'cls':
        if (value <= 0.1) return 'good';
        if (value <= 0.25) return 'needs-improvement';
        return 'poor';
      case 'fcp':
        if (value <= 1800) return 'good';
        if (value <= 3000) return 'needs-improvement';
        return 'poor';
      case 'ttfb':
        if (value <= 800) return 'good';
        if (value <= 1800) return 'needs-improvement';
        return 'poor';
      default:
        return 'good';
    }
  };

  const optimizeImages = async () => {
    try {
      // Reset image metrics and trigger optimization
      imageOptimizationService.resetMetrics();
      
      toast(() => (
        <SuccessToast
          message="Image optimization initiated!"
          icon={<Image className="w-5 h-5 text-green-400" />}
        />
      ));
      
      loadPerformanceData();
    } catch (error) {
      toast(() => (
        <ErrorToast
          message="Image optimization failed"
          icon={<Image className="w-5 h-5 text-red-400" />}
        />
      ));
    }
  };

  const clearCodeSplittingCache = () => {
    codeSplittingService.clearCache();
    
    toast(() => (
      <InfoToast
        message="Code splitting cache cleared"
        icon={<Code className="w-5 h-5 text-blue-400" />}
      />
    ));
    
    loadPerformanceData();
  };

  const resetCDNMetrics = () => {
    cdnService.resetMetrics();
    
    toast(() => (
      <InfoToast
        message="CDN metrics reset"
        icon={<Globe className="w-5 h-5 text-blue-400" />}
      />
    ));
    
    loadPerformanceData();
  };

  const performanceGradeInfo = getPerformanceGrade(performanceScore);

  const tabs = [
    { id: 'overview', label: 'Performance Overview', icon: BarChart3 },
    { id: 'images', label: 'Image Optimization', icon: Image },
    { id: 'code-splitting', label: 'Code Splitting', icon: Code },
    { id: 'cdn', label: 'CDN & Caching', icon: Globe },
    { id: 'caching', label: 'Service Worker', icon: HardDrive }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Performance Center</h2>
          <p className="text-gray-400">Advanced performance optimization and monitoring</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <div className={`text-3xl font-bold ${performanceGradeInfo.color}`}>
              {performanceGradeInfo.grade}
            </div>
            <div className="text-sm text-gray-400">Performance Grade</div>
          </div>
          <button
            onClick={loadPerformanceData}
            className="px-4 py-2 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white rounded-xl transition-all duration-200 flex items-center space-x-2"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Performance Score Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-green-900/50 to-green-800/30 border border-green-500/30 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-300 text-sm font-medium">Performance Score</p>
              <p className="text-2xl font-bold text-white">{performanceScore}/100</p>
            </div>
            <Zap className="w-8 h-8 text-green-400" />
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-900/50 to-blue-800/30 border border-blue-500/30 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-300 text-sm font-medium">Images Optimized</p>
              <p className="text-2xl font-bold text-white">{imageMetrics.optimizedImages}/{imageMetrics.totalImages}</p>
            </div>
            <Image className="w-8 h-8 text-blue-400" />
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-900/50 to-purple-800/30 border border-purple-500/30 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-300 text-sm font-medium">Chunks Loaded</p>
              <p className="text-2xl font-bold text-white">{chunkMetrics.loadedChunks}/{chunkMetrics.totalChunks}</p>
            </div>
            <Code className="w-8 h-8 text-purple-400" />
          </div>
        </div>

        <div className="bg-gradient-to-br from-orange-900/50 to-orange-800/30 border border-orange-500/30 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-300 text-sm font-medium">Cache Hit Rate</p>
              <p className="text-2xl font-bold text-white">{cdnMetrics.cacheHitRate.toFixed(1)}%</p>
            </div>
            <Globe className="w-8 h-8 text-orange-400" />
          </div>
        </div>
      </div>

      {/* Web Vitals */}
      <div className="bg-gray-800/50 rounded-2xl border border-gray-700/50 p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Core Web Vitals</h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {Object.entries(webVitals).map(([metric, value]) => {
            const score = getWebVitalScore(metric, value);
            const scoreColor = score === 'good' ? 'text-green-400' : 
                              score === 'needs-improvement' ? 'text-yellow-400' : 'text-red-400';
            
            return (
              <div key={metric} className="text-center">
                <div className={`text-2xl font-bold ${scoreColor}`}>
                  {metric === 'cls' ? value.toFixed(3) : Math.round(value)}
                  {metric !== 'cls' && 'ms'}
                </div>
                <div className="text-sm text-gray-400 uppercase">{metric}</div>
                <div className={`text-xs ${scoreColor} capitalize`}>{score.replace('-', ' ')}</div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-800/50 rounded-xl p-1">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-purple-600 text-white shadow-lg'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="bg-gray-800/50 rounded-2xl border border-gray-700/50 p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">Performance Overview</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-white">Optimization Status</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Image Optimization:</span>
                    <span className="text-green-400">{imageMetrics.formatSupport.webp ? 'WebP' : 'Standard'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Code Splitting:</span>
                    <span className="text-green-400">Active</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">CDN:</span>
                    <span className="text-green-400">{cdnService.getConfig().primaryProvider}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Service Worker:</span>
                    <span className="text-green-400">Active</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-white">Performance Metrics</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Avg Load Time:</span>
                    <span className="text-white">{cdnMetrics.avgResponseTime.toFixed(0)}ms</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Cache Efficiency:</span>
                    <span className="text-white">{cdnMetrics.cacheHitRate.toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Bandwidth Saved:</span>
                    <span className="text-white">{(cdnMetrics.bandwidthSaved / 1024 / 1024).toFixed(1)}MB</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Error Rate:</span>
                    <span className="text-white">{cdnMetrics.errorRate.toFixed(2)}%</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex space-x-4">
              <button
                onClick={optimizeImages}
                className="px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl transition-all duration-200"
              >
                Optimize Images
              </button>
              <button
                onClick={clearCodeSplittingCache}
                className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-xl transition-all duration-200"
              >
                Clear Code Cache
              </button>
              <button
                onClick={resetCDNMetrics}
                className="px-4 py-2 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white rounded-xl transition-all duration-200"
              >
                Reset CDN Metrics
              </button>
            </div>
          </div>
        )}

        {activeTab === 'images' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">Image Optimization</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-700/50 rounded-xl p-4">
                <h4 className="font-semibold text-white mb-3">Format Support</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">WebP:</span>
                    <span className={imageMetrics.formatSupport.webp ? 'text-green-400' : 'text-red-400'}>
                      {imageMetrics.formatSupport.webp ? 'Supported' : 'Not Supported'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">AVIF:</span>
                    <span className={imageMetrics.formatSupport.avif ? 'text-green-400' : 'text-red-400'}>
                      {imageMetrics.formatSupport.avif ? 'Supported' : 'Not Supported'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-700/50 rounded-xl p-4">
                <h4 className="font-semibold text-white mb-3">Optimization Stats</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Total Images:</span>
                    <span className="text-white">{imageMetrics.totalImages}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Optimized:</span>
                    <span className="text-white">{imageMetrics.optimizedImages}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Bytes Saved:</span>
                    <span className="text-white">{((imageMetrics.bytesOriginal - imageMetrics.bytesOptimized) / 1024).toFixed(1)}KB</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-700/50 rounded-xl p-4">
                <h4 className="font-semibold text-white mb-3">Performance</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Avg Load Time:</span>
                    <span className="text-white">{imageMetrics.loadingTimes.length > 0 ? 
                      (imageMetrics.loadingTimes.reduce((a, b) => a + b, 0) / imageMetrics.loadingTimes.length).toFixed(0) : 0}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Lazy Loading:</span>
                    <span className="text-green-400">Active</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'code-splitting' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">Code Splitting</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-700/50 rounded-xl p-4">
                <h4 className="font-semibold text-white mb-3">Chunk Statistics</h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Total Chunks:</span>
                    <span className="text-white">{chunkMetrics.totalChunks}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Loaded:</span>
                    <span className="text-white">{chunkMetrics.loadedChunks}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Failed:</span>
                    <span className="text-white">{chunkMetrics.failedChunks}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Cache Hit Rate:</span>
                    <span className="text-white">{chunkMetrics.cacheHitRate.toFixed(1)}%</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-700/50 rounded-xl p-4">
                <h4 className="font-semibold text-white mb-3">Performance</h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Total Size:</span>
                    <span className="text-white">{(chunkMetrics.totalSize / 1024).toFixed(1)}KB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Loaded Size:</span>
                    <span className="text-white">{(chunkMetrics.loadedSize / 1024).toFixed(1)}KB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Avg Load Time:</span>
                    <span className="text-white">{chunkMetrics.avgLoadTime.toFixed(0)}ms</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'cdn' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">CDN & Global Delivery</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-white">CDN Configuration</h4>
                <div className="bg-gray-700/50 rounded-xl p-4">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-300">Primary Provider:</span>
                      <span className="text-white capitalize">{cdnService.getConfig().primaryProvider}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Auto Failover:</span>
                      <span className="text-green-400">{cdnService.getConfig().autoFailover ? 'Enabled' : 'Disabled'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Geo Optimization:</span>
                      <span className="text-green-400">{cdnService.getConfig().geoOptimization ? 'Enabled' : 'Disabled'}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-white">Performance Metrics</h4>
                <div className="bg-gray-700/50 rounded-xl p-4">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-300">Total Requests:</span>
                      <span className="text-white">{cdnMetrics.totalRequests}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Avg Response Time:</span>
                      <span className="text-white">{cdnMetrics.avgResponseTime.toFixed(0)}ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Error Rate:</span>
                      <span className="text-white">{cdnMetrics.errorRate.toFixed(2)}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'caching' && (
          <div className="space-y-6">
            <h3 className="text-xl font-semibold text-white">Service Worker & Caching</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-white">Cache Status</h4>
                <div className="bg-gray-700/50 rounded-xl p-4">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-300">Service Worker:</span>
                      <span className="text-green-400">Active</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Static Cache:</span>
                      <span className="text-green-400">Enabled</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Dynamic Cache:</span>
                      <span className="text-green-400">Enabled</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">Image Cache:</span>
                      <span className="text-green-400">Enabled</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-white">Cache Strategies</h4>
                <div className="bg-gray-700/50 rounded-xl p-4">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-300">Static Assets:</span>
                      <span className="text-white">Cache First</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">API Calls:</span>
                      <span className="text-white">Network First</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">CDN Resources:</span>
                      <span className="text-white">Stale While Revalidate</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceManager;
