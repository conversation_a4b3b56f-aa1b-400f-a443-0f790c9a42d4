import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOData {
  meta: {
    title: string;
    description: string;
    keywords: string[];
    author: string;
    robots: string;
    canonical: string;
    language: string;
    viewport: string;
  };
  openGraph: {
    title: string;
    description: string;
    image: string;
    url: string;
    type: string;
    siteName: string;
    locale: string;
  };
  twitter: {
    card: string;
    site: string;
    creator: string;
    title: string;
    description: string;
    image: string;
  };
  structuredData: {
    person: {
      name: string;
      jobTitle: string;
      url: string;
      sameAs: string[];
      worksFor: string;
      email: string;
      telephone: string;
      address: {
        addressLocality: string;
        addressCountry: string;
      };
    };
    website: {
      name: string;
      url: string;
      description: string;
      inLanguage: string;
    };
  };
  analytics: {
    googleAnalyticsId: string;
    googleSearchConsole: string;
    bingWebmasterTools: string;
    yandexWebmaster: string;
  };
  localSEO: {
    businessName: string;
    businessType: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    phone: string;
    email: string;
    website: string;
    hours: string;
    priceRange: string;
  };
}

const SEOHead: React.FC = () => {
  const [seoData, setSeoData] = useState<SEOData | null>(null);

  useEffect(() => {
    // Load SEO data from localStorage
    const loadSEOData = () => {
      const saved = localStorage.getItem('seo_data');
      if (saved) {
        try {
          const parsed = JSON.parse(saved);
          setSeoData(parsed);
        } catch (error) {
          console.error('Error loading SEO data:', error);
        }
      }
    };

    loadSEOData();

    // Listen for SEO data changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'seo_data') {
        loadSEOData();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  if (!seoData) {
    // Default SEO data if none is set
    return (
      <Helmet>
        <title>Nural Bhardwaj - Full Stack Developer & UI/UX Designer</title>
        <meta name="description" content="Experienced Full Stack Developer & UI/UX Designer specializing in React, Node.js, and modern web technologies. View my portfolio and get in touch." />
        <meta name="keywords" content="Full Stack Developer, UI/UX Designer, React, Node.js, JavaScript, TypeScript, Portfolio" />
        <meta name="author" content="Nural Bhardwaj" />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://nuralbhardwaj.me" />
      </Helmet>
    );
  }

  // Generate structured data JSON-LD
  const personStructuredData = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": seoData.structuredData.person.name,
    "jobTitle": seoData.structuredData.person.jobTitle,
    "url": seoData.structuredData.person.url,
    "sameAs": seoData.structuredData.person.sameAs,
    "worksFor": {
      "@type": "Organization",
      "name": seoData.structuredData.person.worksFor
    },
    "email": seoData.structuredData.person.email,
    "telephone": seoData.structuredData.person.telephone,
    "address": {
      "@type": "PostalAddress",
      "addressLocality": seoData.structuredData.person.address.addressLocality,
      "addressCountry": seoData.structuredData.person.address.addressCountry
    }
  };

  const websiteStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": seoData.structuredData.website.name,
    "url": seoData.structuredData.website.url,
    "description": seoData.structuredData.website.description,
    "inLanguage": seoData.structuredData.website.inLanguage,
    "author": {
      "@type": "Person",
      "name": seoData.structuredData.person.name
    }
  };

  const localBusinessStructuredData = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": seoData.localSEO.businessName,
    "description": seoData.meta.description,
    "url": seoData.localSEO.website,
    "telephone": seoData.localSEO.phone,
    "email": seoData.localSEO.email,
    "priceRange": seoData.localSEO.priceRange,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": seoData.localSEO.address,
      "addressLocality": seoData.localSEO.city,
      "addressRegion": seoData.localSEO.state,
      "postalCode": seoData.localSEO.zipCode,
      "addressCountry": seoData.localSEO.country
    },
    "openingHours": seoData.localSEO.hours,
    "serviceType": seoData.localSEO.businessType
  };

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{seoData.meta.title}</title>
      <meta name="description" content={seoData.meta.description} />
      <meta name="keywords" content={seoData.meta.keywords.join(', ')} />
      <meta name="author" content={seoData.meta.author} />
      <meta name="robots" content={seoData.meta.robots} />
      <meta name="viewport" content={seoData.meta.viewport} />
      <meta httpEquiv="Content-Language" content={seoData.meta.language} />
      <link rel="canonical" href={seoData.meta.canonical} />

      {/* Open Graph Tags */}
      <meta property="og:title" content={seoData.openGraph.title} />
      <meta property="og:description" content={seoData.openGraph.description} />
      <meta property="og:image" content={seoData.openGraph.image} />
      <meta property="og:url" content={seoData.openGraph.url} />
      <meta property="og:type" content={seoData.openGraph.type} />
      <meta property="og:site_name" content={seoData.openGraph.siteName} />
      <meta property="og:locale" content={seoData.openGraph.locale} />

      {/* Twitter Card Tags */}
      <meta name="twitter:card" content={seoData.twitter.card} />
      <meta name="twitter:site" content={seoData.twitter.site} />
      <meta name="twitter:creator" content={seoData.twitter.creator} />
      <meta name="twitter:title" content={seoData.twitter.title} />
      <meta name="twitter:description" content={seoData.twitter.description} />
      <meta name="twitter:image" content={seoData.twitter.image} />

      {/* Additional SEO Meta Tags */}
      <meta name="theme-color" content="#8B5CF6" />
      <meta name="msapplication-TileColor" content="#8B5CF6" />
      <meta name="application-name" content={seoData.openGraph.siteName} />
      <meta name="apple-mobile-web-app-title" content={seoData.openGraph.siteName} />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />

      {/* Verification Tags */}
      {seoData.analytics.googleSearchConsole && (
        <meta name="google-site-verification" content={seoData.analytics.googleSearchConsole} />
      )}
      {seoData.analytics.bingWebmasterTools && (
        <meta name="msvalidate.01" content={seoData.analytics.bingWebmasterTools} />
      )}
      {seoData.analytics.yandexWebmaster && (
        <meta name="yandex-verification" content={seoData.analytics.yandexWebmaster} />
      )}

      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(personStructuredData)}
      </script>
      <script type="application/ld+json">
        {JSON.stringify(websiteStructuredData)}
      </script>
      <script type="application/ld+json">
        {JSON.stringify(localBusinessStructuredData)}
      </script>

      {/* Google Analytics */}
      {seoData.analytics.googleAnalyticsId && (
        <>
          <script async src={`https://www.googletagmanager.com/gtag/js?id=${seoData.analytics.googleAnalyticsId}`} />
          <script>
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${seoData.analytics.googleAnalyticsId}', {
                page_title: '${seoData.meta.title}',
                page_location: '${seoData.meta.canonical}',
                send_page_view: true
              });
            `}
          </script>
        </>
      )}

      {/* Preconnect to external domains for performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://www.google-analytics.com" />
      <link rel="preconnect" href="https://www.googletagmanager.com" />

      {/* DNS Prefetch for better performance */}
      <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
      <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
      <link rel="dns-prefetch" href="https://www.google-analytics.com" />

      {/* Favicon and App Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />

      {/* Additional Performance Hints */}
      
      {/* Security Headers */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
      <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
      
      {/* Rich Snippets for Better SERP Display */}
      <meta name="rating" content="5" />
      <meta name="coverage" content="Worldwide" />
      <meta name="distribution" content="Global" />
      <meta name="target" content="all" />
      <meta name="HandheldFriendly" content="True" />
      <meta name="MobileOptimized" content="320" />
    </Helmet>
  );
};

export default SEOHead;
