import { motion } from 'framer-motion';
import React, { useEffect, useRef, useState } from 'react';

interface SnowflakeParticleSystemProps {
  count?: number;
  className?: string;
  showTrails?: boolean;
}

// Mobile detection hook
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return isMobile;
};

interface SnowflakeParticle {
  id: number;
  x: number;
  y: number;
  direction: number;
  speed: number;
  amplitude: number;
  delay: number;
  size: number;
  rotationSpeed: number;
  fallSpeed: number;
}

const SnowflakeParticleSystem: React.FC<SnowflakeParticleSystemProps> = ({
  count = 12,
  className = '',
  showTrails = true,
}) => {
  const [snowflakes, setSnowflakes] = useState<SnowflakeParticle[]>([]);
  const [footerOffset, setFooterOffset] = useState<number>(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();

  // Reduce count for better performance
  const optimizedCount = isMobile ? 0 : Math.min(count, 15); // Reduced from 23 to 15
  const optimizedShowTrails = isMobile ? false : showTrails;

  // Track footer position for fade-out effect (throttled for mobile performance)
  useEffect(() => {
    let ticking = false;

    const updateFooterPosition = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const footer = document.querySelector('footer');
          if (footer) {
            const footerRect = footer.getBoundingClientRect();
            setFooterOffset(footerRect.top);
          }
          ticking = false;
        });
        ticking = true;
      }
    };

    updateFooterPosition();

    // Use passive listeners and throttle on mobile
    const options = { passive: true };
    window.addEventListener('scroll', updateFooterPosition, options);
    window.addEventListener('resize', updateFooterPosition, options);

    return () => {
      window.removeEventListener('scroll', updateFooterPosition);
      window.removeEventListener('resize', updateFooterPosition);
    };
  }, []);

  // Initialize snowflakes for STRAIGHT DOWN falling like rain
  useEffect(() => {
    const initialSnowflakes: SnowflakeParticle[] = Array.from({ length: optimizedCount }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: -50 - (Math.random() * 200), // Start above screen, staggered
      delay: i * 0.2,
      size: 0.5 + Math.random() * 0.6, // Optimized size
      speed: 0, // NO horizontal speed - rain falls straight
      direction: 0, // NO direction - straight down only
      amplitude: 0, // NO oscillation - pure vertical
      rotationSpeed: 0.5 + Math.random() * 1.5, // Keep rotation for visual effect
      fallSpeed: 1.5 + Math.random() * 2.0, // Fast straight falling like rain
    }));
    setSnowflakes(initialSnowflakes);
  }, [optimizedCount]);

  // Independent snowflake movement
  useEffect(() => {
    const interval = setInterval(() => {
      setSnowflakes(prevSnowflakes =>
        prevSnowflakes.map(snowflake => {
          // PURE RAIN-LIKE FALLING - ABSOLUTELY STRAIGHT DOWN
          let newX = snowflake.x; // ZERO horizontal movement
          let newY = snowflake.y + (snowflake.fallSpeed * 2); // Faster straight downward movement

          // Only reset when snowflake goes off bottom of screen
          if (newY > window.innerHeight + 50) {
            // Reset to top when snowflake goes off screen
            newY = -50 - Math.random() * 100;
            newX = Math.random() * window.innerWidth; // New random X position only when resetting
          }
          
          return {
            ...snowflake,
            x: newX,
            y: newY,
          };
        })
      );
    }, isMobile ? 100 : 50); // Further optimized FPS for better performance

    return () => clearInterval(interval);
  }, [isMobile]);

  // Don't render anything on mobile (after all hooks)
  if (isMobile) {
    return null;
  }

  return (
    <div ref={containerRef} className={`fixed inset-0 pointer-events-none z-40 ${className}`}>
      {snowflakes.map((snowflake) => (
        <SnowflakeComponent
          key={snowflake.id}
          snowflake={snowflake}
          showTrail={optimizedShowTrails}
          footerOffset={footerOffset}
          isMobile={isMobile}
        />
      ))}
    </div>
  );
};

interface SnowflakeComponentProps {
  snowflake: SnowflakeParticle;
  showTrail: boolean;
  footerOffset: number;
  isMobile: boolean;
}

const SnowflakeComponent: React.FC<SnowflakeComponentProps> = ({ snowflake, showTrail, footerOffset, isMobile }) => {
  // Calculate opacity based on proximity to footer
  const calculateOpacity = () => {
    const snowflakeY = snowflake.y;
    const fadeZone = 200; // Start fading 200px before footer
    const fadeStart = footerOffset - fadeZone;

    if (snowflakeY < fadeStart) return 1;
    if (snowflakeY > footerOffset) return 0;

    return Math.max(0, (footerOffset - snowflakeY) / fadeZone);
  };
  return (
    <motion.div
      className="absolute"
      style={{
        left: snowflake.x - 10,
        top: snowflake.y - 10,
        scale: snowflake.size,
        opacity: calculateOpacity(),
      }}
      animate={{
        rotate: [0, 360],
      }}
      transition={{
        duration: 3 / snowflake.rotationSpeed, // Much faster rotation
        repeat: Infinity,
        ease: "linear",
      }}
    >
      {/* Enhanced Trail Effect */}
      {showTrail && (
        <motion.div
          className="absolute inset-0"
          animate={{
            scale: [1, 2.2, 0.8, 1.5, 1], // More dynamic trail scaling
            opacity: [0.35, 0.05, 0.4, 0.1, 0.35], // More visible trail
            rotate: [0, 180, 360], // Add rotation to trail
          }}
          transition={{
            duration: 2, // Faster trail animation
            repeat: Infinity,
            ease: "easeInOut",
            delay: snowflake.delay,
          }}
        >
          <div className="w-8 h-8 bg-gradient-to-r from-blue-300/40 via-white/35 to-cyan-300/40 rounded-full blur-lg shadow-lg shadow-blue-200/30"></div>
        </motion.div>
      )}

      {/* New Sparkle Effect */}
      {showTrail && (
        <motion.div
          className="absolute inset-0"
          animate={{
            scale: [0.5, 1.8, 0.3, 1.2, 0.5],
            opacity: [0.8, 0.1, 0.9, 0.2, 0.8],
            rotate: [0, 90, 180, 270, 360],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: snowflake.delay * 0.5,
          }}
        >
          <div className="w-3 h-3 bg-gradient-to-r from-white/60 via-cyan-200/50 to-blue-200/60 rounded-full blur-sm animate-pulse"></div>
        </motion.div>
      )}

      {/* Crystalline sparkle trail */}
      {showTrail && (
        <motion.div
          className="absolute inset-0"
          animate={{
            rotate: [0, 360],
            scale: [0.6, 1.4, 0.6],
            opacity: [0.15, 0.02, 0.15],
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "linear",
            delay: snowflake.delay * 0.7,
          }}
        >
          <div className="w-8 h-8 bg-gradient-to-r from-white/12 via-blue-100/18 to-cyan-100/12 rounded-full blur-lg"></div>
        </motion.div>
      )}

      {/* Ice crystal shimmer */}
      {showTrail && (
        <motion.div
          className="absolute inset-0"
          animate={{
            scale: [0.8, 1.3, 0.8],
            opacity: [0.1, 0.4, 0.1],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
            delay: snowflake.delay * 1.2,
          }}
        >
          <div className="w-5 h-5 bg-gradient-to-r from-cyan-100/15 via-white/25 to-blue-100/15 rounded-full blur-sm"></div>
        </motion.div>
      )}

      {/* Enhanced Beautiful Snowflake */}
      <motion.div
        className="relative w-5 h-5"
        animate={isMobile ? {} : {
          rotate: [0, 360], // Only gentle rotation, no other movement
        }}
        transition={isMobile ? {} : {
          duration: 8 + snowflake.delay * 0.5, // Slow gentle rotation
          repeat: Infinity,
          ease: "linear",
          delay: snowflake.delay,
        }}
      >
        {/* Enhanced Snowflake Design */}
        <div className="relative w-full h-full">
          {isMobile ? (
            // Simple mobile snowflake
            <div className="w-full h-full">
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-1 bg-white rounded-full"></div>
              <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-white to-transparent transform -translate-y-1/2"></div>
              <div className="absolute top-0 bottom-0 left-1/2 w-0.5 bg-gradient-to-b from-transparent via-white to-transparent transform -translate-x-1/2"></div>
              <div className="absolute inset-0 transform rotate-45">
                <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-white/70 to-transparent transform -translate-y-1/2"></div>
                <div className="absolute top-0 bottom-0 left-1/2 w-0.5 bg-gradient-to-b from-transparent via-white/70 to-transparent transform -translate-x-1/2"></div>
              </div>
            </div>
          ) : (
            // Full desktop snowflake design
            <>
          {/* Crystalline Center Core */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 bg-gradient-to-br from-white via-blue-50 to-cyan-100 rounded-full shadow-lg border border-white/80">
            <div className="absolute inset-0.5 bg-gradient-to-br from-white/60 to-transparent rounded-full"></div>
          </div>

          {/* Enhanced Main Cross Arms with thickness variation */}
          <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-white to-transparent transform -translate-y-1/2 shadow-md"
               style={{ filter: 'drop-shadow(0 0 2px rgba(255,255,255,0.8))' }}></div>
          <div className="absolute top-0 bottom-0 left-1/2 w-0.5 bg-gradient-to-b from-transparent via-white to-transparent transform -translate-x-1/2 shadow-md"
               style={{ filter: 'drop-shadow(0 0 2px rgba(255,255,255,0.8))' }}></div>

          {/* Enhanced Diagonal Arms with crystal structure */}
          <div className="absolute inset-0 transform rotate-45">
            <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-white/90 to-transparent transform -translate-y-1/2 shadow-md"></div>
            <div className="absolute top-0 bottom-0 left-1/2 w-0.5 bg-gradient-to-b from-transparent via-white/90 to-transparent transform -translate-x-1/2 shadow-md"></div>
          </div>

          {/* Secondary diagonal arms */}
          <div className="absolute inset-0 transform -rotate-45">
            <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-white/90 to-transparent transform -translate-y-1/2 shadow-md"></div>
            <div className="absolute top-0 bottom-0 left-1/2 w-0.5 bg-gradient-to-b from-transparent via-white/90 to-transparent transform -translate-x-1/2 shadow-md"></div>
          </div>

          {/* Enhanced Decorative End Points with crystal tips */}
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-gradient-to-br from-white via-cyan-50 to-blue-100 rounded-full shadow-md border border-white/70">
            <div className="absolute inset-0.5 bg-white/40 rounded-full"></div>
          </div>
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-gradient-to-br from-white via-cyan-50 to-blue-100 rounded-full shadow-md border border-white/70">
            <div className="absolute inset-0.5 bg-white/40 rounded-full"></div>
          </div>
          <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-1 bg-gradient-to-br from-white via-cyan-50 to-blue-100 rounded-full shadow-md border border-white/70">
            <div className="absolute inset-0.5 bg-white/40 rounded-full"></div>
          </div>
          <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-1 bg-gradient-to-br from-white via-cyan-50 to-blue-100 rounded-full shadow-md border border-white/70">
            <div className="absolute inset-0.5 bg-white/40 rounded-full"></div>
          </div>

          {/* Intricate branch details */}
          <div className="absolute top-1 left-1/2 transform -translate-x-1/2 w-0.5 h-1 bg-white/70 rounded-full"></div>
          <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-0.5 h-1 bg-white/70 rounded-full"></div>
          <div className="absolute left-1 top-1/2 transform -translate-y-1/2 w-1 h-0.5 bg-white/70 rounded-full"></div>
          <div className="absolute right-1 top-1/2 transform -translate-y-1/2 w-1 h-0.5 bg-white/70 rounded-full"></div>

          {/* Corner crystal formations */}
          <div className="absolute top-1 left-1 w-0.5 h-0.5 bg-white/60 rounded-full shadow-sm"></div>
          <div className="absolute top-1 right-1 w-0.5 h-0.5 bg-white/60 rounded-full shadow-sm"></div>
          <div className="absolute bottom-1 left-1 w-0.5 h-0.5 bg-white/60 rounded-full shadow-sm"></div>
          <div className="absolute bottom-1 right-1 w-0.5 h-0.5 bg-white/60 rounded-full shadow-sm"></div>

          {/* Enhanced crystalline glow effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-blue-50/30 to-white/20 rounded-full blur-sm"></div>
          <div className="absolute inset-0.5 bg-gradient-to-r from-cyan-100/15 via-white/25 to-blue-100/15 rounded-full blur-sm"></div>

          {/* Subtle ice crystal texture */}
          <div className="absolute inset-0 opacity-25">
            <div className="absolute top-1.5 left-1.5 w-0.5 h-0.5 bg-cyan-200 rounded-full"></div>
            <div className="absolute top-1.5 right-1.5 w-0.5 h-0.5 bg-blue-200 rounded-full"></div>
            <div className="absolute bottom-1.5 left-1.5 w-0.5 h-0.5 bg-cyan-200 rounded-full"></div>
            <div className="absolute bottom-1.5 right-1.5 w-0.5 h-0.5 bg-blue-200 rounded-full"></div>
          </div>
          </>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default SnowflakeParticleSystem;
