import React, { useRef, useEffect } from 'react';
import { motion, useInView, useAnimation, Variants } from 'framer-motion';
import { useAnimation as useAnimationContext } from '../contexts/AnimationContext';

// Improved animation variants with better performance
export const improvedFadeInUp: Variants = {
  hidden: {
    opacity: 0,
    y: 60,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94], // Custom easing for smoother animation
      opacity: { duration: 0.4 },
      y: { duration: 0.6 },
      scale: { duration: 0.6 },
    },
  },
};

export const improvedFadeInLeft: Variants = {
  hidden: {
    opacity: 0,
    x: -80,
    scale: 0.9,
  },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      duration: 0.7,
      ease: [0.25, 0.46, 0.45, 0.94],
      opacity: { duration: 0.4 },
      x: { duration: 0.7 },
      scale: { duration: 0.7 },
    },
  },
};

export const improvedFadeInRight: Variants = {
  hidden: {
    opacity: 0,
    x: 80,
    scale: 0.9,
  },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      duration: 0.7,
      ease: [0.25, 0.46, 0.45, 0.94],
      opacity: { duration: 0.4 },
      x: { duration: 0.7 },
      scale: { duration: 0.7 },
    },
  },
};

export const improvedScaleIn: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    rotateY: -15,
  },
  visible: {
    opacity: 1,
    scale: 1,
    rotateY: 0,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94],
      opacity: { duration: 0.4 },
      scale: { duration: 0.8 },
      rotateY: { duration: 0.8 },
    },
  },
};

export const improvedSlideInUp: Variants = {
  hidden: {
    opacity: 0,
    y: 100,
    rotateX: 15,
  },
  visible: {
    opacity: 1,
    y: 0,
    rotateX: 0,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94],
      opacity: { duration: 0.4 },
      y: { duration: 0.8 },
      rotateX: { duration: 0.8 },
    },
  },
};

export const improvedStaggerContainer: Variants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

export const improvedStaggerItem: Variants = {
  hidden: {
    opacity: 0,
    y: 30,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: [0.25, 0.46, 0.45, 0.94],
    },
  },
};

// Enhanced scroll animation hook with better performance
export const useImprovedScrollAnimation = (options: {
  threshold?: number;
  triggerOnce?: boolean;
  margin?: string;
  delay?: number;
} = {}) => {
  const {
    threshold = 0.2,
    triggerOnce = true,
    margin = '-50px',
    delay = 0,
  } = options;

  const { settings, deviceInfo } = useAnimationContext();
  const ref = useRef(null);
  const controls = useAnimation();

  // Adjust threshold for mobile devices
  const adjustedThreshold = deviceInfo.isMobile ? 0.1 : threshold;
  const adjustedMargin = deviceInfo.isMobile ? '-20px' : margin;

  const isInView = useInView(ref, {
    threshold: adjustedThreshold,
    once: triggerOnce,
    margin: adjustedMargin,
  });

  useEffect(() => {
    if (isInView) {
      const timer = setTimeout(() => {
        controls.start('visible');
      }, delay);
      return () => clearTimeout(timer);
    } else if (!triggerOnce && settings.enableScrollAnimations) {
      controls.start('hidden');
    }
  }, [isInView, controls, delay, triggerOnce, settings.enableScrollAnimations]);

  return { ref, controls, isInView };
};

// Main improved scroll animation component
export const ImprovedScrollAnimation: React.FC<{
  children: React.ReactNode;
  animation?: 'fadeInUp' | 'fadeInLeft' | 'fadeInRight' | 'scaleIn' | 'slideInUp' | 'stagger';
  delay?: number;
  className?: string;
  threshold?: number;
  triggerOnce?: boolean;
  disabled?: boolean;
}> = ({
  children,
  animation = 'fadeInUp',
  delay = 0,
  className = '',
  threshold = 0.2,
  triggerOnce = true,
  disabled = false,
}) => {
  const { settings, deviceInfo } = useAnimationContext();
  const { ref, controls } = useImprovedScrollAnimation({
    threshold,
    triggerOnce,
    delay,
  });

  // Don't animate if disabled or animations are turned off
  if (disabled || !settings.enableScrollAnimations || settings.reducedMotion) {
    return <div className={className}>{children}</div>;
  }

  const getVariants = () => {
    switch (animation) {
      case 'fadeInUp':
        return improvedFadeInUp;
      case 'fadeInLeft':
        return improvedFadeInLeft;
      case 'fadeInRight':
        return improvedFadeInRight;
      case 'scaleIn':
        return improvedScaleIn;
      case 'slideInUp':
        return improvedSlideInUp;
      case 'stagger':
        return improvedStaggerContainer;
      default:
        return improvedFadeInUp;
    }
  };

  const variants = getVariants();

  // Adjust animation duration for mobile
  const adjustedVariants = {
    ...variants,
    visible: {
      ...variants.visible,
      transition: {
        ...variants.visible.transition,
        duration: deviceInfo.isMobile ? 0.4 : variants.visible.transition?.duration || 0.6,
      },
    },
  };

  return (
    <motion.div
      ref={ref}
      className={className}
      variants={adjustedVariants}
      initial="hidden"
      animate={controls}
      style={{
        willChange: 'transform, opacity',
      }}
    >
      {children}
    </motion.div>
  );
};

// Stagger animation wrapper
export const ImprovedStaggerAnimation: React.FC<{
  children: React.ReactNode;
  className?: string;
  staggerDelay?: number;
  disabled?: boolean;
}> = ({
  children,
  className = '',
  staggerDelay,
  disabled = false,
}) => {
  const { settings, deviceInfo } = useAnimationContext();
  const { ref, controls } = useImprovedScrollAnimation();

  if (disabled || !settings.enableScrollAnimations || settings.reducedMotion) {
    return <div className={className}>{children}</div>;
  }

  const adjustedStaggerDelay = staggerDelay || (deviceInfo.isMobile ? 0.05 : 0.1);

  const containerVariants: Variants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: adjustedStaggerDelay,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants: Variants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: deviceInfo.isMobile ? 0.3 : 0.5,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
  };

  return (
    <motion.div
      ref={ref}
      className={className}
      variants={containerVariants}
      initial="hidden"
      animate={controls}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div key={index} variants={itemVariants}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};

// Performance-optimized parallax component
export const ImprovedParallax: React.FC<{
  children: React.ReactNode;
  speed?: number;
  className?: string;
  disabled?: boolean;
}> = ({
  children,
  speed = 0.5,
  className = '',
  disabled = false,
}) => {
  const { settings, deviceInfo } = useAnimationContext();

  // Disable parallax on mobile or if not supported
  if (disabled || !settings.enableParallax || deviceInfo.isMobile || settings.reducedMotion) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      className={className}
      style={{
        willChange: 'transform',
      }}
      whileInView={{
        y: [0, -speed * 100],
      }}
      transition={{
        duration: 2,
        ease: 'linear',
      }}
      viewport={{
        once: false,
        amount: 0.1,
      }}
    >
      {children}
    </motion.div>
  );
};
