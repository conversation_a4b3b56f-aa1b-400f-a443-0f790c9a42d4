{"name": "n<PERSON>-b<PERSON><PERSON><PERSON>-portfolio", "private": true, "version": "1.0.0", "type": "module", "description": "Nural Bhardwaj's Portfolio - Full Stack Developer & UI/UX Designer", "author": "<PERSON><PERSON> Bhardwaj <<EMAIL>>", "homepage": "https://nuralbhardwaj.me", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "type-check": "tsc --noEmit", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"@emailjs/browser": "^4.4.1", "framer-motion": "^10.16.16", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.5.3", "react-router-dom": "^7.6.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "gh-pages": "^6.3.0", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.3.5"}}