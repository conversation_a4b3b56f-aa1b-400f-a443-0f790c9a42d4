import { ArrowRight, BookOpen, Calendar, Clock, Search, User } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { BlogPost } from '../data/cmsData';
import { cmsService } from '../services/cmsService';
import { ImprovedScrollAnimation, ImprovedStaggerAnimation } from './ImprovedScrollAnimations';

const Blog = () => {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPost, setSelectedPost] = useState<BlogPost | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [showAll, setShowAll] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  const POSTS_PER_PAGE = 6; // Show 6 posts per page initially

  useEffect(() => {
    const posts = cmsService.getPublishedBlogPosts();
    setBlogPosts(posts);
    setFilteredPosts(posts);
  }, []);

  useEffect(() => {
    let filtered = blogPosts;

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(post => post.category === selectedCategory);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    setFilteredPosts(filtered);
    setCurrentPage(1); // Reset pagination when filters change
    setShowAll(false);
  }, [blogPosts, selectedCategory, searchTerm]);

  const categories = ['All', ...Array.from(new Set(blogPosts.map(post => post.category)))];
  const featuredPosts = blogPosts.filter(post => post.featured);

  // Pagination logic
  const displayedPosts = showAll
    ? filteredPosts
    : filteredPosts.slice(0, currentPage * POSTS_PER_PAGE);

  const hasMorePosts = filteredPosts.length > POSTS_PER_PAGE && !showAll;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const BlogCard = ({ post, featured = false }: { post: BlogPost; featured?: boolean }) => (
    <div
      className={`blog-card group relative rounded-3xl overflow-hidden transition-all duration-500 transform hover:scale-[1.03] hover:-translate-y-3 ${
        featured ? 'lg:col-span-2' : ''
      }`}
      style={{
        background: 'linear-gradient(135deg, rgba(17, 24, 39, 0.95) 0%, rgba(31, 41, 55, 0.9) 50%, rgba(17, 24, 39, 0.95) 100%)',
        backdropFilter: 'blur(24px)',
        WebkitBackdropFilter: 'blur(24px)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        boxShadow: '0 20px 40px -12px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
        isolation: 'isolate',
        zIndex: 10
      }}
    >
      {/* Blog Image with Enhanced Effects */}
      <div className={`relative ${featured ? 'h-64' : 'h-56'} overflow-hidden`}>
        <img
          src={post.image}
          alt={post.title}
          className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"
        />

        {/* Multi-layer Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/30 to-transparent"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-purple-600/20 via-transparent to-cyan-600/20 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>

        {/* Enhanced Badges */}
        <div className="absolute top-4 left-4 right-4 flex justify-between items-start">
          {post.featured && (
            <div className="flex items-center space-x-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-black px-3 py-1.5 rounded-full text-xs font-bold backdrop-blur-sm">
              <BookOpen className="w-3 h-3" />
              <span>FEATURED</span>
            </div>
          )}

          <div className="flex items-center space-x-2 ml-auto">
            <span className="px-3 py-1.5 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 text-purple-300 rounded-full text-xs font-medium border border-purple-500/30 backdrop-blur-sm">
              {post.category}
            </span>
            <div className="flex items-center space-x-1 bg-black/60 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-xs font-medium border border-white/20">
              <Clock className="w-3 h-3" />
              <span>{post.readTime}m</span>
            </div>
          </div>
        </div>

        {/* Enhanced Overlay Action */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500">
          <button
            onClick={() => setSelectedPost(post)}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 backdrop-blur-sm rounded-xl text-white font-medium hover:from-purple-500 hover:to-cyan-500 transition-all duration-300 transform hover:scale-110 hover:-translate-y-1 shadow-lg hover:shadow-purple-500/30 border border-purple-400/30"
          >
            Read Article
          </button>
        </div>

        {/* Article Stats Overlay */}
        <div className="absolute bottom-4 left-4 right-4 flex justify-between items-center opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-2 group-hover:translate-y-0">
          <div className="flex items-center space-x-1 text-white text-sm bg-black/40 backdrop-blur-sm px-2 py-1 rounded-lg">
            <Calendar className="w-4 h-4 text-gray-300" />
            <span className="font-medium">{formatDate(post.publishedAt || post.createdAt)}</span>
          </div>
          <div className="flex items-center space-x-1 text-white text-sm bg-black/40 backdrop-blur-sm px-2 py-1 rounded-lg">
            <User className="w-4 h-4 text-gray-300" />
            <span className="font-medium">{post.author}</span>
          </div>
        </div>
      </div>

      {/* Enhanced Content Section */}
      <div className="p-6 relative">
        {/* Title */}
        <h3 className="text-xl font-bold text-white mb-3 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-purple-400 group-hover:to-cyan-400 group-hover:bg-clip-text transition-all duration-300 text-spacing-fix line-clamp-2">
          {post.title}
        </h3>

        {/* Excerpt */}
        <p className="text-gray-400 text-sm leading-relaxed mb-4 group-hover:text-gray-300 transition-colors duration-300 text-spacing-fix line-clamp-3">
          {post.excerpt}
        </p>

        {/* Enhanced Tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          {post.tags.slice(0, 3).map((tag, index) => (
            <span
              key={tag}
              className="px-3 py-1.5 bg-gradient-to-r from-white/10 to-white/5 text-gray-300 text-xs rounded-lg font-medium border border-white/10 hover:border-purple-400/50 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/20 hover:to-cyan-500/20 transition-all duration-300 transform hover:scale-105"
              style={{
                animationDelay: `${index * 100}ms`
              }}
            >
              #{tag}
            </span>
          ))}
          {post.tags.length > 3 && (
            <span className="px-3 py-1.5 bg-gradient-to-r from-purple-600/30 to-cyan-600/30 text-purple-200 text-xs rounded-lg font-medium border border-purple-400/30 hover:border-purple-300/50 transition-all duration-300 transform hover:scale-105">
              +{post.tags.length - 3}
            </span>
          )}
        </div>

        {/* Enhanced Footer */}
        <div className="flex items-center justify-between pt-4 border-t border-white/10">
          <button
            onClick={() => setSelectedPost(post)}
            className="flex items-center space-x-2 text-purple-400 hover:text-purple-300 transition-colors duration-200 text-sm font-medium group/btn"
          >
            <span>Read Article</span>
            <ArrowRight className="w-4 h-4 transform group-hover/btn:translate-x-1 transition-transform duration-200" />
          </button>

          <div className="flex items-center space-x-3 text-gray-400 text-sm">
            <div className="flex items-center space-x-1">
              <Calendar className="w-4 h-4" />
              <span>{new Date(post.publishedAt || post.createdAt).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>{post.readTime}m</span>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Glow Effects */}
      <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-600/20 via-cyan-600/20 to-purple-600/20 opacity-0 group-hover:opacity-100 blur-xl transition-all duration-700 rounded-3xl -z-10"></div>
      <div className="absolute -inset-1 bg-gradient-to-r from-purple-500/10 via-cyan-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 blur-2xl transition-all duration-700 rounded-3xl -z-10"></div>
    </div>
  );

  return (
    <section id="blog" ref={sectionRef} className="py-32 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
      
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-pulse"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Header */}
        <ImprovedScrollAnimation animation="fadeInUp" delay={0.2}>
          <div className="text-center mb-16">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 backdrop-blur-sm border border-purple-500/30 rounded-full px-6 py-3 mb-8">
              <BookOpen className="w-5 h-5 text-purple-400" />
              <span className="text-purple-300 font-medium">Blog & Articles</span>
            </div>

            <h2
              className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 leading-tight section-title text-spacing-fix blog-main-title latest-insights-title"
              style={{
                wordSpacing: 'normal !important',
                letterSpacing: 'normal !important',
                whiteSpace: 'normal !important'
              }}
            >
              <span
                className="text-spacing-fix latest-insights-word"
                style={{
                  wordSpacing: 'normal !important',
                  letterSpacing: 'normal !important',
                  whiteSpace: 'normal !important'
                }}
              >
                Latest
              </span>{' '}
              <span className="relative inline-block">
                <span
                  className="bg-gradient-to-r from-purple-400 via-pink-500 to-cyan-400 bg-clip-text text-transparent text-spacing-fix latest-insights-word"
                  style={{
                    wordSpacing: 'normal !important',
                    letterSpacing: 'normal !important',
                    whiteSpace: 'normal !important'
                  }}
                >
                  Tech Insights
                </span>
                <div className="absolute -inset-2 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 blur-2xl animate-pulse"></div>
              </span>
            </h2>

            <p className="text-lg sm:text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
              Sharing knowledge, experiences, and insights about web development, design, and technology
            </p>
          </div>
        </ImprovedScrollAnimation>

        {/* Search and Filter */}
        <ImprovedScrollAnimation animation="slideInUp" delay={0.4}>
          <div className="flex flex-col md:flex-row gap-4 mb-12">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 bg-white/5 border border-white/10 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
              />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-6 py-3 rounded-2xl font-medium transition-all duration-300 ${
                    selectedCategory === category
                      ? 'bg-gradient-to-r from-purple-600 to-cyan-600 text-white shadow-lg'
                      : 'bg-white/5 text-gray-300 hover:bg-white/10 border border-white/10 hover:border-white/20'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </ImprovedScrollAnimation>

        {/* Featured Posts */}
        {featuredPosts.length > 0 && (
          <ImprovedScrollAnimation animation="fadeInUp" delay={0.6}>
            <div className="mb-16">
              <h3 className="text-2xl sm:text-3xl font-bold text-white mb-8 flex items-center space-x-3">
                <span>Featured Articles</span>
                <div className="w-12 h-1 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full"></div>
              </h3>
              <ImprovedStaggerAnimation className="grid lg:grid-cols-2 gap-6 sm:gap-8">
                {featuredPosts.slice(0, 2).map((post) => (
                  <BlogCard key={post.id} post={post} featured />
                ))}
              </ImprovedStaggerAnimation>
            </div>
          </ImprovedScrollAnimation>
        )}

        {/* All Posts */}
        <ImprovedScrollAnimation animation="fadeInUp" delay={0.8} className="space-y-8">
          <h3 className="text-2xl sm:text-3xl font-bold text-white flex items-center space-x-3">
            <span>All Articles</span>
            <div className="w-12 h-1 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full"></div>
          </h3>

          {filteredPosts.length > 0 ? (
            <ImprovedStaggerAnimation className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
              {displayedPosts.map((post) => (
                <BlogCard key={post.id} post={post} />
              ))}
            </ImprovedStaggerAnimation>
          ) : (
            <div className="text-center py-16">
              <BookOpen className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-400 mb-2">No articles found</h3>
              <p className="text-gray-500">Try adjusting your search or filter criteria</p>
            </div>
          )}
        </ImprovedScrollAnimation>

        {/* Load More Button */}
        {hasMorePosts && (
          <ImprovedScrollAnimation animation="fadeInUp" delay={0.2} className="text-center mt-12">
            <button
              onClick={() => {
                if (currentPage * POSTS_PER_PAGE >= filteredPosts.length) {
                  setShowAll(true);
                } else {
                  setCurrentPage(prev => prev + 1);
                }
              }}
              className="group relative px-8 py-4 bg-gradient-to-r from-purple-600 to-cyan-600 text-white font-bold text-lg rounded-2xl transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25"
            >
              <span className="relative z-10">
                {currentPage * POSTS_PER_PAGE >= filteredPosts.length ? 'Show All Articles' : 'Load More Articles'}
              </span>
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-cyan-600 opacity-30 blur-lg rounded-2xl group-hover:opacity-50 transition-opacity duration-500"></div>
            </button>

            <p className="text-gray-400 text-sm mt-4">
              Showing {displayedPosts.length} of {filteredPosts.length} articles
            </p>
          </ImprovedScrollAnimation>
        )}

        {/* Blog Post Modal */}
        {selectedPost && (
          <div className="fixed inset-0 bg-black/98 z-[9999] flex items-center justify-center p-4 animate-fadeIn" style={{ backdropFilter: 'blur(20px)' }}>
            <div
              className="rounded-3xl max-w-4xl w-full max-h-[90vh] overflow-y-auto border-2 border-purple-500/60 shadow-2xl shadow-purple-500/50"
              style={{
                background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.98) 0%, rgba(17, 24, 39, 0.95) 50%, rgba(0, 0, 0, 0.98) 100%)',
                backdropFilter: 'blur(40px) saturate(180%)',
                WebkitBackdropFilter: 'blur(40px) saturate(180%)',
                isolation: 'isolate'
              }}
            >
              {/* Modal Header */}
              <div className="relative">
                <img
                  src={selectedPost.image}
                  alt={selectedPost.title}
                  className="w-full h-80 object-cover rounded-t-3xl"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent rounded-t-3xl"></div>
                
                <button
                  onClick={() => setSelectedPost(null)}
                  className="absolute top-6 right-6 p-3 bg-black/60 backdrop-blur-sm rounded-full text-white hover:bg-red-600/80 transition-all duration-300 transform hover:scale-110 border border-white/10"
                >
                  ×
                </button>
              </div>
              
              {/* Modal Content */}
              <div className="p-8">
                <div className="flex items-center space-x-4 mb-6">
                  <span className="px-3 py-1 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 text-purple-300 rounded-full text-sm font-medium">
                    {selectedPost.category}
                  </span>
                  <div className="flex items-center space-x-2 text-gray-400 text-sm">
                    <Calendar className="w-4 h-4" />
                    <span>{formatDate(selectedPost.publishedAt || selectedPost.createdAt)}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-400 text-sm">
                    <Clock className="w-4 h-4" />
                    <span>{selectedPost.readTime} min read</span>
                  </div>
                </div>
                
                <h1 className="text-4xl font-bold text-white mb-4 text-spacing-fix modal-content">{selectedPost.title}</h1>
                <p className="text-xl text-gray-400 mb-8 text-spacing-fix modal-content">{selectedPost.excerpt}</p>

                {/* Content */}
                <div className="prose prose-invert max-w-none modal-content">
                  <div className="text-gray-300 leading-relaxed whitespace-pre-wrap text-spacing-fix">
                    {selectedPost.content}
                  </div>
                </div>
                
                {/* Tags */}
                <div className="flex flex-wrap gap-2 mt-8 pt-8 border-t border-white/10">
                  {selectedPost.tags.map((tag) => (
                    <span
                      key={tag}
                      className="px-3 py-1 bg-gray-700/50 text-gray-300 text-sm rounded-full font-medium"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default Blog;
