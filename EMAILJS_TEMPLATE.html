<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            line-height: 1.6;
            color: #e2e8f0;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
        }
        .container {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 16px;
            padding: 30px;
            border: 1px solid rgba(139, 92, 246, 0.3);
            box-shadow: 0 20px 60px rgba(139, 92, 246, 0.2);
            backdrop-filter: blur(10px);
        }
        .header {
            background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 50%, #10b981 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shine 3s infinite;
        }
        @keyframes shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
            position: relative;
            z-index: 1;
        }
        .header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
            position: relative;
            z-index: 1;
        }
        .section {
            margin-bottom: 25px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(139, 92, 246, 0.2);
            backdrop-filter: blur(5px);
        }
        .section h3 {
            margin: 0 0 20px 0;
            color: #a855f7;
            font-size: 18px;
            font-weight: 600;
            text-shadow: 0 0 10px rgba(168, 85, 247, 0.5);
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-item {
            background: rgba(0, 0, 0, 0.4);
            padding: 18px;
            border-radius: 8px;
            border: 1px solid rgba(6, 182, 212, 0.3);
            transition: all 0.3s ease;
        }
        .info-item:hover {
            border-color: rgba(6, 182, 212, 0.6);
            box-shadow: 0 0 20px rgba(6, 182, 212, 0.2);
        }
        .info-label {
            font-weight: 600;
            color: #06b6d4;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
        }
        .info-value {
            color: #f1f5f9;
            font-size: 16px;
            font-weight: 500;
        }
        .message-section {
            background: rgba(0, 0, 0, 0.3);
            padding: 25px;
            border-radius: 12px;
            border: 1px solid rgba(16, 185, 129, 0.3);
            margin-top: 20px;
        }
        .message-content {
            background: rgba(0, 0, 0, 0.5);
            padding: 25px;
            border-radius: 8px;
            border-left: 4px solid #10b981;
            font-size: 15px;
            line-height: 1.8;
            white-space: pre-wrap;
            color: #e2e8f0;
            box-shadow: inset 0 0 20px rgba(16, 185, 129, 0.1);
        }
        .footer {
            text-align: center;
            padding: 25px;
            color: #94a3b8;
            font-size: 12px;
            border-top: 1px solid rgba(139, 92, 246, 0.3);
            margin-top: 30px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
        }
        .footer strong {
            color: #e2e8f0;
        }
        .priority-badge {
            display: inline-block;
            padding: 6px 14px;
            border-radius: 20px;
            font-size: 10px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .priority-asap {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 0 15px rgba(239, 68, 68, 0.5);
        }
        .priority-urgent {
            background: linear-gradient(45deg, #f97316, #ea580c);
            color: white;
            box-shadow: 0 0 15px rgba(249, 115, 22, 0.5);
        }
        .priority-normal {
            background: linear-gradient(45deg, #22c55e, #16a34a);
            color: white;
            box-shadow: 0 0 15px rgba(34, 197, 94, 0.5);
        }
        .budget-high {
            color: #22c55e;
            font-weight: 700;
            text-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
        }
        .budget-medium {
            color: #f97316;
            font-weight: 700;
            text-shadow: 0 0 10px rgba(249, 115, 22, 0.5);
        }
        .budget-low {
            color: #94a3b8;
            font-weight: 600;
        }
        .glow-text {
            text-shadow: 0 0 10px currentColor;
        }
        .cyber-border {
            position: relative;
        }
        .cyber-border::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: linear-gradient(45deg, #8b5cf6, #06b6d4, #10b981, #8b5cf6);
            border-radius: inherit;
            z-index: -1;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header cyber-border">
            <h1>⚡ INCOMING PROJECT TRANSMISSION</h1>
            <p>[ CYBERSEC PORTFOLIO CONTACT PROTOCOL ]</p>
        </div>

        <div class="section">
            <h3>🔐 CLIENT_DATA.DECRYPT()</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">IDENTITY_NAME</div>
                    <div class="info-value glow-text">{{from_name}}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">CONTACT_VECTOR</div>
                    <div class="info-value glow-text">{{from_email}}</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>📊 PROJECT_PARAMETERS.ANALYZE()</h3>
            <div class="info-item" style="margin-bottom: 15px;">
                <div class="info-label">MISSION_OBJECTIVE</div>
                <div class="info-value glow-text">{{subject}}</div>
            </div>

            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">RESOURCE_ALLOCATION</div>
                    <div class="info-value budget-high">⚡ {{budget}}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">EXECUTION_WINDOW</div>
                    <div class="info-value glow-text">🕒 {{timeline}}</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>💾 MESSAGE_PAYLOAD.EXTRACT()</h3>
            <div class="message-section">
                <div class="message-content">{{message}}</div>
            </div>
        </div>

        <div class="footer">
            <p>📡 <strong>REPLY_CHANNEL:</strong> <span class="glow-text">{{reply_to}}</span></p>
            <p>🌐 <strong>TRANSMISSION_SOURCE:</strong> <span class="glow-text">NURAL.BHARDWAJ.PORTFOLIO.SYS</span></p>
            <p>⏰ <strong>TIMESTAMP:</strong> <span class="glow-text">{{sent_date}} :: {{sent_time}}</span></p>
            <hr style="margin: 20px 0; border: none; border-top: 1px solid rgba(139, 92, 246, 0.3);">
            <p style="color: #22c55e; font-weight: 700; text-shadow: 0 0 10px rgba(34, 197, 94, 0.5);">
                🎯 <strong>PROTOCOL_NEXT:</strong> ANALYZE >> RESPOND >> EXECUTE [24H_WINDOW]
            </p>
            <p style="color: #06b6d4; font-size: 10px; margin-top: 15px;">
                [ SECURE_TRANSMISSION_COMPLETE ] [ STATUS: DELIVERED ] [ ENCRYPTION: AES-256 ]
            </p>
        </div>
    </div>
</body>
</html>
