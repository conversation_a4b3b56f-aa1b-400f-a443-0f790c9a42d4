// Advanced Resource Preloading Service
// Intelligently preloads resources based on user behavior and network conditions

interface PreloadConfig {
  enableIntelligentPreloading: boolean;
  enableNetworkAwareLoading: boolean;
  enableUserBehaviorPrediction: boolean;
  preloadThreshold: number; // seconds before predicted navigation
  maxConcurrentPreloads: number;
  networkSpeedThreshold: string; // '4g', '3g', '2g', 'slow-2g'
}

interface ResourceMetrics {
  url: string;
  loadTime: number;
  size: number;
  priority: 'high' | 'medium' | 'low';
  lastAccessed: number;
  accessCount: number;
}

class ResourcePreloadingService {
  private static instance: ResourcePreloadingService;
  private config: PreloadConfig;
  private resourceMetrics: Map<string, ResourceMetrics> = new Map();
  private preloadQueue: Set<string> = new Set();
  private userBehavior: Array<{ url: string; timestamp: number }> = [];
  private networkInfo: any = null;

  private defaultConfig: PreloadConfig = {
    enableIntelligentPreloading: true,
    enableNetworkAwareLoading: true,
    enableUserBehaviorPrediction: false, // Disabled for less aggressive optimization
    preloadThreshold: 3, // 3 seconds before predicted navigation (less aggressive)
    maxConcurrentPreloads: 2, // Reduced concurrent preloads
    networkSpeedThreshold: '4g' // Only preload on fast networks
  };

  private constructor() {
    this.config = { ...this.defaultConfig };
    this.initializeNetworkInfo();
    this.startUserBehaviorTracking();
  }

  public static getInstance(): ResourcePreloadingService {
    if (!ResourcePreloadingService.instance) {
      ResourcePreloadingService.instance = new ResourcePreloadingService();
    }
    return ResourcePreloadingService.instance;
  }

  // Initialize network information
  private initializeNetworkInfo(): void {
    if ('connection' in navigator) {
      this.networkInfo = (navigator as any).connection;
      
      // Listen for network changes
      this.networkInfo.addEventListener('change', () => {
        this.adjustPreloadingStrategy();
      });
    }
  }

  // Start tracking user behavior for prediction
  private startUserBehaviorTracking(): void {
    if (!this.config.enableUserBehaviorPrediction) return;

    // Track link hovers
    document.addEventListener('mouseover', (e) => {
      const link = (e.target as Element).closest('a');
      if (link && link.href) {
        this.recordUserInteraction(link.href);
        this.predictAndPreload(link.href);
      }
    });

    // Track scroll behavior
    let scrollTimeout: number;
    document.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = window.setTimeout(() => {
        this.analyzeScrollBehavior();
      }, 150);
    }, { passive: true });
  }

  // Record user interaction for behavior analysis
  private recordUserInteraction(url: string): void {
    this.userBehavior.push({
      url,
      timestamp: Date.now()
    });

    // Keep only recent interactions (last 10 minutes)
    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
    this.userBehavior = this.userBehavior.filter(
      interaction => interaction.timestamp > tenMinutesAgo
    );
  }

  // Predict and preload resources based on user behavior
  private predictAndPreload(hoveredUrl: string): void {
    if (!this.config.enableIntelligentPreloading) return;
    if (this.preloadQueue.has(hoveredUrl)) return;

    // Check network conditions
    if (!this.shouldPreloadBasedOnNetwork()) return;

    // Predict likelihood of navigation
    const likelihood = this.calculateNavigationLikelihood(hoveredUrl);
    
    if (likelihood > 0.7) { // 70% likelihood threshold
      this.preloadResource(hoveredUrl, 'high');
    } else if (likelihood > 0.4) { // 40% likelihood threshold
      setTimeout(() => {
        if (this.preloadQueue.size < this.config.maxConcurrentPreloads) {
          this.preloadResource(hoveredUrl, 'medium');
        }
      }, this.config.preloadThreshold * 1000);
    }
  }

  // Calculate navigation likelihood based on user behavior
  private calculateNavigationLikelihood(url: string): number {
    let likelihood = 0.3; // Base likelihood

    // Check recent interactions with similar URLs
    const recentInteractions = this.userBehavior.filter(
      interaction => Date.now() - interaction.timestamp < 30000 // Last 30 seconds
    );

    const similarInteractions = recentInteractions.filter(
      interaction => interaction.url.includes(url) || url.includes(interaction.url)
    );

    likelihood += similarInteractions.length * 0.2;

    // Check if user is hovering (this method is called on hover)
    likelihood += 0.3;

    // Check scroll direction and speed
    likelihood += this.getScrollBasedLikelihood();

    return Math.min(likelihood, 1.0);
  }

  // Get likelihood based on scroll behavior
  private getScrollBasedLikelihood(): number {
    // Simple implementation - can be enhanced
    const scrollPosition = window.scrollY;
    const documentHeight = document.documentElement.scrollHeight;
    const windowHeight = window.innerHeight;
    
    const scrollPercentage = scrollPosition / (documentHeight - windowHeight);
    
    // Higher likelihood if user is actively scrolling
    return scrollPercentage > 0.5 ? 0.2 : 0.1;
  }

  // Check if preloading should happen based on network conditions
  private shouldPreloadBasedOnNetwork(): boolean {
    if (!this.config.enableNetworkAwareLoading || !this.networkInfo) {
      return true; // Default to allowing preloading
    }

    const effectiveType = this.networkInfo.effectiveType;
    const saveData = this.networkInfo.saveData;

    // Don't preload if user has data saver enabled
    if (saveData) return false;

    // Check network speed threshold
    const networkSpeeds = ['slow-2g', '2g', '3g', '4g'];
    const currentSpeedIndex = networkSpeeds.indexOf(effectiveType);
    const thresholdIndex = networkSpeeds.indexOf(this.config.networkSpeedThreshold);

    return currentSpeedIndex >= thresholdIndex;
  }

  // Preload a resource
  private preloadResource(url: string, priority: 'high' | 'medium' | 'low'): void {
    if (this.preloadQueue.has(url)) return;
    if (this.preloadQueue.size >= this.config.maxConcurrentPreloads) return;

    this.preloadQueue.add(url);

    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = url;
    
    if (priority === 'high') {
      link.rel = 'preload';
      link.as = 'document';
    }

    link.onload = () => {
      this.preloadQueue.delete(url);
      this.recordResourceMetrics(url, 'success');
    };

    link.onerror = () => {
      this.preloadQueue.delete(url);
      this.recordResourceMetrics(url, 'error');
    };

    document.head.appendChild(link);

    console.log(`🚀 Preloading resource: ${url} (priority: ${priority})`);
  }

  // Record resource metrics for optimization
  private recordResourceMetrics(url: string, status: 'success' | 'error'): void {
    const existing = this.resourceMetrics.get(url);
    
    if (existing) {
      existing.accessCount++;
      existing.lastAccessed = Date.now();
    } else {
      this.resourceMetrics.set(url, {
        url,
        loadTime: 0,
        size: 0,
        priority: 'medium',
        lastAccessed: Date.now(),
        accessCount: 1
      });
    }
  }

  // Analyze scroll behavior for preloading decisions
  private analyzeScrollBehavior(): void {
    const scrollPosition = window.scrollY;
    const documentHeight = document.documentElement.scrollHeight;
    const windowHeight = window.innerHeight;
    
    const scrollPercentage = scrollPosition / (documentHeight - windowHeight);

    // Preload next section content if user is scrolling down
    if (scrollPercentage > 0.7) {
      this.preloadNextSectionResources();
    }
  }

  // Preload resources for the next section
  private preloadNextSectionResources(): void {
    const sections = document.querySelectorAll('section');
    const currentSection = this.getCurrentSection(sections);
    
    if (currentSection) {
      const nextSection = currentSection.nextElementSibling as HTMLElement;
      if (nextSection) {
        this.preloadSectionResources(nextSection);
      }
    }
  }

  // Get current section based on scroll position
  private getCurrentSection(sections: NodeListOf<Element>): Element | null {
    const scrollPosition = window.scrollY + window.innerHeight / 2;
    
    for (const section of sections) {
      const rect = section.getBoundingClientRect();
      const sectionTop = rect.top + window.scrollY;
      const sectionBottom = sectionTop + rect.height;
      
      if (scrollPosition >= sectionTop && scrollPosition <= sectionBottom) {
        return section;
      }
    }
    
    return null;
  }

  // Preload resources within a section (with CSP compliance)
  private preloadSectionResources(section: HTMLElement): void {
    const images = section.querySelectorAll('img[data-src]');
    const links = section.querySelectorAll('a[href]');

    // Preload images
    images.forEach((img, index) => {
      if (index < 2) { // Reduced to 2 images for less aggressive preloading
        const dataSrc = img.getAttribute('data-src');
        if (dataSrc && !dataSrc.startsWith('mailto:') && !dataSrc.startsWith('tel:')) {
          this.preloadResource(dataSrc, 'medium');
        }
      }
    });

    // Preload only internal links to avoid CSP violations
    links.forEach((link, index) => {
      if (index < 1) { // Reduced to 1 link for less aggressive preloading
        const href = (link as HTMLAnchorElement).href;
        if (href &&
            !href.startsWith('#') &&
            !href.startsWith('mailto:') &&
            !href.startsWith('tel:') &&
            !href.startsWith('http://') &&
            !href.startsWith('https://github.com') &&
            !href.startsWith('https://linkedin.com')) {
          this.preloadResource(href, 'low');
        }
      }
    });
  }

  // Adjust preloading strategy based on network changes
  private adjustPreloadingStrategy(): void {
    if (!this.networkInfo) return;

    const effectiveType = this.networkInfo.effectiveType;
    
    // Adjust max concurrent preloads based on network speed
    switch (effectiveType) {
      case '4g':
        this.config.maxConcurrentPreloads = 5;
        break;
      case '3g':
        this.config.maxConcurrentPreloads = 3;
        break;
      case '2g':
        this.config.maxConcurrentPreloads = 1;
        break;
      case 'slow-2g':
        this.config.maxConcurrentPreloads = 0;
        break;
    }

    console.log(`📶 Network changed to ${effectiveType}, adjusted preloading strategy`);
  }

  // Get current configuration
  public getConfig(): PreloadConfig {
    return { ...this.config };
  }

  // Update configuration
  public updateConfig(newConfig: Partial<PreloadConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  // Get resource metrics
  public getResourceMetrics(): Map<string, ResourceMetrics> {
    return new Map(this.resourceMetrics);
  }

  // Clear preload queue
  public clearPreloadQueue(): void {
    this.preloadQueue.clear();
  }
}

// Export singleton instance
export const resourcePreloadingService = ResourcePreloadingService.getInstance();
export default ResourcePreloadingService;
