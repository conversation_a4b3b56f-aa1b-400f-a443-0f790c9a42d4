import { Brain, Code, Database, Globe, Palette, Server, Shield, Smartphone } from 'lucide-react';
import { useRef, useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { ImprovedScrollAnimation, ImprovedStaggerAnimation } from './ImprovedScrollAnimations';

const Skills = () => {
  const { t } = useLanguage();
  const [activeCategory, setActiveCategory] = useState(0);
  const [hoveredSkill, setHoveredSkill] = useState<string | null>(null);
  const sectionRef = useRef<HTMLElement>(null);
  // CMS Skills integration - for future use
  // const [cmsSkills, setCmsSkills] = useState<Skill[]>([]);

  // useEffect(() => {
  //   const skills = cmsService.getSkills();
  //   setCmsSkills(skills);
  // }, []);

  const skillCategories = [
    {
      title: t('skills.frontend'),
      icon: Code,
      color: 'from-blue-500 to-cyan-500',
      skills: [
        { name: 'React', level: 98, description: 'Expert in hooks, context, performance optimization & advanced patterns' },
        { name: 'TypeScript', level: 95, description: 'Advanced type systems, generics, and enterprise-level applications' },
        { name: 'Next.js', level: 92, description: 'Full-stack applications with SSR, SSG, API routes & deployment' },
        { name: 'Tailwind CSS', level: 96, description: 'Custom design systems, responsive layouts & component libraries' },
        { name: 'Three.js', level: 82, description: '3D graphics, WebGL, interactive experiences & game development' },
        { name: 'Framer Motion', level: 90, description: 'Complex animations, gesture handling & performance optimization' }
      ]
    },
    {
      title: t('skills.backend'),
      icon: Server,
      color: 'from-green-500 to-emerald-500',
      skills: [
        { name: 'Node.js', level: 94, description: 'Scalable APIs, microservices, real-time applications & performance tuning' },
        { name: 'Python', level: 88, description: 'Django, FastAPI, data processing, AI/ML integration & automation' },
        { name: 'PostgreSQL', level: 91, description: 'Advanced queries, indexing, performance optimization & data modeling' },
        { name: 'MongoDB', level: 85, description: 'Document design, aggregation pipelines, sharding & replica sets' },
        { name: 'GraphQL', level: 86, description: 'Schema design, resolvers, federation & performance optimization' },
        { name: 'Redis', level: 83, description: 'Caching strategies, pub/sub, session management & data structures' }
      ]
    },
    {
      title: t('skills.tools'),
      icon: Globe,
      color: 'from-orange-500 to-red-500',
      skills: [
        { name: 'AWS', level: 89, description: 'EC2, S3, Lambda, CloudFormation, RDS, and enterprise serverless architecture' },
        { name: 'Docker', level: 92, description: 'Advanced containerization, multi-stage builds, and orchestration' },
        { name: 'Kubernetes', level: 78, description: 'Container orchestration, scaling, service mesh, and cluster management' },
        { name: 'CI/CD', level: 87, description: 'GitHub Actions, Jenkins, GitLab CI, automated testing and deployment pipelines' },
        { name: 'Terraform', level: 73, description: 'Infrastructure as code, state management, and cloud provisioning' },
        { name: 'Monitoring', level: 84, description: 'Prometheus, Grafana, ELK stack, APM, and distributed tracing' }
      ]
    },
    {
      title: t('skills.design'),
      icon: Palette,
      color: 'from-purple-500 to-pink-500',
      skills: [
        { name: 'Figma', level: 90, description: 'UI/UX design & prototyping' },
        { name: 'Adobe Creative Suite', level: 85, description: 'Photoshop, Illustrator, After Effects' },
        { name: 'User Research', level: 80, description: 'User interviews & usability testing' },
        { name: 'Design Systems', level: 88, description: 'Component libraries & style guides' },
        { name: 'Accessibility', level: 85, description: 'WCAG compliance & inclusive design' },
        { name: 'Prototyping', level: 82, description: 'Interactive mockups & user flows' }
      ]
    },
    {
      title: t('skills.ml'),
      icon: Brain,
      color: 'from-indigo-500 to-purple-500',
      skills: [
        { name: 'TensorFlow', level: 85, description: 'Deep learning models, neural networks, and production deployment' },
        { name: 'PyTorch', level: 82, description: 'Research-oriented ML, computer vision, and natural language processing' },
        { name: 'Scikit-learn', level: 88, description: 'Classical ML algorithms, data preprocessing, and model evaluation' },
        { name: 'OpenCV', level: 80, description: 'Computer vision, image processing, and real-time video analysis' },
        { name: 'Pandas & NumPy', level: 92, description: 'Data manipulation, analysis, and scientific computing' },
        { name: 'MLOps', level: 75, description: 'Model deployment, monitoring, and continuous integration for ML' }
      ]
    },
    {
      title: t('skills.security'),
      icon: Shield,
      color: 'from-red-500 to-pink-500',
      skills: [
        { name: 'Penetration Testing', level: 78, description: 'Vulnerability assessment, ethical hacking, and security audits' },
        { name: 'Network Security', level: 82, description: 'Firewall configuration, intrusion detection, and network monitoring' },
        { name: 'Web Security', level: 85, description: 'OWASP Top 10, XSS, CSRF, SQL injection prevention and mitigation' },
        { name: 'Cryptography', level: 80, description: 'Encryption algorithms, digital signatures, and secure communication' },
        { name: 'Security Tools', level: 83, description: 'Nmap, Wireshark, Metasploit, Burp Suite, and security frameworks' },
        { name: 'Compliance', level: 75, description: 'GDPR, SOC 2, ISO 27001, and security policy implementation' }
      ]
    },
    {
      title: t('skills.mobile'),
      icon: Smartphone,
      color: 'from-green-500 to-teal-500',
      skills: [
        { name: 'React Native', level: 88, description: 'Cross-platform mobile apps with native performance and features' },
        { name: 'Flutter', level: 82, description: 'Dart-based mobile development with beautiful, fast user interfaces' },
        { name: 'iOS Development', level: 75, description: 'Swift, UIKit, SwiftUI, and App Store deployment' },
        { name: 'Android Development', level: 78, description: 'Kotlin, Java, Android SDK, and Google Play Store publishing' },
        { name: 'Mobile UI/UX', level: 85, description: 'Platform-specific design patterns and mobile user experience' },
        { name: 'App Performance', level: 80, description: 'Optimization, profiling, and mobile-specific performance tuning' }
      ]
    },
    {
      title: t('skills.database'),
      icon: Database,
      color: 'from-blue-500 to-indigo-500',
      skills: [
        { name: 'PostgreSQL', level: 91, description: 'Advanced SQL, indexing, performance tuning, and complex queries' },
        { name: 'MongoDB', level: 85, description: 'NoSQL design, aggregation pipelines, sharding, and replica sets' },
        { name: 'Redis', level: 83, description: 'In-memory caching, pub/sub, session storage, and data structures' },
        { name: 'Database Design', level: 88, description: 'Schema design, normalization, relationships, and optimization' },
        { name: 'Data Modeling', level: 86, description: 'Entity-relationship modeling, data warehousing, and analytics' },
        { name: 'Database Security', level: 82, description: 'Access control, encryption, backup strategies, and compliance' }
      ]
    }
  ];

  return (
    <section id="skills" ref={sectionRef} className="py-32 relative overflow-hidden">
      {/* Ultra Advanced Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
      
      {/* Animated Geometric Patterns */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur-3xl animate-float-slow"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-cyan-600 to-blue-600 rounded-full blur-3xl animate-float-reverse"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-green-600 to-emerald-600 rounded-full blur-3xl animate-pulse-slow"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header with Improved Scroll Animation */}
        <ImprovedScrollAnimation animation="fadeInUp" delay={0.2} className="text-center mb-20">
          <ImprovedScrollAnimation animation="scaleIn" delay={0.4}>
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 backdrop-blur-sm border border-purple-500/30 rounded-full px-6 py-3 mb-8">
              <Code className="w-5 h-5 text-purple-400" />
              <span className="text-purple-300 font-medium">{t('skills.subtitle')}</span>
            </div>
          </ImprovedScrollAnimation>

          <ImprovedScrollAnimation animation="scaleIn" delay={0.6}>
            <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-black text-white mb-8 leading-tight">
              <span className="relative inline-block">
                <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-cyan-400 bg-clip-text text-transparent">
                  {t('skills.title')}
                </span>
                <div className="absolute -inset-2 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 blur-2xl animate-pulse"></div>
              </span>
            </h2>
          </ImprovedScrollAnimation>

          <ImprovedScrollAnimation animation="fadeInLeft" delay={0.8}>
            <div className="w-32 h-1 bg-gradient-to-r from-purple-500 via-pink-500 to-cyan-500 mx-auto rounded-full mb-8"></div>
          </ImprovedScrollAnimation>

          <ImprovedScrollAnimation animation="fadeInRight" delay={1.0}>
            <p className="text-lg sm:text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
              A comprehensive toolkit of modern technologies and methodologies to build exceptional digital experiences
            </p>
          </ImprovedScrollAnimation>
        </ImprovedScrollAnimation>

        {/* Category Selector with Improved Scroll Animation */}
        <ImprovedStaggerAnimation className="flex flex-wrap justify-center gap-4 mb-16">
          {skillCategories.map((category, index) => (
            <div
              key={category.title}
              onClick={() => setActiveCategory(index)}
              className={`group relative px-6 sm:px-8 py-3 sm:py-4 rounded-2xl font-bold text-sm sm:text-lg transition-all duration-500 transform hover:scale-105 cursor-pointer ${
                activeCategory === index
                  ? `bg-gradient-to-r ${category.color} text-white shadow-2xl`
                  : 'bg-white/5 text-gray-300 hover:bg-white/10 border border-white/10 hover:border-white/20'
              }`}
            >
              <div className="flex items-center space-x-2 sm:space-x-3">
                <category.icon className="w-5 h-5 sm:w-6 sm:h-6" />
                <span>{category.title}</span>
              </div>
              {activeCategory === index && (
                <div className={`absolute -inset-1 bg-gradient-to-r ${category.color} opacity-30 blur-lg rounded-2xl`}></div>
              )}
            </div>
          ))}
        </ImprovedStaggerAnimation>

        {/* Skills Display with Improved Scroll Animation */}
        <ImprovedScrollAnimation animation="fadeInUp" delay={0.4} className="mb-20" key={`skills-${activeCategory}`}>
          <ImprovedStaggerAnimation className="grid lg:grid-cols-2 gap-6 sm:gap-8"  staggerDelay={0.1}>
            {skillCategories[activeCategory]?.skills?.map((skill, index) => (
              <div
                key={`${activeCategory}-${skill.name}-${index}`}
                className="skill-card group relative p-4 sm:p-6 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm rounded-3xl border border-white/10 hover:border-purple-500/30 transition-all duration-700 transform hover:scale-105"
                onMouseEnter={() => setHoveredSkill(skill.name)}
                onMouseLeave={() => setHoveredSkill(null)}
              >
                {/* Skill Header */}
                <div className="flex justify-between items-center mb-4">
                  <h4 className="text-xl font-bold text-white group-hover:text-purple-300 transition-colors duration-300">
                    {skill.name}
                  </h4>
                  <div className="flex items-center space-x-2">
                    <span className={`text-lg font-black bg-gradient-to-r ${skillCategories[activeCategory].color} bg-clip-text text-transparent`}>
                      {skill.level}%
                    </span>
                  </div>
                </div>

                {/* Enhanced Interactive Progress Bar */}
                <div className="relative h-4 bg-gray-800 rounded-full overflow-hidden mb-4 shadow-inner cursor-pointer group">
                  <div
                    className={`absolute inset-y-0 left-0 bg-gradient-to-r ${skillCategories[activeCategory].color} rounded-full shadow-lg transition-all duration-1000 ease-out group-hover:shadow-xl`}
                    style={{
                      width: `${skill.level}%`,
                      animationDelay: `${index * 0.1 + 0.5}s`
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-white/20 rounded-full"></div>
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 animate-shine"></div>

                    {/* Interactive Particles */}
                    {hoveredSkill === skill.name && (
                      <>
                        {[...Array(5)].map((_, particleIndex) => (
                          <div
                            key={particleIndex}
                            className="absolute w-1 h-1 bg-white rounded-full animate-bounce"
                            style={{
                              left: `${Math.random() * 100}%`,
                              top: `${Math.random() * 100}%`,
                              animationDelay: `${particleIndex * 0.1}s`,
                              animationDuration: '0.6s'
                            }}
                          />
                        ))}
                      </>
                    )}
                  </div>

                  {/* Enhanced Glow Effect */}
                  <div
                    className={`absolute inset-y-0 left-0 bg-gradient-to-r ${skillCategories[activeCategory].color} opacity-50 blur-sm rounded-full transition-all duration-1000 group-hover:opacity-70 group-hover:blur-md`}
                    style={{ width: `${skill.level}%` }}
                  ></div>

                  {/* Skill Level Tooltip */}
                  <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-black/80 text-white px-2 py-1 rounded text-xs whitespace-nowrap">
                      {skill.level}% Proficiency
                    </div>
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors duration-300">
                  {skill.description}
                </p>

                {/* Hover Effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 to-cyan-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                
                {/* Skill Level Indicator */}
                <div className="absolute top-4 right-4">
                  <div className={`w-3 h-3 bg-gradient-to-r ${skillCategories[activeCategory].color} rounded-full animate-pulse`}></div>
                </div>
              </div>
            ))}
          </ImprovedStaggerAnimation>
        </ImprovedScrollAnimation>

        {/* Technology Cloud */}
        <div className="text-center">
          <h3 className="text-2xl font-semibold text-white mb-8">
            {t('skills.tech.title')}
          </h3>
          <div className="flex flex-wrap justify-center gap-4">
            {[
              'JavaScript', 'Python', 'Java', 'Go', 'Rust', 'GraphQL', 'REST APIs', 
              'Microservices', 'Serverless', 'WebAssembly', 'Progressive Web Apps',
              'Blockchain', 'Web3', 'AI/ML', 'Data Visualization', 'Real-time Systems'
            ].map((tech, index) => (
              <span
                key={tech}
                className="group relative px-4 py-2 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-cyan-500/10 text-white rounded-full text-sm font-medium border border-purple-500/20 hover:border-purple-400/50 hover:scale-110 transition-all duration-300 cursor-default backdrop-blur-sm"
                style={{ animationDelay: `${index * 0.05}s` }}
              >
                {tech}
                <div className="absolute -inset-1 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 opacity-0 group-hover:opacity-100 blur-lg transition-opacity duration-300 rounded-full"></div>
              </span>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;