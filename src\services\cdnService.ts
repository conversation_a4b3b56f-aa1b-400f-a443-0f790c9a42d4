// Advanced CDN Integration Service
// Implements global content delivery optimization

export interface CDNProvider {
  name: string;
  baseUrl: string;
  regions: string[];
  features: {
    imageOptimization: boolean;
    videoStreaming: boolean;
    edgeComputing: boolean;
    realTimeAnalytics: boolean;
  };
}

export interface CDNConfig {
  primaryProvider: string;
  fallbackProviders: string[];
  autoFailover: boolean;
  performanceThreshold: number; // ms
  geoOptimization: boolean;
  compressionLevel: number;
  cacheHeaders: {
    static: number;
    dynamic: number;
    images: number;
  };
}

export interface CDNMetrics {
  totalRequests: number;
  cacheHitRate: number;
  avgResponseTime: number;
  bandwidthSaved: number;
  errorRate: number;
  geoDistribution: Record<string, number>;
  providerPerformance: Record<string, {
    requests: number;
    avgTime: number;
    errors: number;
  }>;
}

export interface AssetOptimization {
  url: string;
  originalSize: number;
  optimizedSize: number;
  format: string;
  compressionRatio: number;
}

class CDNService {
  private static instance: CDNService;
  private config: CDNConfig;
  private metrics: CDNMetrics;
  private providers: Map<string, CDNProvider> = new Map();
  private userLocation: { country: string; region: string } | null = null;
  private performanceData: Map<string, number[]> = new Map();

  private defaultConfig: CDNConfig = {
    primaryProvider: 'cloudflare',
    fallbackProviders: ['aws-cloudfront', 'fastly'],
    autoFailover: true,
    performanceThreshold: 2000,
    geoOptimization: true,
    compressionLevel: 6,
    cacheHeaders: {
      static: 31536000, // 1 year
      dynamic: 3600,    // 1 hour
      images: 2592000   // 30 days
    }
  };

  private constructor() {
    this.config = this.loadConfig();
    this.metrics = this.initializeMetrics();
    this.initializeCDNProviders();
    this.detectUserLocation();
    this.setupPerformanceMonitoring();
  }

  public static getInstance(): CDNService {
    if (!CDNService.instance) {
      CDNService.instance = new CDNService();
    }
    return CDNService.instance;
  }

  // Initialize CDN providers
  private initializeCDNProviders(): void {
    const providers: CDNProvider[] = [
      {
        name: 'cloudflare',
        baseUrl: 'https://cdn.cloudflare.com',
        regions: ['global'],
        features: {
          imageOptimization: true,
          videoStreaming: true,
          edgeComputing: true,
          realTimeAnalytics: true
        }
      },
      {
        name: 'aws-cloudfront',
        baseUrl: 'https://d1234567890.cloudfront.net',
        regions: ['us-east-1', 'eu-west-1', 'ap-southeast-1'],
        features: {
          imageOptimization: true,
          videoStreaming: true,
          edgeComputing: false,
          realTimeAnalytics: true
        }
      },
      {
        name: 'fastly',
        baseUrl: 'https://cdn.fastly.com',
        regions: ['global'],
        features: {
          imageOptimization: true,
          videoStreaming: false,
          edgeComputing: true,
          realTimeAnalytics: true
        }
      },
      {
        name: 'jsdelivr',
        baseUrl: 'https://cdn.jsdelivr.net',
        regions: ['global'],
        features: {
          imageOptimization: false,
          videoStreaming: false,
          edgeComputing: false,
          realTimeAnalytics: false
        }
      }
    ];

    providers.forEach(provider => {
      this.providers.set(provider.name, provider);
    });

    console.log('🌐 CDN providers initialized:', providers.map(p => p.name));
  }

  // Initialize metrics
  private initializeMetrics(): CDNMetrics {
    return {
      totalRequests: 0,
      cacheHitRate: 0,
      avgResponseTime: 0,
      bandwidthSaved: 0,
      errorRate: 0,
      geoDistribution: {},
      providerPerformance: {}
    };
  }

  // Detect user location for geo-optimization
  private async detectUserLocation(): Promise<void> {
    try {
      // Use browser-based location detection to avoid CORS issues
      this.userLocation = {
        country: navigator.language.split('-')[1] || 'US',
        region: Intl.DateTimeFormat().resolvedOptions().timeZone
      };

      console.log('🌍 User location detected:', this.userLocation);

      // Update geo distribution metrics
      if (this.userLocation.country) {
        this.metrics.geoDistribution[this.userLocation.country] =
          (this.metrics.geoDistribution[this.userLocation.country] || 0) + 1;
      }

    } catch (error) {
      console.warn('Failed to detect user location:', error);
      // Ultimate fallback
      this.userLocation = {
        country: 'US',
        region: 'America/New_York'
      };
    }
  }

  // Setup performance monitoring
  private setupPerformanceMonitoring(): void {
    // Monitor resource loading performance
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'resource') {
            this.recordResourcePerformance(entry as PerformanceResourceTiming);
          }
        });
      });

      observer.observe({ entryTypes: ['resource'] });
    }

    // Periodic performance analysis
    setInterval(() => {
      this.analyzeProviderPerformance();
    }, 60000); // Every minute
  }

  // Record resource performance
  private recordResourcePerformance(entry: PerformanceResourceTiming): void {
    const url = entry.name;
    const duration = entry.duration;

    // Determine which CDN provider served this resource
    const provider = this.identifyProvider(url);
    
    if (provider) {
      if (!this.performanceData.has(provider)) {
        this.performanceData.set(provider, []);
      }
      
      this.performanceData.get(provider)!.push(duration);
      
      // Keep only last 100 measurements per provider
      const measurements = this.performanceData.get(provider)!;
      if (measurements.length > 100) {
        measurements.splice(0, measurements.length - 100);
      }

      // Update metrics
      this.updateProviderMetrics(provider, duration, entry.transferSize > 0);
    }

    this.metrics.totalRequests++;
  }

  // Identify CDN provider from URL
  private identifyProvider(url: string): string | null {
    for (const [name, provider] of this.providers) {
      if (url.includes(provider.baseUrl) || url.includes(name)) {
        return name;
      }
    }
    return null;
  }

  // Update provider metrics
  private updateProviderMetrics(provider: string, responseTime: number, cacheHit: boolean): void {
    if (!this.metrics.providerPerformance[provider]) {
      this.metrics.providerPerformance[provider] = {
        requests: 0,
        avgTime: 0,
        errors: 0
      };
    }

    const providerMetrics = this.metrics.providerPerformance[provider];
    providerMetrics.requests++;
    
    // Update average response time
    providerMetrics.avgTime = (
      (providerMetrics.avgTime * (providerMetrics.requests - 1) + responseTime) / 
      providerMetrics.requests
    );

    // Update cache hit rate
    if (cacheHit) {
      this.metrics.cacheHitRate = (
        (this.metrics.cacheHitRate * (this.metrics.totalRequests - 1) + 1) / 
        this.metrics.totalRequests
      );
    }
  }

  // Analyze provider performance and switch if needed
  private analyzeProviderPerformance(): void {
    if (!this.config.autoFailover) return;

    const currentProvider = this.config.primaryProvider;
    const currentPerformance = this.performanceData.get(currentProvider);

    if (!currentPerformance || currentPerformance.length < 10) return;

    const avgPerformance = currentPerformance.reduce((a, b) => a + b, 0) / currentPerformance.length;

    if (avgPerformance > this.config.performanceThreshold) {
      console.warn(`🚨 CDN performance degraded: ${avgPerformance}ms > ${this.config.performanceThreshold}ms`);
      this.switchToFallbackProvider();
    }
  }

  // Switch to fallback provider
  private switchToFallbackProvider(): void {
    const fallbackProviders = this.config.fallbackProviders;
    
    for (const provider of fallbackProviders) {
      const performance = this.performanceData.get(provider);
      
      if (performance && performance.length > 0) {
        const avgPerformance = performance.reduce((a, b) => a + b, 0) / performance.length;
        
        if (avgPerformance < this.config.performanceThreshold) {
          console.log(`🔄 Switching to fallback CDN provider: ${provider}`);
          this.config.primaryProvider = provider;
          this.saveConfig();
          return;
        }
      }
    }

    console.warn('⚠️ No suitable fallback CDN provider found');
  }

  // Optimize asset URL for CDN delivery
  public optimizeAssetUrl(
    originalUrl: string,
    options: {
      width?: number;
      height?: number;
      quality?: number;
      format?: string;
      compression?: boolean;
    } = {}
  ): string {
    const provider = this.providers.get(this.config.primaryProvider);
    
    if (!provider) {
      return originalUrl;
    }

    // For local assets, use CDN
    if (originalUrl.startsWith('/') || originalUrl.startsWith('./')) {
      let optimizedUrl = `${provider.baseUrl}${originalUrl}`;
      
      // Add optimization parameters based on provider capabilities
      if (provider.features.imageOptimization && this.isImageUrl(originalUrl)) {
        optimizedUrl = this.addImageOptimization(optimizedUrl, options);
      }
      
      return optimizedUrl;
    }

    // For external assets, apply provider-specific optimizations
    return this.applyProviderOptimizations(originalUrl, options);
  }

  // Check if URL is an image
  private isImageUrl(url: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.avif', '.svg'];
    return imageExtensions.some(ext => url.toLowerCase().includes(ext));
  }

  // Add image optimization parameters
  private addImageOptimization(url: string, options: any): string {
    const params = new URLSearchParams();
    
    if (options.width) params.set('w', options.width.toString());
    if (options.height) params.set('h', options.height.toString());
    if (options.quality) params.set('q', options.quality.toString());
    if (options.format) params.set('f', options.format);
    if (options.compression) params.set('c', '1');

    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}${params.toString()}`;
  }

  // Apply provider-specific optimizations
  private applyProviderOptimizations(url: string, options: any): string {
    const provider = this.config.primaryProvider;

    switch (provider) {
      case 'cloudflare':
        return this.applyCloudflareOptimizations(url, options);
      case 'aws-cloudfront':
        return this.applyCloudFrontOptimizations(url, options);
      case 'fastly':
        return this.applyFastlyOptimizations(url, options);
      default:
        return url;
    }
  }

  // Cloudflare-specific optimizations
  private applyCloudflareOptimizations(url: string, options: any): string {
    // Cloudflare Polish and other optimizations
    const optimizedUrl = url.replace('http://', 'https://'); // Force HTTPS
    
    if (this.isImageUrl(url) && options.quality) {
      // Cloudflare Polish automatic optimization
      return `${optimizedUrl}?cf_polish=lossy&cf_quality=${options.quality}`;
    }
    
    return optimizedUrl;
  }

  // CloudFront-specific optimizations
  private applyCloudFrontOptimizations(url: string, options: any): string {
    // AWS CloudFront optimizations
    if (this.isImageUrl(url)) {
      const params = new URLSearchParams();
      if (options.width) params.set('w', options.width.toString());
      if (options.height) params.set('h', options.height.toString());
      
      const separator = url.includes('?') ? '&' : '?';
      return `${url}${separator}${params.toString()}`;
    }
    
    return url;
  }

  // Fastly-specific optimizations
  private applyFastlyOptimizations(url: string, options: any): string {
    // Fastly Image Optimizer
    if (this.isImageUrl(url) && options.width) {
      return `${url}?width=${options.width}&optimize=medium`;
    }
    
    return url;
  }

  // Preload critical assets via CDN
  public preloadCriticalAssets(assets: string[]): void {
    assets.forEach(asset => {
      const optimizedUrl = this.optimizeAssetUrl(asset);
      
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = optimizedUrl;
      
      if (this.isImageUrl(asset)) {
        link.as = 'image';
      } else if (asset.includes('.css')) {
        link.as = 'style';
      } else if (asset.includes('.js')) {
        link.as = 'script';
      }
      
      document.head.appendChild(link);
    });

    console.log(`🚀 Preloaded ${assets.length} critical assets via CDN`);
  }

  // Get optimal CDN provider for user location
  public getOptimalProvider(): string {
    if (!this.config.geoOptimization || !this.userLocation) {
      return this.config.primaryProvider;
    }

    // Simple geo-optimization logic
    const userCountry = this.userLocation.country;
    
    if (['US', 'CA', 'MX'].includes(userCountry)) {
      return 'aws-cloudfront'; // Better for Americas
    } else if (['GB', 'DE', 'FR', 'IT', 'ES'].includes(userCountry)) {
      return 'fastly'; // Better for Europe
    } else {
      return 'cloudflare'; // Global coverage
    }
  }

  // Get CDN metrics
  public getMetrics(): CDNMetrics {
    // Calculate average response time
    let totalTime = 0;
    let totalRequests = 0;
    
    for (const [provider, times] of this.performanceData) {
      totalTime += times.reduce((a, b) => a + b, 0);
      totalRequests += times.length;
    }
    
    this.metrics.avgResponseTime = totalRequests > 0 ? totalTime / totalRequests : 0;
    
    return { ...this.metrics };
  }

  // Update configuration
  public updateConfig(newConfig: Partial<CDNConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();
    console.log('🌐 CDN configuration updated');
  }

  // Get current configuration
  public getConfig(): CDNConfig {
    return { ...this.config };
  }

  // Get available providers
  public getProviders(): CDNProvider[] {
    return Array.from(this.providers.values());
  }

  // Save configuration
  private saveConfig(): void {
    try {
      localStorage.setItem('cdn_config', JSON.stringify(this.config));
    } catch (error) {
      console.error('Failed to save CDN config:', error);
    }
  }

  // Load configuration
  private loadConfig(): CDNConfig {
    try {
      const saved = localStorage.getItem('cdn_config');
      return saved ? { ...this.defaultConfig, ...JSON.parse(saved) } : this.defaultConfig;
    } catch (error) {
      console.error('Failed to load CDN config:', error);
      return this.defaultConfig;
    }
  }

  // Reset metrics
  public resetMetrics(): void {
    this.metrics = this.initializeMetrics();
    this.performanceData.clear();
    console.log('🗑️ CDN metrics reset');
  }
}

// Export singleton instance
export const cdnService = CDNService.getInstance();
