import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

// Hook for responsive breakpoints
export const useResponsive = () => {
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800,
  });

  const [breakpoint, setBreakpoint] = useState('desktop');

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setScreenSize({ width, height });
      
      if (width < 640) {
        setBreakpoint('mobile');
      } else if (width < 768) {
        setBreakpoint('sm');
      } else if (width < 1024) {
        setBreakpoint('tablet');
      } else if (width < 1280) {
        setBreakpoint('laptop');
      } else {
        setBreakpoint('desktop');
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Call once to set initial values

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return {
    ...screenSize,
    breakpoint,
    isMobile: breakpoint === 'mobile',
    isTablet: breakpoint === 'tablet' || breakpoint === 'sm',
    isDesktop: breakpoint === 'laptop' || breakpoint === 'desktop',
  };
};

// Responsive Container Component
export const ResponsiveContainer: React.FC<{
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
}> = ({ children, className = '', maxWidth = 'xl' }) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-7xl',
    '2xl': 'max-w-8xl',
    full: 'max-w-full',
  };

  return (
    <div className={`container mx-auto px-4 sm:px-6 lg:px-8 ${maxWidthClasses[maxWidth]} ${className}`}>
      {children}
    </div>
  );
};

// Responsive Grid Component
export const ResponsiveGrid: React.FC<{
  children: React.ReactNode;
  cols?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: number;
  className?: string;
}> = ({ 
  children, 
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 6,
  className = '' 
}) => {
  const gridClasses = `grid gap-${gap} grid-cols-${cols.mobile} md:grid-cols-${cols.tablet} lg:grid-cols-${cols.desktop}`;
  
  return (
    <div className={`${gridClasses} ${className}`}>
      {children}
    </div>
  );
};

// Responsive Text Component
export const ResponsiveText: React.FC<{
  children: React.ReactNode;
  size?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  };
  className?: string;
}> = ({ 
  children, 
  size = { mobile: 'text-2xl', tablet: 'text-4xl', desktop: 'text-6xl' },
  className = '' 
}) => {
  const textClasses = `${size.mobile} md:${size.tablet} lg:${size.desktop}`;
  
  return (
    <div className={`${textClasses} ${className}`}>
      {children}
    </div>
  );
};

// Mobile Navigation Component
export const MobileNav: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  links: Array<{ href: string; label: string; }>;
}> = ({ isOpen, onClose, links }) => {
  const { isMobile } = useResponsive();

  if (!isMobile) return null;

  return (
    <motion.div
      initial={{ opacity: 0, x: '100%' }}
      animate={{ 
        opacity: isOpen ? 1 : 0, 
        x: isOpen ? '0%' : '100%' 
      }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className="fixed inset-0 bg-black/95 backdrop-blur-lg z-50 lg:hidden"
    >
      <div className="flex flex-col h-full">
        <div className="flex justify-end p-6">
          <button
            onClick={onClose}
            className="p-2 text-white hover:text-purple-400 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <nav className="flex-1 flex flex-col justify-center px-6">
          {links.map((link, index) => (
            <motion.a
              key={link.href}
              href={link.href}
              onClick={onClose}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1, duration: 0.3 }}
              className="text-2xl font-bold text-white hover:text-purple-400 py-4 border-b border-white/10 transition-colors"
            >
              {link.label}
            </motion.a>
          ))}
        </nav>
      </div>
    </motion.div>
  );
};

// Touch-friendly Button Component
export const TouchButton: React.FC<{
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}> = ({ 
  children, 
  onClick, 
  variant = 'primary', 
  size = 'md',
  className = '' 
}) => {
  const { isMobile } = useResponsive();
  
  const baseClasses = 'font-bold rounded-2xl transition-all duration-300 transform active:scale-95';
  
  const sizeClasses = {
    sm: isMobile ? 'px-4 py-3 text-sm' : 'px-3 py-2 text-sm',
    md: isMobile ? 'px-6 py-4 text-base' : 'px-4 py-3 text-base',
    lg: isMobile ? 'px-8 py-5 text-lg' : 'px-6 py-4 text-lg',
  };
  
  const variantClasses = {
    primary: 'bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white shadow-lg hover:shadow-xl',
    secondary: 'bg-white/10 hover:bg-white/20 text-white border border-white/20 hover:border-white/30',
    ghost: 'text-white hover:bg-white/10',
  };

  return (
    <motion.button
      onClick={onClick}
      className={`${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      style={{ minHeight: isMobile ? '48px' : 'auto' }} // Touch target size
    >
      {children}
    </motion.button>
  );
};

// Responsive Image Component
export const ResponsiveImage: React.FC<{
  src: string;
  alt: string;
  className?: string;
  sizes?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  };
}> = ({ 
  src, 
  alt, 
  className = '',
  sizes = { mobile: '100vw', tablet: '50vw', desktop: '33vw' }
}) => {
  return (
    <picture>
      <source media="(min-width: 1024px)" srcSet={src} sizes={sizes.desktop} />
      <source media="(min-width: 768px)" srcSet={src} sizes={sizes.tablet} />
      <img 
        src={src} 
        alt={alt} 
        className={`w-full h-auto ${className}`}
        sizes={sizes.mobile}
        loading="lazy"
      />
    </picture>
  );
};

// Responsive Spacing Component
export const ResponsiveSpacing: React.FC<{
  children: React.ReactNode;
  padding?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  };
  margin?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  };
  className?: string;
}> = ({ 
  children, 
  padding = { mobile: 'p-4', tablet: 'p-6', desktop: 'p-8' },
  margin = { mobile: 'm-0', tablet: 'm-0', desktop: 'm-0' },
  className = '' 
}) => {
  const spacingClasses = `${padding.mobile} md:${padding.tablet} lg:${padding.desktop} ${margin.mobile} md:${margin.tablet} lg:${margin.desktop}`;
  
  return (
    <div className={`${spacingClasses} ${className}`}>
      {children}
    </div>
  );
};

// Performance optimized component for mobile
export const MobileOptimized: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback }) => {
  const { isMobile } = useResponsive();
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    // Delay rendering of complex components on mobile
    if (isMobile) {
      const timer = setTimeout(() => setShouldRender(true), 100);
      return () => clearTimeout(timer);
    } else {
      setShouldRender(true);
    }
  }, [isMobile]);

  if (isMobile && !shouldRender) {
    return fallback ? <>{fallback}</> : <div className="animate-pulse bg-gray-800 rounded-lg h-32" />;
  }

  return <>{children}</>;
};
