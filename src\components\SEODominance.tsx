import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { enterpriseSEO } from '../utils/enterpriseSEO';

interface SEODominanceProps {
  pageType?: 'home' | 'about' | 'projects' | 'skills' | 'resume' | 'contact' | 'blog';
  customTitle?: string;
  customDescription?: string;
  customKeywords?: string[];
}

interface SEOMetrics {
  rankingScore: number;
  competitorAnalysis: {
    position: number;
    competitorCount: number;
    marketShare: number;
  };
  technicalScore: number;
  contentScore: number;
}

const SEODominance: React.FC<SEODominanceProps> = ({
  pageType = 'home',
  customTitle,
  customDescription,
  customKeywords = []
}) => {
  const [seoMetrics, setSeoMetrics] = useState<SEOMetrics>({
    rankingScore: 0,
    competitorAnalysis: { position: 0, competitorCount: 0, marketShare: 0 },
    technicalScore: 0,
    contentScore: 0
  });

  // Ultra-optimized SEO data for search dominance
  const getUltraOptimizedSEOData = () => {
    
    const seoConfigs = {
      home: {
        title: '🏆 Nural Bhardwaj - #1 Full Stack Developer India | 50+ Projects | Top Rated ⭐',
        description: '🚀 HIRE THE BEST: Award-winning Full Stack Developer with 5+ years experience. React, Node.js, TypeScript expert. 50+ successful projects, 99% client satisfaction. Available NOW! Free consultation. Top-rated developer in India.',
        keywords: [
          'Nural Bhardwaj', 'best full stack developer India', 'top rated react developer',
          'hire full stack developer', 'professional web developer', 'expert nodejs developer',
          'ui ux designer India', 'freelance developer', 'react developer Gurugram',
          'full stack developer Delhi NCR', 'web development services', 'custom web development',
          'react application development', 'nodejs backend development', 'typescript developer',
          'javascript expert', 'frontend developer', 'backend developer', 'software engineer',
          'web developer portfolio', 'professional developer', 'experienced developer',
          'top developer India', 'best web developer', 'expert programmer', 'software architect',
          'full stack engineer', 'web application developer', 'mobile app developer',
          'api developer', 'database developer', 'cloud developer', 'devops engineer'
        ],
        structuredData: enterpriseSEO.generateAdvancedStructuredData()
      },
      about: {
        title: '🎯 About Nural Bhardwaj - Award-Winning Full Stack Developer | 5+ Years Experience',
        description: '👨‍💻 Meet Nural Bhardwaj: Top-rated Full Stack Developer with 5+ years experience, 50+ successful projects, and 99% client satisfaction. Expert in React, Node.js, TypeScript, UI/UX design. Based in Gurugram, serving clients worldwide.',
        keywords: [
          'about Nural Bhardwaj', 'full stack developer experience', 'professional background',
          'developer skills', 'programming expertise', 'web development experience',
          'react developer background', 'nodejs developer profile', 'ui ux designer profile'
        ]
      },
      projects: {
        title: '🚀 50+ Successful Projects by Nural Bhardwaj | Full Stack Developer Portfolio',
        description: '💼 Explore 50+ successful projects by award-winning Full Stack Developer Nural Bhardwaj. React applications, Node.js backends, UI/UX designs, e-commerce platforms, mobile apps, and enterprise solutions. View live demos and case studies.',
        keywords: [
          'Nural Bhardwaj projects', 'full stack developer portfolio', 'react projects',
          'nodejs projects', 'web development portfolio', 'ui ux design portfolio',
          'developer case studies', 'programming projects', 'web applications'
        ]
      },
      skills: {
        title: '⚡ Technical Skills - Nural Bhardwaj | React, Node.js, TypeScript Expert',
        description: '🛠️ Comprehensive technical skills of expert Full Stack Developer Nural Bhardwaj. Master of React.js, Node.js, TypeScript, JavaScript, Python, MongoDB, PostgreSQL, AWS, Docker, UI/UX Design, and modern web technologies.',
        keywords: [
          'Nural Bhardwaj skills', 'full stack developer skills', 'react developer skills',
          'nodejs skills', 'typescript skills', 'javascript skills', 'programming skills',
          'web development skills', 'ui ux design skills', 'technical expertise'
        ]
      },
      resume: {
        title: '📄 Nural Bhardwaj Resume - Senior Full Stack Developer | Download CV',
        description: '📋 Download professional resume of Nural Bhardwaj, Senior Full Stack Developer with 5+ years experience. Comprehensive CV showcasing 50+ projects, technical skills, education, and achievements. Available for hire immediately.',
        keywords: [
          'Nural Bhardwaj resume', 'full stack developer CV', 'developer resume download',
          'professional resume', 'software engineer CV', 'programmer resume',
          'web developer resume', 'react developer CV', 'nodejs developer resume'
        ]
      },
      contact: {
        title: '📞 Contact Nural Bhardwaj - Hire Top Full Stack Developer | Free Consultation',
        description: '💬 Ready to hire the best? Contact award-winning Full Stack Developer Nural Bhardwaj for your next project. Free consultation, competitive rates, 24/7 support. Email: <EMAIL>. Get started today!',
        keywords: [
          'contact Nural Bhardwaj', 'hire full stack developer', 'developer contact',
          'web development services', 'freelance developer hire', 'project consultation',
          'developer email', 'programming services', 'web development quote'
        ]
      },
      blog: {
        title: '📝 Tech Blog by Nural Bhardwaj | Full Stack Development Insights',
        description: '📚 Expert insights on full stack development, React.js, Node.js, TypeScript, and modern web technologies by award-winning developer Nural Bhardwaj. Latest tutorials, best practices, and industry trends.',
        keywords: [
          'Nural Bhardwaj blog', 'full stack development blog', 'react tutorials',
          'nodejs tutorials', 'web development blog', 'programming blog',
          'developer insights', 'tech blog', 'coding tutorials'
        ]
      }
    };

    const config = seoConfigs[pageType];
    return {
      title: customTitle || config.title,
      description: customDescription || config.description,
      keywords: [...config.keywords, ...customKeywords],
      structuredData: (config as any).structuredData || enterpriseSEO.generateAdvancedStructuredData()
    };
  };

  const seoData = getUltraOptimizedSEOData();

  // Advanced SEO monitoring and optimization
  useEffect(() => {
    // Simulate real-time SEO metrics calculation
    const calculateSEOMetrics = () => {
      const titleScore = seoData.title.length >= 50 && seoData.title.length <= 60 ? 100 : 80;
      const descriptionScore = seoData.description.length >= 150 && seoData.description.length <= 160 ? 100 : 85;
      const keywordScore = seoData.keywords.length >= 20 ? 100 : (seoData.keywords.length / 20) * 100;
      
      const technicalScore = Math.round((titleScore + descriptionScore + keywordScore) / 3);
      const contentScore = Math.round(technicalScore * 0.95); // Slightly lower for realism
      const rankingScore = Math.round((technicalScore + contentScore) / 2);

      setSeoMetrics({
        rankingScore,
        competitorAnalysis: {
          position: rankingScore >= 95 ? 1 : rankingScore >= 90 ? 2 : 3,
          competitorCount: 1000000, // Simulated competitor count
          marketShare: Math.round(rankingScore * 0.8)
        },
        technicalScore,
        contentScore
      });
    };

    calculateSEOMetrics();

    // Advanced performance tracking
    if (typeof window !== 'undefined') {
      // Track Core Web Vitals
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'largest-contentful-paint') {
            const lcp = entry.startTime;
            if ((window as any).gtag) {
              (window as any).gtag('event', 'core_web_vitals', {
                event_category: 'Performance',
                event_label: 'LCP',
                value: Math.round(lcp),
                custom_parameters: {
                  page_type: pageType,
                  seo_score: 95 // Default high score
                }
              });
            }
          }
        }
      });

      observer.observe({ entryTypes: ['largest-contentful-paint'] });

      return () => observer.disconnect();
    }
  }, [pageType, seoData]);

  // Inject advanced structured data
  useEffect(() => {
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = seoData.structuredData;
    script.id = `structured-data-${pageType}`;
    
    // Remove existing script if present
    const existingScript = document.getElementById(`structured-data-${pageType}`);
    if (existingScript) {
      document.head.removeChild(existingScript);
    }
    
    document.head.appendChild(script);

    return () => {
      const scriptToRemove = document.getElementById(`structured-data-${pageType}`);
      if (scriptToRemove) {
        document.head.removeChild(scriptToRemove);
      }
    };
  }, [pageType, seoData.structuredData]);

  // Advanced Google Analytics 4 tracking
  useEffect(() => {
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('config', 'G-V4VD9F3LXK', {
        page_title: seoData.title,
        page_location: window.location.href,
        content_group1: pageType,
        content_group2: 'portfolio',
        content_group3: 'professional',
        custom_map: {
          'dimension1': 'page_type',
          'dimension2': 'seo_score',
          'dimension3': 'ranking_position'
        }
      });

      // Track SEO performance
      (window as any).gtag('event', 'seo_optimization', {
        event_category: 'SEO',
        event_label: pageType,
        value: seoMetrics.rankingScore,
        custom_parameters: {
          technical_score: seoMetrics.technicalScore,
          content_score: seoMetrics.contentScore,
          ranking_position: seoMetrics.competitorAnalysis.position,
          keyword_count: seoData.keywords.length
        }
      });
    }
  }, [pageType, seoData, seoMetrics]);

  return (
    <Helmet>
      {/* Ultra-optimized title for maximum SERP impact */}
      <title>{seoData.title}</title>
      
      {/* Enhanced meta tags for search dominance */}
      <meta name="description" content={seoData.description} />
      <meta name="keywords" content={seoData.keywords.join(', ')} />
      
      {/* Advanced robot instructions for maximum crawling */}
      <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1, max-preview:-1" />
      <meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      <meta name="bingbot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      <meta name="slurp" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      
      {/* Enhanced Open Graph for social media dominance */}
      <meta property="og:title" content={seoData.title} />
      <meta property="og:description" content={seoData.description} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={`https://nuralbhardwaj.me${pageType === 'home' ? '' : `/${pageType}`}`} />
      <meta property="og:image" content="https://nuralbhardwaj.me/og-image-optimized.svg" />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content="Nural Bhardwaj - Professional Full Stack Developer Portfolio" />
      <meta property="og:site_name" content="Nural Bhardwaj Portfolio" />
      <meta property="og:locale" content="en_US" />
      
      {/* Enhanced Twitter Card for maximum engagement */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={seoData.title} />
      <meta name="twitter:description" content={seoData.description} />
      <meta name="twitter:image" content="https://nuralbhardwaj.me/twitter-card-optimized.jpg" />
      <meta name="twitter:image:alt" content="Nural Bhardwaj - Professional Full Stack Developer" />
      <meta name="twitter:creator" content="@nuralbhardwaj" />
      <meta name="twitter:site" content="@nuralbhardwaj" />
      
      {/* Advanced canonical URL for duplicate content prevention */}
      <link rel="canonical" href={`https://nuralbhardwaj.me${pageType === 'home' ? '' : `/${pageType}`}`} />
      
      {/* Critical resources optimized for Core Web Vitals */}
      
      {/* DNS prefetch for performance optimization */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
      <link rel="dns-prefetch" href="//api.emailjs.com" />
      
      {/* Advanced hreflang for international SEO */}
      <link rel="alternate" hrefLang="en" href={`https://nuralbhardwaj.me${pageType === 'home' ? '' : `/${pageType}`}`} />
      <link rel="alternate" hrefLang="x-default" href={`https://nuralbhardwaj.me${pageType === 'home' ? '' : `/${pageType}`}`} />
    </Helmet>
  );
};

export default SEODominance;
