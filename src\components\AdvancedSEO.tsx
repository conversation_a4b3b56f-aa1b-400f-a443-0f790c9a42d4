import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

interface AdvancedSEOProps {
  pageType?: 'home' | 'about' | 'projects' | 'blog' | 'contact' | 'resume';
  title?: string;
  description?: string;
  keywords?: string[];
  canonicalUrl?: string;
  ogImage?: string;
  structuredData?: any;
}

const AdvancedSEO: React.FC<AdvancedSEOProps> = ({
  pageType = 'home',
  title,
  description,
  keywords = [],
  canonicalUrl,
  ogImage,
  structuredData
}) => {
  // Advanced SEO data based on page type
  const getPageSEOData = () => {
    const baseUrl = 'https://nuralbhardwaj.me';
    
    const seoData = {
      home: {
        title: '🏆 Nural Bhardwaj - #1 Full Stack Developer & UI/UX Designer | 50+ Projects | Hire Now',
        description: '⭐ TOP-RATED Full Stack Developer with 5+ years experience. React, Node.js, TypeScript expert. 50+ successful projects delivered. 99% client satisfaction. Available for hire! 🚀 Get free consultation.',
        keywords: [
          'Nural Bhardwaj', 'Full Stack Developer', 'UI/UX Designer', 'React Developer', 'Node.js Developer',
          'TypeScript Developer', 'JavaScript Developer', 'Web Developer', 'Frontend Developer', 'Backend Developer',
          'Full Stack Developer India', 'React Developer Gurugram', 'Web Developer Haryana', 'UI/UX Designer Delhi NCR',
          'Best Full Stack Developer', 'Top React Developer', 'Expert JavaScript Developer', 'Professional Web Developer',
          'Custom Web Development', 'React Application Development', 'E-commerce Development', 'Mobile App Development',
          'API Development', 'Database Design', 'Cloud Computing', 'DevOps Services', 'Website Optimization',
          'React.js', 'Next.js', 'Vue.js', 'Angular', 'Express.js', 'MongoDB', 'PostgreSQL', 'AWS', 'Docker',
          'Hire Full Stack Developer', 'Freelance Developer', 'Remote Developer', 'Software Engineer Portfolio',
          'Web Development Services', 'Custom Software Development', 'Enterprise Web Applications'
        ],
        canonical: baseUrl,
        ogImage: `${baseUrl}/og-home-optimized.jpg`
      },
      about: {
        title: 'About Nural Bhardwaj - Senior Full Stack Developer | 5+ Years Experience | 50+ Projects',
        description: 'Meet Nural Bhardwaj - Senior Full Stack Developer & UI/UX Designer with 5+ years experience. Specialized in React, Node.js, TypeScript. 50+ successful projects, 99% client satisfaction rate.',
        keywords: [
          'Nural Bhardwaj About', 'Senior Full Stack Developer', 'Developer Experience', 'Programming Skills',
          'Web Development Expert', 'React Specialist', 'Node.js Expert', 'TypeScript Professional',
          'UI/UX Design Skills', 'Software Engineer Background', 'Developer Portfolio', 'Programming Journey'
        ],
        canonical: `${baseUrl}/#about`,
        ogImage: `${baseUrl}/og-about-optimized.jpg`
      },
      projects: {
        title: 'Projects Portfolio - 50+ Successful Web Applications | React, Node.js, TypeScript',
        description: 'Explore 50+ successful projects by Nural Bhardwaj. Full stack web applications, e-commerce platforms, mobile apps, and enterprise solutions. React, Node.js, TypeScript expertise.',
        keywords: [
          'Web Development Projects', 'React Projects', 'Node.js Applications', 'TypeScript Projects',
          'Full Stack Applications', 'E-commerce Websites', 'Mobile Apps', 'Enterprise Solutions',
          'Portfolio Projects', 'Web Application Development', 'Custom Software Projects'
        ],
        canonical: `${baseUrl}/#projects`,
        ogImage: `${baseUrl}/og-projects-optimized.jpg`
      },
      blog: {
        title: 'Tech Blog - Web Development Insights | React, Node.js, TypeScript Tutorials',
        description: 'Latest insights on web development, React tutorials, Node.js guides, TypeScript tips, and industry best practices. Learn from a senior full stack developer with 5+ years experience.',
        keywords: [
          'Web Development Blog', 'React Tutorials', 'Node.js Guides', 'TypeScript Tips',
          'JavaScript Articles', 'Programming Blog', 'Developer Insights', 'Tech Articles',
          'Web Development Tips', 'Coding Best Practices', 'Software Development Blog'
        ],
        canonical: `${baseUrl}/#blog`,
        ogImage: `${baseUrl}/og-blog-optimized.jpg`
      },
      contact: {
        title: 'Hire Nural Bhardwaj - Full Stack Developer | Get Free Consultation | Quick Response',
        description: 'Ready to start your project? Contact Nural Bhardwaj for professional full stack development services. Free consultation, quick response, competitive rates. Available for freelance & contract work.',
        keywords: [
          'Hire Full Stack Developer', 'Contact Nural Bhardwaj', 'Web Development Services',
          'Freelance Developer', 'Contract Developer', 'Project Consultation', 'Development Quote',
          'React Developer for Hire', 'Node.js Developer Services', 'Custom Web Development'
        ],
        canonical: `${baseUrl}/#contact`,
        ogImage: `${baseUrl}/og-contact-optimized.jpg`
      },
      resume: {
        title: 'Resume - Nural Bhardwaj | Senior Full Stack Developer | Download CV PDF',
        description: 'Download Nural Bhardwaj\'s professional resume. Senior Full Stack Developer with 5+ years experience, 50+ projects completed. Skills: React, Node.js, TypeScript, and more.',
        keywords: [
          'Nural Bhardwaj Resume', 'Full Stack Developer CV', 'Developer Resume Download',
          'Software Engineer Resume', 'React Developer CV', 'Node.js Developer Resume',
          'Professional Developer Resume', 'Senior Developer CV', 'Programming Resume'
        ],
        canonical: `${baseUrl}/#resume`,
        ogImage: `${baseUrl}/og-resume-optimized.jpg`
      }
    };

    return seoData[pageType];
  };

  const pageSEO = getPageSEOData();
  const finalTitle = title || pageSEO.title;
  const finalDescription = description || pageSEO.description;
  const finalKeywords = keywords.length > 0 ? keywords : pageSEO.keywords;
  const finalCanonical = canonicalUrl || pageSEO.canonical;
  const finalOgImage = ogImage || pageSEO.ogImage;

  // Generate advanced structured data
  const generateAdvancedStructuredData = () => {
    const baseStructuredData = {
      "@context": "https://schema.org",
      "@graph": [
        {
          "@type": "Person",
          "@id": "https://nuralbhardwaj.me#person",
          "name": "Nural Bhardwaj",
          "givenName": "Nural",
          "familyName": "Bhardwaj",
          "jobTitle": "Senior Full Stack Developer and UI/UX Designer",
          "description": "Senior Full Stack Developer with 5+ years experience specializing in React, Node.js, TypeScript, and modern web technologies. 50+ successful projects delivered with 99% client satisfaction.",
          "url": "https://nuralbhardwaj.me",
          "image": "https://nuralbhardwaj.me/profile-image.jpg",
          "email": "<EMAIL>",
          "telephone": "+91-7404814726",
          "address": {
            "@type": "PostalAddress",
            "streetAddress": "Sector 99A",
            "addressLocality": "Gurugram",
            "addressRegion": "Haryana",
            "postalCode": "122505",
            "addressCountry": "IN"
          },
          "sameAs": [
            "https://github.com/NuralBhardwaj",
            "https://linkedin.com/in/nural-bhardwaj",
            "https://twitter.com/nuralbhardwaj",
            "https://stackoverflow.com/users/nuralbhardwaj",
            "https://dev.to/nuralbhardwaj",
            "https://medium.com/@nuralbhardwaj"
          ],
          "knowsAbout": [
            "React.js", "Node.js", "TypeScript", "JavaScript", "HTML5", "CSS3",
            "MongoDB", "PostgreSQL", "AWS", "Docker", "Kubernetes", "GraphQL",
            "REST API", "Microservices", "UI/UX Design", "Responsive Design"
          ],
          "hasOccupation": {
            "@type": "Occupation",
            "name": "Full Stack Developer",
            "occupationLocation": {
              "@type": "City",
              "name": "Gurugram, Haryana, India"
            },
            "estimatedSalary": {
              "@type": "MonetaryAmountDistribution",
              "name": "base",
              "currency": "INR",
              "duration": "P1H",
              "minValue": 2000,
              "maxValue": 5000
            }
          },
          "award": [
            "Top Rated Freelancer",
            "99% Client Satisfaction Rate",
            "50+ Successful Projects"
          ]
        },
        {
          "@type": "WebSite",
          "@id": "https://nuralbhardwaj.me#website",
          "url": "https://nuralbhardwaj.me",
          "name": "Nural Bhardwaj - Professional Developer Portfolio",
          "description": "Professional portfolio of Nural Bhardwaj, Senior Full Stack Developer & UI/UX Designer. Showcasing 50+ successful projects and cutting-edge web development expertise.",
          "publisher": {
            "@id": "https://nuralbhardwaj.me#person"
          },
          "inLanguage": "en-US",
          "copyrightYear": new Date().getFullYear(),
          "copyrightHolder": {
            "@id": "https://nuralbhardwaj.me#person"
          }
        },
        {
          "@type": "ProfessionalService",
          "@id": "https://nuralbhardwaj.me#service",
          "name": "Nural Bhardwaj - Full Stack Development Services",
          "description": "Premium full stack development and UI/UX design services. Specializing in React, Node.js, TypeScript, and modern web technologies.",
          "provider": {
            "@id": "https://nuralbhardwaj.me#person"
          },
          "areaServed": [
            {
              "@type": "Country",
              "name": "India"
            },
            {
              "@type": "Country", 
              "name": "United States"
            },
            {
              "@type": "Country",
              "name": "United Kingdom"
            },
            {
              "@type": "Country",
              "name": "Canada"
            },
            {
              "@type": "Country",
              "name": "Australia"
            },
            {
              "@type": "Country",
              "name": "Germany"
            },
            {
              "@type": "Country",
              "name": "Japan"
            },
            {
              "@type": "Country",
              "name": "China"
            },
            {
              "@type": "Country",
              "name": "France"
            },
            {
              "@type": "Country",
              "name": "Italy"
            },
            {
              "@type": "Country",
              "name": "Spain"
            },
            {
              "@type": "Country",
              "name": "Brazil"
            },
            {
              "@type": "Country",
              "name": "Mexico"
            },
            {
              "@type": "Country",
              "name": "Russia"
            },
            {
              "@type": "Country",
              "name": "South Korea"
            },
            {
              "@type": "Country",
              "name": "Turkey"
            },
            {
              "@type": "Country",
              "name": "Indonesia"
            },
            {
              "@type": "Country",
              "name": "Pakistan"
            },
            {
              "@type": "Country",
              "name": "Bangladesh"
            },
            {
              "@type": "Country",
              "name": "Netherlands"
            },
            {
              "@type": "Country",
              "name": "Sweden"
            },
            {
              "@type": "Country",
              "name": "Norway"
            },
            {
              "@type": "Country",
              "name": "Denmark"
            },
            {
              "@type": "Country",
              "name": "Finland"
            },
            {
              "@type": "Country",
              "name": "Switzerland"
            },
            {
              "@type": "Country",
              "name": "Israel"
            },
            {
              "@type": "Country",
              "name": "Egypt"
            },
            {
              "@type": "Country",
              "name": "Saudi Arabia"
            },
            {
              "@type": "Country",
              "name": "United Arab Emirates"
            },
            {
              "@type": "Country",
              "name": "Qatar"
            },
            {
              "@type": "Country",
              "name": "Kuwait"
            },
            {
              "@type": "Country",
              "name": "Jordan"
            },
            {
              "@type": "Country",
              "name": "Lebanon"
            },
            {
              "@type": "Country",
              "name": "Morocco"
            },
            {
              "@type": "Country",
              "name": "Algeria"
            },
            {
              "@type": "Country",
              "name": "Tunisia"
            },
            {
              "@type": "Country",
              "name": "Iraq"
            },
            {
              "@type": "Country",
              "name": "Syria"
            },
            {
              "@type": "Country",
              "name": "Palestine"
            },
            {
              "@type": "Country",
              "name": "Iran"
            },
            {
              "@type": "Country",
              "name": "Afghanistan"
            },
            {
              "@type": "Country",
              "name": "Nepal"
            },
            {
              "@type": "Country",
              "name": "Bhutan"
            },
            {
              "@type": "Country",
              "name": "Maldives"
            }
          ],
          "serviceType": [
            "Full Stack Development",
            "Frontend Development", 
            "Backend Development",
            "UI/UX Design",
            "Web Application Development",
            "Mobile App Development",
            "API Development",
            "Database Design",
            "Cloud Computing",
            "DevOps Services"
          ],
          "priceRange": "$$$",
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.9",
            "reviewCount": "500",
            "bestRating": "5",
            "worstRating": "1"
          }
        }
      ]
    };

    return structuredData || baseStructuredData;
  };

  // Inject advanced SEO scripts
  useEffect(() => {
    // Google Analytics 4 Enhanced Ecommerce
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('config', 'G-V4VD9F3LXK', {
        page_title: finalTitle,
        page_location: finalCanonical,
        content_group1: pageType,
        custom_map: {
          'dimension1': 'page_type',
          'dimension2': 'user_type'
        }
      });
    }

    // Schema.org JSON-LD injection
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify(generateAdvancedStructuredData());
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, [pageType, finalTitle, finalCanonical]);

  return (
    <Helmet>
      {/* Enhanced Title with Rich Snippets */}
      <title>{finalTitle}</title>
      
      {/* Meta Tags for Maximum SEO Impact */}
      <meta name="description" content={finalDescription} />
      <meta name="keywords" content={finalKeywords.join(', ')} />
      <meta name="author" content="Nural Bhardwaj" />
      <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      <meta name="bingbot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={finalCanonical} />
      
      {/* Enhanced Open Graph Tags */}
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:image" content={finalOgImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={`${finalTitle} - Professional Portfolio`} />
      <meta property="og:url" content={finalCanonical} />
      <meta property="og:type" content="profile" />
      <meta property="og:site_name" content="Nural Bhardwaj - Professional Developer Portfolio" />
      <meta property="og:locale" content="en_US" />
      <meta property="profile:first_name" content="Nural" />
      <meta property="profile:last_name" content="Bhardwaj" />
      <meta property="profile:username" content="nuralbhardwaj" />
      
      {/* Enhanced Twitter Card Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@nuralbhardwaj" />
      <meta name="twitter:creator" content="@nuralbhardwaj" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={finalOgImage} />
      <meta name="twitter:image:alt" content={`${finalTitle} - Professional Portfolio`} />
      
      {/* Additional Rich Snippets */}
      <meta name="rating" content="5" />
      <meta name="coverage" content="Worldwide" />
      <meta name="distribution" content="Global" />
      <meta name="target" content="all" />
      <meta name="HandheldFriendly" content="True" />
      <meta name="MobileOptimized" content="320" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      <meta name="apple-mobile-web-app-title" content="Nural Bhardwaj" />
      
      {/* Geo Tags for Local SEO */}
      <meta name="geo.region" content="IN-HR" />
      <meta name="geo.placename" content="Gurugram, Haryana" />
      <meta name="geo.position" content="28.4595;77.0266" />
      <meta name="ICBM" content="28.4595, 77.0266" />
      
      {/* Language and Content Tags */}
      <meta httpEquiv="Content-Language" content="en-US" />
      <meta name="language" content="English" />
      <meta name="content-language" content="en-US" />
      
      {/* Performance and Security Headers */}
      <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="skype_toolbar" content="skype_toolbar_parser_compatible" />
      
      {/* Rich Snippets for Better SERP Display */}
      <meta name="thumbnail" content={finalOgImage} />
      <meta name="subject" content="Full Stack Development & UI/UX Design Services" />
      <meta name="copyright" content="Nural Bhardwaj" />
      <meta name="abstract" content={finalDescription} />
      <meta name="topic" content="Web Development, Software Engineering, UI/UX Design" />
      <meta name="summary" content={finalDescription} />
      <meta name="Classification" content="Business" />
      <meta name="designer" content="Nural Bhardwaj" />
      <meta name="owner" content="Nural Bhardwaj" />
      <meta name="url" content={finalCanonical} />
      <meta name="identifier-URL" content={finalCanonical} />
      <meta name="directory" content="submission" />
      <meta name="category" content="Technology, Web Development, Software Engineering" />
      <meta name="pagename" content={finalTitle} />
      <meta name="pagetopic" content="Full Stack Development Services" />
      <meta name="page-type" content={pageType} />
      
      {/* Preload Critical Resources */}
      <link rel="preload" href={finalOgImage} as="image" />
      
      {/* DNS Prefetch for Performance */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
    </Helmet>
  );
};

export default AdvancedSEO;
