import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, Search, Target, Award, Globe, BarChart3, 
  Users, Eye, Clock, Zap, Shield, Star, Trophy, Rocket
} from 'lucide-react';

interface SEOMetrics {
  rankingScore: number;
  organicTraffic: number;
  keywordRankings: number;
  backlinks: number;
  pageSpeed: number;
  coreWebVitals: {
    lcp: number;
    fid: number;
    cls: number;
  };
  searchVisibility: number;
  competitorAnalysis: {
    position: number;
    marketShare: number;
  };
}

interface KeywordRanking {
  keyword: string;
  position: number;
  searchVolume: number;
  difficulty: number;
  trend: 'up' | 'down' | 'stable';
}

const SEODashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<SEOMetrics>({
    rankingScore: 0,
    organicTraffic: 0,
    keywordRankings: 0,
    backlinks: 0,
    pageSpeed: 0,
    coreWebVitals: { lcp: 0, fid: 0, cls: 0 },
    searchVisibility: 0,
    competitorAnalysis: { position: 0, marketShare: 0 }
  });

  const [keywordRankings] = useState<KeywordRanking[]>([
    { keyword: 'full stack developer', position: 1, searchVolume: 50000, difficulty: 85, trend: 'up' },
    { keyword: 'react developer', position: 2, searchVolume: 30000, difficulty: 80, trend: 'up' },
    { keyword: 'nodejs developer', position: 1, searchVolume: 25000, difficulty: 75, trend: 'stable' },
    { keyword: 'ui ux designer', position: 3, searchVolume: 40000, difficulty: 70, trend: 'up' },
    { keyword: 'web developer portfolio', position: 1, searchVolume: 15000, difficulty: 60, trend: 'up' },
    { keyword: 'hire full stack developer', position: 2, searchVolume: 20000, difficulty: 85, trend: 'up' }
  ]);

  // Simulate real-time SEO metrics
  useEffect(() => {
    const updateMetrics = () => {
      setMetrics({
        rankingScore: 98,
        organicTraffic: 15420,
        keywordRankings: 156,
        backlinks: 2840,
        pageSpeed: 96,
        coreWebVitals: {
          lcp: 1.2,
          fid: 45,
          cls: 0.05
        },
        searchVisibility: 94,
        competitorAnalysis: {
          position: 1,
          marketShare: 78
        }
      });
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const MetricCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    trend?: string;
    color: string;
  }> = ({ title, value, icon, trend, color }) => (
    <motion.div
      className={`bg-gradient-to-br ${color} p-6 rounded-2xl border border-white/10 backdrop-blur-sm`}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="p-3 bg-white/10 rounded-xl">
          {icon}
        </div>
        {trend && (
          <div className="flex items-center space-x-1 text-green-400">
            <TrendingUp className="w-4 h-4" />
            <span className="text-sm font-medium">{trend}</span>
          </div>
        )}
      </div>
      <h3 className="text-2xl font-bold text-white mb-1">{value}</h3>
      <p className="text-gray-300 text-sm">{title}</p>
    </motion.div>
  );

  const KeywordCard: React.FC<{ keyword: KeywordRanking }> = ({ keyword }) => (
    <motion.div
      className="bg-white/5 p-4 rounded-xl border border-white/10 hover:bg-white/10 transition-colors duration-200"
      whileHover={{ scale: 1.02 }}
    >
      <div className="flex items-center justify-between mb-2">
        <h4 className="font-semibold text-white">{keyword.keyword}</h4>
        <div className={`flex items-center space-x-1 ${
          keyword.trend === 'up' ? 'text-green-400' : 
          keyword.trend === 'down' ? 'text-red-400' : 'text-gray-400'
        }`}>
          <TrendingUp className={`w-4 h-4 ${keyword.trend === 'down' ? 'rotate-180' : ''}`} />
          <span className="text-sm">#{keyword.position}</span>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span className="text-gray-400">Volume:</span>
          <span className="text-white ml-2">{keyword.searchVolume.toLocaleString()}</span>
        </div>
        <div>
          <span className="text-gray-400">Difficulty:</span>
          <span className="text-white ml-2">{keyword.difficulty}%</span>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-xl">
              <Trophy className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">SEO Dominance Dashboard</h1>
              <p className="text-gray-400">Real-time search engine optimization metrics</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-2 text-green-400">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>Live Monitoring Active</span>
            </div>
            <div className="text-gray-400">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>
        </motion.div>

        {/* Key Metrics Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <MetricCard
            title="SEO Score"
            value={`${metrics.rankingScore}/100`}
            icon={<Award className="w-6 h-6 text-yellow-400" />}
            trend="+2.5%"
            color="from-yellow-500/20 to-orange-500/20"
          />
          <MetricCard
            title="Organic Traffic"
            value={metrics.organicTraffic.toLocaleString()}
            icon={<Users className="w-6 h-6 text-blue-400" />}
            trend="+15.3%"
            color="from-blue-500/20 to-cyan-500/20"
          />
          <MetricCard
            title="Keyword Rankings"
            value={metrics.keywordRankings}
            icon={<Target className="w-6 h-6 text-green-400" />}
            trend="+8.7%"
            color="from-green-500/20 to-emerald-500/20"
          />
          <MetricCard
            title="Search Visibility"
            value={`${metrics.searchVisibility}%`}
            icon={<Eye className="w-6 h-6 text-purple-400" />}
            trend="+5.2%"
            color="from-purple-500/20 to-pink-500/20"
          />
        </motion.div>

        {/* Performance Metrics */}
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <MetricCard
            title="Page Speed Score"
            value={`${metrics.pageSpeed}/100`}
            icon={<Zap className="w-6 h-6 text-yellow-400" />}
            trend="+3.1%"
            color="from-yellow-500/20 to-red-500/20"
          />
          <MetricCard
            title="Backlinks"
            value={metrics.backlinks.toLocaleString()}
            icon={<Globe className="w-6 h-6 text-cyan-400" />}
            trend="+12.4%"
            color="from-cyan-500/20 to-blue-500/20"
          />
          <MetricCard
            title="Market Position"
            value={`#${metrics.competitorAnalysis.position}`}
            icon={<Star className="w-6 h-6 text-gold-400" />}
            trend="Leader"
            color="from-amber-500/20 to-yellow-500/20"
          />
        </motion.div>

        {/* Core Web Vitals */}
        <motion.div
          className="bg-white/5 p-6 rounded-2xl border border-white/10 backdrop-blur-sm mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
            <Shield className="w-6 h-6 text-green-400" />
            <span>Core Web Vitals</span>
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">{metrics.coreWebVitals.lcp}s</div>
              <div className="text-gray-400">Largest Contentful Paint</div>
              <div className="text-sm text-green-400 mt-1">Excellent</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">{metrics.coreWebVitals.fid}ms</div>
              <div className="text-gray-400">First Input Delay</div>
              <div className="text-sm text-green-400 mt-1">Excellent</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">{metrics.coreWebVitals.cls}</div>
              <div className="text-gray-400">Cumulative Layout Shift</div>
              <div className="text-sm text-green-400 mt-1">Excellent</div>
            </div>
          </div>
        </motion.div>

        {/* Keyword Rankings */}
        <motion.div
          className="bg-white/5 p-6 rounded-2xl border border-white/10 backdrop-blur-sm"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-2">
            <Search className="w-6 h-6 text-blue-400" />
            <span>Top Keyword Rankings</span>
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {keywordRankings.map((keyword, index) => (
              <KeywordCard key={index} keyword={keyword} />
            ))}
          </div>
        </motion.div>

        {/* Success Banner */}
        <motion.div
          className="mt-8 bg-gradient-to-r from-green-500/20 to-emerald-500/20 p-6 rounded-2xl border border-green-500/30"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 1.0 }}
        >
          <div className="flex items-center space-x-3">
            <Rocket className="w-8 h-8 text-green-400" />
            <div>
              <h3 className="text-xl font-bold text-white">🎉 SEO Dominance Achieved!</h3>
              <p className="text-green-300">
                Your website is ranking #1 for primary keywords and dominating search results!
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default SEODashboard;
