import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  LayoutDashboard,
  FolderOpen,
  BookOpen,
  User,
  Settings,
  LogOut,
  Plus,
  Eye,
  Edit,
  Trash2,
  Save,
  Download,
  Upload,
  FileText,
  Home,
  Search,
  Mail,
  TrendingUp,
  Palette,
  Bell,
  Shield,
  Zap,
  Cloud
} from 'lucide-react';
import { cmsService } from '../../services/cmsService';
import { Project, BlogPost } from '../../data/cmsData';
import toast from 'react-hot-toast';
import { SuccessToast, ErrorToast } from '../CustomToast';
import ProjectManager from './ProjectManager';
import BlogManager from './BlogManager';
import ProfileManager from './ProfileManager';
import SettingsManager from './SettingsManager';
import AnalyticsManager from './AnalyticsManager';
import HeroManager from './HeroManager';
import ContactManager from './ContactManager';
import SEOManager from './SEOManager';
import ThemeManager from './ThemeManager';
import NotificationManager from './NotificationManager';
import SecurityManager from './SecurityManager';
import PerformanceManager from './PerformanceManager';
import CloudSyncManager from './CloudSyncManager';
import SecureTokenSetup from './SecureTokenSetup';

interface AdminDashboardProps {
  onLogout: () => void;
}

type ActiveTab = 'dashboard' | 'hero' | 'projects' | 'blog' | 'profile' | 'contact' | 'seo' | 'theme' | 'analytics' | 'notifications' | 'security' | 'performance' | 'cloudsync' | 'settings';

const AdminDashboard: React.FC<AdminDashboardProps> = ({ onLogout }) => {
  const [activeTab, setActiveTab] = useState<ActiveTab>('dashboard');
  const [projects, setProjects] = useState<Project[]>([]);
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [showTokenSetup, setShowTokenSetup] = useState(false);
  const [stats, setStats] = useState({
    totalProjects: 0,
    featuredProjects: 0,
    totalBlogPosts: 0,
    publishedPosts: 0
  });

  useEffect(() => {
    loadData();

    // Check if token setup is needed
    const savedToken = localStorage.getItem('github_token');
    if (!savedToken) {
      setShowTokenSetup(true);
    }
  }, []);

  const loadData = () => {
    const projectsData = cmsService.getProjects();
    const blogData = cmsService.getBlogPosts();
    
    setProjects(projectsData);
    setBlogPosts(blogData);
    
    setStats({
      totalProjects: projectsData.length,
      featuredProjects: projectsData.filter(p => p.featured).length,
      totalBlogPosts: blogData.length,
      publishedPosts: blogData.filter(p => p.published).length
    });
  };

  const handleLogout = () => {
    cmsService.logout();
    toast(() => (
      <SuccessToast
        message="Logged out successfully!"
        icon={<LogOut className="w-5 h-5 text-green-400" />}
      />
    ));
    onLogout();
  };

  const exportData = () => {
    try {
      const data = cmsService.exportData();
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `portfolio-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast(() => (
        <SuccessToast
          message="Data exported successfully!"
          icon={<Download className="w-5 h-5 text-green-400" />}
        />
      ));
    } catch (error) {
      toast(() => (
        <ErrorToast
          message="Failed to export data"
          icon={<Download className="w-5 h-5 text-red-400" />}
        />
      ));
    }
  };

  const sidebarItems = [
    { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { id: 'hero', label: 'Hero Section', icon: Home },
    { id: 'projects', label: 'Projects', icon: FolderOpen },
    { id: 'blog', label: 'Blog', icon: BookOpen },
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'contact', label: 'Contact', icon: Mail },
    { id: 'seo', label: 'SEO', icon: Search },
    { id: 'theme', label: 'Theme', icon: Palette },
    { id: 'analytics', label: 'Analytics', icon: TrendingUp },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'performance', label: 'Performance', icon: Zap },
    { id: 'cloudsync', label: 'Cloud Sync', icon: Cloud },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  const StatCard = ({ title, value, subtitle, color }: { 
    title: string; 
    value: number; 
    subtitle: string; 
    color: string; 
  }) => (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10"
    >
      <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${color} flex items-center justify-center mb-4`}>
        <div className="w-6 h-6 bg-white rounded-full"></div>
      </div>
      <h3 className="text-2xl font-bold text-white mb-1">{value}</h3>
      <p className="text-gray-400 text-sm">{title}</p>
      <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
    </motion.div>
  );

  // Show token setup if needed
  if (showTokenSetup) {
    return (
      <SecureTokenSetup
        onTokenSet={() => {
          setShowTokenSetup(false);
          loadData();
        }}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex">
      {/* Sidebar */}
      <div className="w-64 bg-gradient-to-b from-white/10 to-white/5 backdrop-blur-xl border-r border-white/10 p-6">
        {/* Logo */}
        <div className="flex items-center space-x-3 mb-8">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-xl flex items-center justify-center">
            <LayoutDashboard className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">Admin Panel</h1>
            <p className="text-xs text-gray-400">Portfolio CMS</p>
          </div>
        </div>

        {/* Navigation */}
        <nav className="space-y-2">
          {sidebarItems.map((item) => (
            <button
              key={item.id}
              onClick={() => setActiveTab(item.id as ActiveTab)}
              className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 ${
                activeTab === item.id
                  ? 'bg-gradient-to-r from-purple-600/20 to-cyan-600/20 text-white border border-purple-500/30'
                  : 'text-gray-400 hover:text-white hover:bg-white/5'
              }`}
            >
              <item.icon className="w-5 h-5" />
              <span className="font-medium">{item.label}</span>
            </button>
          ))}
        </nav>

        {/* Logout Button */}
        <div className="absolute bottom-6 left-6 right-6">
          <button
            onClick={handleLogout}
            className="w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-all duration-200"
          >
            <LogOut className="w-5 h-5" />
            <span className="font-medium">Logout</span>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8 overflow-y-auto">
        <AnimatePresence mode="wait">
          {activeTab === 'dashboard' && (
            <motion.div
              key="dashboard"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-8"
            >
              {/* Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
                  <p className="text-gray-400">Welcome back! Here's your portfolio overview.</p>
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={exportData}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl transition-all duration-200"
                  >
                    <Download className="w-4 h-4" />
                    <span>Export Data</span>
                  </button>
                </div>
              </div>

              {/* Stats Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatCard
                  title="Total Projects"
                  value={stats.totalProjects}
                  subtitle="All projects"
                  color="from-blue-500 to-blue-600"
                />
                <StatCard
                  title="Featured Projects"
                  value={stats.featuredProjects}
                  subtitle="Highlighted work"
                  color="from-purple-500 to-purple-600"
                />
                <StatCard
                  title="Blog Posts"
                  value={stats.totalBlogPosts}
                  subtitle="Total articles"
                  color="from-green-500 to-green-600"
                />
                <StatCard
                  title="Published Posts"
                  value={stats.publishedPosts}
                  subtitle="Live articles"
                  color="from-orange-500 to-orange-600"
                />
              </div>

              {/* Recent Activity & Quick Actions */}
              <div className="grid lg:grid-cols-2 gap-6">
                {/* Recent Activity */}
                <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
                  <h2 className="text-xl font-bold text-white mb-4">Recent Activity</h2>
                  <div className="space-y-3">
                    {projects.slice(0, 3).map((project) => (
                      <div key={project.id} className="flex items-center space-x-3 p-3 bg-white/5 rounded-xl hover:bg-white/10 transition-all duration-200">
                        <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-lg flex items-center justify-center">
                          <FolderOpen className="w-5 h-5 text-white" />
                        </div>
                        <div className="flex-1">
                          <p className="text-white font-medium">{project.title}</p>
                          <p className="text-gray-400 text-sm">Updated {new Date(project.updatedAt).toLocaleDateString()}</p>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          project.status === 'Completed'
                            ? 'bg-green-500/20 text-green-400'
                            : 'bg-blue-500/20 text-blue-400'
                        }`}>
                          {project.status}
                        </span>
                      </div>
                    ))}

                    {blogPosts.slice(0, 2).map((post) => (
                      <div key={post.id} className="flex items-center space-x-3 p-3 bg-white/5 rounded-xl hover:bg-white/10 transition-all duration-200">
                        <div className="w-10 h-10 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg flex items-center justify-center">
                          <BookOpen className="w-5 h-5 text-white" />
                        </div>
                        <div className="flex-1">
                          <p className="text-white font-medium">{post.title}</p>
                          <p className="text-gray-400 text-sm">Updated {new Date(post.updatedAt).toLocaleDateString()}</p>
                        </div>
                        <span className={`text-xs px-2 py-1 rounded ${
                          post.published ? 'text-green-400 bg-green-500/20' : 'text-gray-400 bg-gray-500/20'
                        }`}>
                          {post.published ? 'Published' : 'Draft'}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
                  <h2 className="text-xl font-bold text-white mb-4">Quick Actions</h2>
                  <div className="grid grid-cols-2 gap-4">
                    <button
                      onClick={() => setActiveTab('projects')}
                      className="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-purple-600/20 to-purple-700/20 hover:from-purple-600/30 hover:to-purple-700/30 rounded-xl border border-purple-500/30 hover:border-purple-400/50 transition-all duration-200 group"
                    >
                      <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-purple-700 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <Plus className="w-6 h-6 text-white" />
                      </div>
                      <span className="text-white font-medium">New Project</span>
                    </button>

                    <button
                      onClick={() => setActiveTab('blog')}
                      className="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-green-600/20 to-green-700/20 hover:from-green-600/30 hover:to-green-700/30 rounded-xl border border-green-500/30 hover:border-green-400/50 transition-all duration-200 group"
                    >
                      <div className="w-12 h-12 bg-gradient-to-r from-green-600 to-green-700 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <FileText className="w-6 h-6 text-white" />
                      </div>
                      <span className="text-white font-medium">Write Article</span>
                    </button>

                    <button
                      onClick={() => setActiveTab('profile')}
                      className="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-blue-600/20 to-blue-700/20 hover:from-blue-600/30 hover:to-blue-700/30 rounded-xl border border-blue-500/30 hover:border-blue-400/50 transition-all duration-200 group"
                    >
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <User className="w-6 h-6 text-white" />
                      </div>
                      <span className="text-white font-medium">Edit Profile</span>
                    </button>

                    <button
                      onClick={exportData}
                      className="flex flex-col items-center space-y-3 p-4 bg-gradient-to-br from-orange-600/20 to-orange-700/20 hover:from-orange-600/30 hover:to-orange-700/30 rounded-xl border border-orange-500/30 hover:border-orange-400/50 transition-all duration-200 group"
                    >
                      <div className="w-12 h-12 bg-gradient-to-r from-orange-600 to-orange-700 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <Download className="w-6 h-6 text-white" />
                      </div>
                      <span className="text-white font-medium">Export Data</span>
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'hero' && (
            <motion.div
              key="hero"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <HeroManager onDataChange={loadData} />
            </motion.div>
          )}

          {activeTab === 'projects' && (
            <motion.div
              key="projects"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <ProjectManager onDataChange={loadData} />
            </motion.div>
          )}

          {activeTab === 'blog' && (
            <motion.div
              key="blog"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <BlogManager onDataChange={loadData} />
            </motion.div>
          )}

          {activeTab === 'profile' && (
            <motion.div
              key="profile"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <ProfileManager onDataChange={loadData} />
            </motion.div>
          )}

          {activeTab === 'contact' && (
            <motion.div
              key="contact"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <ContactManager onDataChange={loadData} />
            </motion.div>
          )}

          {activeTab === 'seo' && (
            <motion.div
              key="seo"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <SEOManager onDataChange={loadData} />
            </motion.div>
          )}

          {activeTab === 'theme' && (
            <motion.div
              key="theme"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <ThemeManager onDataChange={loadData} />
            </motion.div>
          )}

          {activeTab === 'analytics' && (
            <motion.div
              key="analytics"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <AnalyticsManager onDataChange={loadData} />
            </motion.div>
          )}

          {activeTab === 'notifications' && (
            <motion.div
              key="notifications"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <NotificationManager />
            </motion.div>
          )}

          {activeTab === 'security' && (
            <motion.div
              key="security"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <SecurityManager />
            </motion.div>
          )}

          {activeTab === 'performance' && (
            <motion.div
              key="performance"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <PerformanceManager />
            </motion.div>
          )}

          {activeTab === 'cloudsync' && (
            <motion.div
              key="cloudsync"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <CloudSyncManager onDataChange={loadData} />
            </motion.div>
          )}

          {activeTab === 'settings' && (
            <motion.div
              key="settings"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <SettingsManager onDataChange={loadData} />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default AdminDashboard;
