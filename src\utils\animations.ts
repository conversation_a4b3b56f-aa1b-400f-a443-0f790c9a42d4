import { Variants } from 'framer-motion';

// Easing functions for smooth animations
export const easing = {
  easeInOut: [0.4, 0, 0.2, 1],
  easeOut: [0, 0, 0.2, 1],
  easeIn: [0.4, 0, 1, 1],
  bounce: [0.68, -0.55, 0.265, 1.55],
  elastic: [0.175, 0.885, 0.32, 1.275],
};

// Page transition variants
export const pageTransition: Variants = {
  initial: {
    opacity: 0,
    y: 20,
    scale: 0.98,
  },
  animate: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: easing.easeOut,
      staggerChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    scale: 0.98,
    transition: {
      duration: 0.4,
      ease: easing.easeIn,
    },
  },
};

// Scroll-triggered animations
export const fadeInUp: Variants = {
  hidden: {
    opacity: 0,
    y: 60,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: easing.easeOut,
    },
  },
};

export const fadeInDown: Variants = {
  hidden: {
    opacity: 0,
    y: -60,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: easing.easeOut,
    },
  },
};

export const fadeInLeft: Variants = {
  hidden: {
    opacity: 0,
    x: -60,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: easing.easeOut,
    },
  },
};

export const fadeInRight: Variants = {
  hidden: {
    opacity: 0,
    x: 60,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: easing.easeOut,
    },
  },
};

export const scaleIn: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
  },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: easing.bounce,
    },
  },
};

export const slideInUp: Variants = {
  hidden: {
    y: 100,
    opacity: 0,
  },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.8,
      ease: easing.easeOut,
    },
  },
};

// Stagger container for multiple children
export const staggerContainer: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

export const staggerItem: Variants = {
  hidden: {
    opacity: 0,
    y: 30,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: easing.easeOut,
    },
  },
};

// Button hover animations
export const buttonHover: Variants = {
  rest: {
    scale: 1,
    boxShadow: '0 10px 30px rgba(139, 92, 246, 0.2)',
  },
  hover: {
    scale: 1.05,
    boxShadow: '0 20px 40px rgba(139, 92, 246, 0.4)',
    transition: {
      duration: 0.3,
      ease: easing.easeOut,
    },
  },
  tap: {
    scale: 0.98,
    transition: {
      duration: 0.1,
    },
  },
};

// Card hover animations
export const cardHover: Variants = {
  rest: {
    scale: 1,
    y: 0,
    rotateX: 0,
    rotateY: 0,
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)',
  },
  hover: {
    scale: 1.02,
    y: -8,
    rotateX: 5,
    rotateY: 5,
    boxShadow: '0 25px 50px rgba(139, 92, 246, 0.3)',
    transition: {
      duration: 0.4,
      ease: easing.easeOut,
    },
  },
};

// Loading animations
export const loadingSpinner: Variants = {
  animate: {
    rotate: 360,
    transition: {
      duration: 1,
      repeat: Infinity,
      ease: 'linear',
    },
  },
};

export const loadingPulse: Variants = {
  animate: {
    scale: [1, 1.2, 1],
    opacity: [0.7, 1, 0.7],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: easing.easeInOut,
    },
  },
};

// Skeleton loading animation
export const skeletonPulse: Variants = {
  animate: {
    opacity: [0.5, 1, 0.5],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: easing.easeInOut,
    },
  },
};

// Text reveal animations
export const textReveal: Variants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: easing.easeOut,
    },
  },
};

// Floating animation
export const floatingAnimation: Variants = {
  animate: {
    y: [-10, 10, -10],
    transition: {
      duration: 3,
      repeat: Infinity,
      ease: easing.easeInOut,
    },
  },
};

// Magnetic hover effect
export const magneticHover = {
  rest: { x: 0, y: 0 },
  hover: { x: 0, y: 0 },
};

// Parallax scroll effect
export const parallaxScroll = (offset: number) => ({
  y: offset,
  transition: {
    type: 'spring',
    stiffness: 100,
    damping: 30,
  },
});

// Viewport animation options - More forgiving for better compatibility
export const viewportOptions = {
  once: true,
  margin: '-50px',
  amount: 0.1,
};

// Enhanced viewport options for different scenarios
export const viewportOptionsRepeating = {
  once: false,
  margin: '-50px',
  amount: 0.2,
};

// Cool scroll animation variants
export const slideInFromLeft = {
  hidden: {
    opacity: 0,
    x: -100,
    scale: 0.8,
    rotateY: -45,
  },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    rotateY: 0,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94],
      type: "spring",
      stiffness: 100,
      damping: 15,
    }
  },
};

export const slideInFromRight = {
  hidden: {
    opacity: 0,
    x: 100,
    scale: 0.8,
    rotateY: 45,
  },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    rotateY: 0,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94],
      type: "spring",
      stiffness: 100,
      damping: 15,
    }
  },
};

export const bounceIn = {
  hidden: {
    opacity: 0,
    scale: 0.3,
    y: 50,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.68, -0.55, 0.265, 1.55],
      type: "spring",
      stiffness: 200,
      damping: 10,
    }
  },
};

export const flipIn = {
  hidden: {
    opacity: 0,
    rotateX: -90,
    scale: 0.8,
  },
  visible: {
    opacity: 1,
    rotateX: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94],
    }
  },
};

export const zoomIn = {
  hidden: {
    opacity: 0,
    scale: 0.5,
    filter: "blur(10px)",
  },
  visible: {
    opacity: 1,
    scale: 1,
    filter: "blur(0px)",
    transition: {
      duration: 0.7,
      ease: [0.25, 0.46, 0.45, 0.94],
    }
  },
};

export const rotateIn = {
  hidden: {
    opacity: 0,
    rotate: -180,
    scale: 0.5,
  },
  visible: {
    opacity: 1,
    rotate: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94],
    }
  },
};

export const elasticIn = {
  hidden: {
    opacity: 0,
    scale: 0,
    y: 100,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 1.2,
      ease: [0.175, 0.885, 0.32, 1.275],
      type: "spring",
      stiffness: 300,
      damping: 20,
    }
  },
};

// Advanced stagger animations for scroll
export const staggerContainerScroll = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.1,
      duration: 0.6,
    },
  },
};

export const staggerItemScroll = {
  hidden: {
    opacity: 0,
    y: 60,
    scale: 0.8,
    rotateX: -15,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    rotateX: 0,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94],
      type: "spring",
      stiffness: 100,
      damping: 12,
    }
  },
};

// Parallax-style animations
export const parallaxUp = {
  hidden: {
    opacity: 0,
    y: 100,
    scale: 0.9,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 1.2,
      ease: [0.25, 0.46, 0.45, 0.94],
    }
  },
};

export const parallaxDown = {
  hidden: {
    opacity: 0,
    y: -100,
    scale: 0.9,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 1.2,
      ease: [0.25, 0.46, 0.45, 0.94],
    }
  },
};

// Magnetic hover effect for scroll elements
export const magneticScrollHover = {
  rest: {
    scale: 1,
    filter: "brightness(1)",
  },
  hover: {
    scale: 1.05,
    filter: "brightness(1.1)",
    transition: {
      duration: 0.3,
      ease: "easeOut",
    }
  },
};

// Reveal animation with mask effect
export const revealMask = {
  hidden: {
    opacity: 0,
    clipPath: "inset(0 100% 0 0)",
  },
  visible: {
    opacity: 1,
    clipPath: "inset(0 0% 0 0)",
    transition: {
      duration: 1.2,
      ease: [0.25, 0.46, 0.45, 0.94],
    }
  },
};

// Typewriter effect for text
export const typewriter = {
  hidden: {
    width: 0,
    opacity: 0,
  },
  visible: {
    width: "100%",
    opacity: 1,
    transition: {
      duration: 1.5,
      ease: "easeInOut",
    }
  },
};

// Reduced motion variants
export const reducedMotionVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 0.3 } },
};
