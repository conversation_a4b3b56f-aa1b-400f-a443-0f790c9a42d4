import React, { createContext, useContext, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { deviceDetection, DeviceInfo } from '../utils/deviceDetection';

interface AnimationSettings {
  enableComplexAnimations: boolean;
  enableScrollAnimations: boolean;
  enableParallax: boolean;
  enableParticles: boolean;
  enableLoadingAnimation: boolean;
  animationDuration: number;
  staggerDelay: number;
  enableBlur: boolean;
  enableShadows: boolean;
  reducedMotion: boolean;
}

interface AnimationContextType {
  settings: AnimationSettings;
  deviceInfo: DeviceInfo;
  updateSettings: (newSettings: Partial<AnimationSettings>) => void;
  isAnimationEnabled: (type: keyof AnimationSettings) => boolean;
}

const AnimationContext = createContext<AnimationContextType | undefined>(undefined);

export const useAnimation = () => {
  const context = useContext(AnimationContext);
  if (!context) {
    throw new Error('useAnimation must be used within an AnimationProvider');
  }
  return context;
};

interface AnimationProviderProps {
  children: React.ReactNode;
}

export const AnimationProvider: React.FC<AnimationProviderProps> = ({ children }) => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>(() => deviceDetection.getDeviceInfo());
  const [settings, setSettings] = useState<AnimationSettings>(() => {
    const device = deviceDetection.getDeviceInfo();
    const animationSettings = deviceDetection.getAnimationSettings();
    
    return {
      enableComplexAnimations: animationSettings.enableComplexAnimations,
      enableScrollAnimations: animationSettings.enableScrollAnimations,
      enableParallax: animationSettings.enableParallax,
      enableParticles: animationSettings.enableParticles,
      enableLoadingAnimation: deviceDetection.shouldShowLoadingAnimation(),
      animationDuration: animationSettings.animationDuration,
      staggerDelay: animationSettings.staggerDelay,
      enableBlur: animationSettings.enableBlur,
      enableShadows: animationSettings.enableShadows,
      reducedMotion: device.reducedMotion,
    };
  });

  useEffect(() => {
    // Listen for media query changes
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const handleChange = () => {
      const newDeviceInfo = deviceDetection.getDeviceInfo();
      setDeviceInfo(newDeviceInfo);
      
      const newAnimationSettings = deviceDetection.getAnimationSettings();
      setSettings(prev => ({
        ...prev,
        ...newAnimationSettings,
        enableLoadingAnimation: deviceDetection.shouldShowLoadingAnimation(),
        reducedMotion: newDeviceInfo.reducedMotion,
      }));
    };

    mediaQuery.addEventListener('change', handleChange);
    
    // Also listen for resize events to update device info
    const handleResize = () => {
      // Reset device detection to get fresh info
      deviceDetection.reset();
      handleChange();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      mediaQuery.removeEventListener('change', handleChange);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const updateSettings = (newSettings: Partial<AnimationSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const isAnimationEnabled = (type: keyof AnimationSettings): boolean => {
    if (settings.reducedMotion) return false;
    return settings[type] as boolean;
  };

  const value: AnimationContextType = {
    settings,
    deviceInfo,
    updateSettings,
    isAnimationEnabled,
  };

  return (
    <AnimationContext.Provider value={value}>
      {children}
    </AnimationContext.Provider>
  );
};

// Custom hooks for specific animation types
export const useScrollAnimations = () => {
  const { isAnimationEnabled } = useAnimation();
  return isAnimationEnabled('enableScrollAnimations');
};

export const useComplexAnimations = () => {
  const { isAnimationEnabled } = useAnimation();
  return isAnimationEnabled('enableComplexAnimations');
};

export const useParallaxAnimations = () => {
  const { isAnimationEnabled } = useAnimation();
  return isAnimationEnabled('enableParallax');
};

export const useParticleAnimations = () => {
  const { isAnimationEnabled } = useAnimation();
  return isAnimationEnabled('enableParticles');
};

export const useLoadingAnimation = () => {
  const { isAnimationEnabled } = useAnimation();
  return isAnimationEnabled('enableLoadingAnimation');
};

// Animation variant generators based on device capabilities
export const getOptimizedVariants = (baseVariants: any) => {
  const { settings } = useAnimation();
  
  if (settings.reducedMotion) {
    return {
      hidden: { opacity: 0 },
      visible: { opacity: 1, transition: { duration: 0.1 } },
    };
  }

  // Adjust animation duration based on device
  const adjustedVariants = { ...baseVariants };
  if (adjustedVariants.visible?.transition) {
    adjustedVariants.visible.transition.duration = settings.animationDuration;
  }

  return adjustedVariants;
};

// Optimized viewport options based on device
export const getOptimizedViewportOptions = () => {
  const { deviceInfo, settings } = useAnimation();
  
  if (deviceInfo.isMobile) {
    return {
      once: true,
      margin: '-50px',
      amount: 0.1,
    };
  }

  return {
    once: !settings.enableScrollAnimations,
    margin: '-100px',
    amount: 0.3,
  };
};

// Performance-optimized animation wrapper
export const OptimizedMotion: React.FC<{
  children: React.ReactNode;
  variants?: any;
  className?: string;
  [key: string]: any;
}> = ({ children, variants, ...props }) => {
  const { settings } = useAnimation();
  
  if (settings.reducedMotion) {
    return <div className={props.className}>{children}</div>;
  }

  const optimizedVariants = variants ? getOptimizedVariants(variants) : undefined;
  
  return (
    <motion.div variants={optimizedVariants} {...props}>
      {children}
    </motion.div>
  );
};

// Conditional animation wrapper
export const ConditionalAnimation: React.FC<{
  children: React.ReactNode;
  condition: keyof AnimationSettings;
  fallback?: React.ReactNode;
}> = ({ children, condition, fallback }) => {
  const { isAnimationEnabled } = useAnimation();
  
  if (!isAnimationEnabled(condition)) {
    return <>{fallback || children}</>;
  }
  
  return <>{children}</>;
};
