import { motion } from 'framer-motion';
import React from 'react';
import { loadingPulse, loadingSpinner, skeletonPulse } from '../utils/animations';

interface LoadingProps {
  type?: 'spinner' | 'pulse' | 'skeleton' | 'dots' | 'wave';
  size?: 'sm' | 'md' | 'lg';
  color?: 'purple' | 'cyan' | 'white' | 'gray';
  className?: string;
}

const Loading: React.FC<LoadingProps> = ({ 
  type = 'spinner', 
  size = 'md', 
  color = 'purple', 
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
  };

  const colorClasses = {
    purple: 'text-purple-500',
    cyan: 'text-cyan-500',
    white: 'text-white',
    gray: 'text-gray-400',
  };

  if (type === 'spinner') {
    return (
      <motion.div
        className={`${sizeClasses[size]} ${colorClasses[color]} ${className}`}
        variants={loadingSpinner}
        animate="animate"
      >
        <svg
          className="w-full h-full"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeDasharray="60"
            strokeDashoffset="60"
            opacity="0.3"
          />
          <circle
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeDasharray="15"
            strokeDashoffset="15"
          />
        </svg>
      </motion.div>
    );
  }

  if (type === 'pulse') {
    return (
      <motion.div
        className={`${sizeClasses[size]} bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full ${className}`}
        variants={loadingPulse}
        animate="animate"
      />
    );
  }

  if (type === 'dots') {
    return (
      <div className={`flex space-x-2 ${className}`}>
        {[0, 1, 2].map((index) => (
          <motion.div
            key={index}
            className={`${sizeClasses[size]} bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full`}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.7, 1, 0.7],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: index * 0.2,
            }}
          />
        ))}
      </div>
    );
  }

  if (type === 'wave') {
    return (
      <div className={`flex items-end space-x-1 ${className}`}>
        {[0, 1, 2, 3, 4].map((index) => (
          <motion.div
            key={index}
            className="w-1 bg-gradient-to-t from-purple-500 to-cyan-500 rounded-full"
            animate={{
              height: ['10px', '30px', '10px'],
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              delay: index * 0.1,
            }}
          />
        ))}
      </div>
    );
  }

  return null;
};

// Skeleton loading components
export const SkeletonText: React.FC<{ lines?: number; className?: string }> = ({ 
  lines = 3, 
  className = '' 
}) => (
  <div className={`space-y-3 ${className}`}>
    {Array.from({ length: lines }).map((_, index) => (
      <motion.div
        key={index}
        className={`h-4 bg-gray-700/50 rounded ${
          index === lines - 1 ? 'w-3/4' : 'w-full'
        }`}
        variants={skeletonPulse}
        animate="animate"
      />
    ))}
  </div>
);

export const SkeletonCard: React.FC<{ className?: string }> = ({ className = '' }) => (
  <motion.div
    className={`bg-white/5 backdrop-blur-sm rounded-3xl p-6 border border-white/10 ${className}`}
    variants={skeletonPulse}
    animate="animate"
  >
    <div className="space-y-4">
      <motion.div
        className="h-48 bg-gray-700/50 rounded-2xl"
        variants={skeletonPulse}
        animate="animate"
      />
      <motion.div
        className="h-6 bg-gray-700/50 rounded w-3/4"
        variants={skeletonPulse}
        animate="animate"
      />
      <SkeletonText lines={2} />
      <div className="flex space-x-2">
        {[1, 2, 3].map((i) => (
          <motion.div
            key={i}
            className="h-6 w-16 bg-gray-700/50 rounded-full"
            variants={skeletonPulse}
            animate="animate"
          />
        ))}
      </div>
    </div>
  </motion.div>
);

// Epic Modern Loading Screen
export const PageLoader: React.FC = () => {
  return (
    <motion.div
      className="fixed inset-0 bg-gradient-to-br from-gray-900 via-purple-900/20 to-cyan-900/20 z-50 flex items-center justify-center overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Animated Background Particles */}
      <div className="absolute inset-0">
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-gradient-to-r from-purple-400 to-cyan-400 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              scale: [0, 1, 0],
              opacity: [0, 1, 0],
              x: [0, Math.random() * 100 - 50],
              y: [0, Math.random() * 100 - 50],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      {/* Main Loading Content */}
      <div className="relative z-10 text-center">
        {/* Morphing Logo/Icon */}
        <motion.div
          className="relative mb-8"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          {/* Outer Ring */}
          <motion.div
            className="w-24 h-24 mx-auto relative"
            animate={{ rotate: 360 }}
            transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
          >
            <div className="absolute inset-0 rounded-full border-2 border-gradient-to-r from-purple-500 to-cyan-500 opacity-30"></div>
            <div className="absolute inset-2 rounded-full border-2 border-gradient-to-r from-cyan-500 to-purple-500 opacity-60"></div>
          </motion.div>

          {/* Inner Morphing Shape */}
          <motion.div
            className="absolute inset-0 flex items-center justify-center"
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-lg transform rotate-45"></div>
          </motion.div>
        </motion.div>

        {/* Dynamic Progress Bar */}
        <motion.div
          className="w-64 h-1 bg-gray-800 rounded-full mx-auto mb-6 overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <motion.div
            className="h-full bg-gradient-to-r from-purple-500 via-pink-500 to-cyan-500 rounded-full"
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        </motion.div>

        {/* Loading Text with Typewriter Effect */}
        <motion.div
          className="space-y-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <motion.h2
            className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent"
            animate={{
              backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "linear",
            }}
          >
            Loading Experience
          </motion.h2>

          <motion.p
            className="text-gray-400 text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: [0, 1, 0] }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            Crafting something amazing...
          </motion.p>
        </motion.div>

        {/* Floating Elements */}
        <div className="absolute -top-20 -left-20">
          <motion.div
            className="w-4 h-4 bg-purple-500/30 rounded-full"
            animate={{
              y: [0, -20, 0],
              x: [0, 10, 0],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        </div>

        <div className="absolute -bottom-20 -right-20">
          <motion.div
            className="w-6 h-6 bg-cyan-500/30 rounded-full"
            animate={{
              y: [0, 20, 0],
              x: [0, -15, 0],
              scale: [1, 0.8, 1],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1,
            }}
          />
        </div>
      </div>

      {/* Grid Background Effect */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(168, 85, 247, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(168, 85, 247, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px',
        }} />
      </div>
    </motion.div>
  );
};

// Button loading state
export const ButtonLoader: React.FC<{ className?: string }> = ({ className = '' }) => (
  <Loading type="spinner" size="sm" color="white" className={className} />
);

// Working Hacker Terminal Loading Screen
export const HackerTerminalLoader: React.FC = () => {
  return (
    <motion.div
      className="fixed inset-0 bg-black z-50 flex items-center justify-center overflow-hidden font-mono"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Matrix Rain Background - Reduced for mobile */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute text-green-400/20 text-xs sm:text-sm"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          >
            {Math.random() > 0.5 ? '1' : '0'}
          </motion.div>
        ))}
      </div>

      {/* Terminal Content - Perfect Width for ASCII Art */}
      <div className="relative z-10 text-center w-full max-w-2xl mx-4">
        <motion.div
          className="bg-black/90 border border-green-500/30 rounded-lg p-4 sm:p-6 overflow-hidden mx-2"
          style={{ width: '100%', maxWidth: '600px', margin: '0 auto' }}
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.8 }}
        >
          {/* Terminal Header - Subtle Style */}
          <motion.div
            className="flex items-center justify-center sm:justify-between mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-green-500/30"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 sm:w-3 sm:h-3 bg-red-500 rounded-full"></div>
                <div className="w-2 h-2 sm:w-3 sm:h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-2 h-2 sm:w-3 sm:h-3 bg-green-500 rounded-full"></div>
              </div>
              <span className="text-green-400 text-xs sm:text-sm font-mono">root@hackermachine:~$</span>
            </div>
          </motion.div>

          {/* Unified Clean HACKER Text - All Devices */}
          <motion.div
            className="text-center mb-6 md:mb-8"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5, duration: 1 }}
          >
            <motion.div
              className="text-4xl md:text-5xl lg:text-6xl font-bold mb-3 text-green-400"
              style={{
                textShadow: '0 0 15px rgba(34, 197, 94, 0.8)',
                fontFamily: 'Consolas, "Courier New", monospace',
                letterSpacing: '0.1em'
              }}
              animate={{
                textShadow: [
                  '0 0 15px rgba(34, 197, 94, 0.8)',
                  '0 0 25px rgba(34, 197, 94, 1), 0 0 35px rgba(34, 197, 94, 0.6)',
                  '0 0 15px rgba(34, 197, 94, 0.8)'
                ]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              HACKER
            </motion.div>
            <motion.div
              className="text-sm md:text-base text-green-300 font-mono"
              animate={{
                opacity: [0.7, 1, 0.7]
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              TERMINAL LOADING...
            </motion.div>
          </motion.div>



          {/* Simplified Commands - Unified Design */}
          <motion.div
            className="text-left space-y-3 mb-6 md:mb-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
          >
            {[
              'Initializing secure connection...',
              'Bypassing firewall protocols...',
              'Decrypting access tokens...',
              'Loading neural interface...'
            ].map((cmd, index) => (
              <motion.div
                key={index}
                className="text-green-400 text-sm md:text-base"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1 + index * 0.3 }}
              >
                <div className="flex items-center space-x-2">
                  <span className="text-green-500">&gt;</span>
                  <span>{cmd}</span>
                  <motion.span
                    className="text-green-300"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1.2 + index * 0.3 }}
                  >
                    ✓
                  </motion.span>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Current Command - Simplified */}
          <motion.div
            className="text-green-400 mb-6 text-sm md:text-base"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2.5 }}
          >
            <div className="flex items-center justify-center space-x-2">
              <span className="text-green-500">&gt;</span>
              <span>Access granted. Loading interface...</span>
              <motion.span
                className="inline-block w-2 h-4 bg-green-400 ml-1"
                animate={{ opacity: [1, 0] }}
                transition={{ duration: 0.5, repeat: Infinity }}
              />
            </div>
          </motion.div>

          {/* Progress Bar - Unified Design */}
          <motion.div
            className="w-full bg-gray-800 h-2 rounded-full overflow-hidden mb-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 3 }}
          >
            <motion.div
              className="h-full bg-gradient-to-r from-green-500 to-green-400"
              initial={{ width: "0%" }}
              animate={{ width: "100%" }}
              transition={{
                duration: 2,
                ease: "easeInOut",
              }}
              style={{
                boxShadow: '0 0 10px rgba(34, 197, 94, 0.5)'
              }}
            />
          </motion.div>

          {/* System Stats - Unified Design */}
          <motion.div
            className="grid grid-cols-3 gap-6 text-green-400"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 3.5 }}
          >
            <div className="text-center">
              <div className="text-green-500 mb-2 text-sm font-mono">CPU</div>
              <motion.div
                className="text-base md:text-lg font-mono"
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 1, repeat: Infinity }}
              >
                87%
              </motion.div>
            </div>
            <div className="text-center">
              <div className="text-green-500 mb-2 text-sm font-mono">RAM</div>
              <motion.div
                className="text-base md:text-lg font-mono"
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 1.2, repeat: Infinity }}
              >
                74%
              </motion.div>
            </div>
            <div className="text-center">
              <div className="text-green-500 mb-2 text-sm font-mono">NET</div>
              <motion.div
                className="text-base md:text-lg font-mono"
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 0.8, repeat: Infinity }}
              >
                1.2 GB/s
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Glitch Effect */}
      <motion.div
        className="absolute inset-0 bg-green-500/5"
        animate={{
          opacity: [0, 0.1, 0],
        }}
        transition={{
          duration: 0.1,
          repeat: Infinity,
          repeatDelay: Math.random() * 3 + 1,
        }}
      />
    </motion.div>
  );
};

// Futuristic Tech Loader
export const TechLoader: React.FC = () => {
  return (
    <motion.div
      className="fixed inset-0 bg-black z-50 flex items-center justify-center overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Matrix-style Background */}
      <div className="absolute inset-0">
        {[...Array(100)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute text-green-500/20 text-xs font-mono"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0, 1, 0],
              y: [0, 50],
            }}
            transition={{
              duration: Math.random() * 2 + 1,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          >
            {Math.random() > 0.5 ? '1' : '0'}
          </motion.div>
        ))}
      </div>

      {/* Main Tech Interface */}
      <div className="relative z-10 text-center">
        {/* Holographic Circle */}
        <motion.div
          className="relative w-32 h-32 mx-auto mb-8"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 1, ease: "backOut" }}
        >
          {/* Outer Rings */}
          {[0, 1, 2].map((ring) => (
            <motion.div
              key={ring}
              className={`absolute inset-${ring * 2} border border-cyan-500/50 rounded-full`}
              animate={{ rotate: ring % 2 === 0 ? 360 : -360 }}
              transition={{
                duration: 3 + ring,
                repeat: Infinity,
                ease: "linear",
              }}
            />
          ))}

          {/* Center Core */}
          <motion.div
            className="absolute inset-8 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              boxShadow: [
                "0 0 20px rgba(6, 182, 212, 0.5)",
                "0 0 40px rgba(168, 85, 247, 0.8)",
                "0 0 20px rgba(6, 182, 212, 0.5)",
              ],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        </motion.div>

        {/* System Status */}
        <motion.div
          className="space-y-3"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <motion.h2
            className="text-xl font-mono text-cyan-400"
            animate={{
              opacity: [0.7, 1, 0.7],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
            }}
          >
            SYSTEM INITIALIZING
          </motion.h2>

          <div className="space-y-1 text-xs font-mono text-gray-400">
            {['Loading modules...', 'Connecting to server...', 'Preparing interface...'].map((text, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 1 + index * 0.3 }}
              >
                <span className="text-green-400">&gt;</span> {text}
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

// SIMPLE WORKING HACKER LOADER
export const SimpleHackerLoader: React.FC = () => {
  const [currentPhase, setCurrentPhase] = React.useState(0);
  const [progress, setProgress] = React.useState(0);

  const phases = [
    'INITIALIZING QUANTUM ENCRYPTION...',
    'BYPASSING NEURAL FIREWALLS...',
    'SCANNING NETWORK TOPOLOGY...',
    'INJECTING PAYLOAD VECTORS...',
    'ESTABLISHING SECURE TUNNEL...',
    'DECRYPTING MAINFRAME ACCESS...',
    'LOADING NEURAL INTERFACE...',
    'ACCESS GRANTED - WELCOME HACKER'
  ];

  React.useEffect(() => {
    const phaseInterval = setInterval(() => {
      setCurrentPhase(prev => (prev < phases.length - 1 ? prev + 1 : prev));
    }, 800);

    const progressInterval = setInterval(() => {
      setProgress(prev => (prev < 100 ? prev + 2 : 100));
    }, 100);

    return () => {
      clearInterval(phaseInterval);
      clearInterval(progressInterval);
    };
  }, [phases.length]);

  return (
    <motion.div
      className="fixed inset-0 bg-black z-50 flex items-center justify-center overflow-hidden font-mono"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Enhanced Matrix Background */}
      <div className="absolute inset-0">
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute text-green-400/30 text-xs font-mono"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0, 1, 0],
              y: [0, 30],
              scale: [0.8, 1.2, 0.8]
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: "easeInOut"
            }}
          >
            {Math.random() > 0.7 ? ['01', '10', '11', '00'][Math.floor(Math.random() * 4)] : Math.random() > 0.5 ? '1' : '0'}
          </motion.div>
        ))}

        {/* Scanning Lines */}
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={`scan-${i}`}
            className="absolute w-full h-px bg-gradient-to-r from-transparent via-green-400/50 to-transparent"
            animate={{
              top: ['0%', '100%'],
              opacity: [0, 1, 0]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              delay: i * 1.5,
              ease: "linear"
            }}
          />
        ))}
      </div>

      {/* Terminal Window - MOBILE SAFE */}
      <motion.div
        className="bg-black border border-green-400/50 rounded-lg p-3 sm:p-4 md:p-6 mx-3 sm:mx-4"
        style={{
          width: '94vw',
          maxWidth: '700px',
          margin: '0 auto'
        }}
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        {/* Terminal Header */}
        <div className="flex items-center space-x-2 mb-6 pb-4 border-b border-green-400/40">
          <div className="flex space-x-1">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          </div>
          <span className="text-green-400 text-sm font-mono">
            root@hackermachine:~$
          </span>
        </div>

        {/* Unified Clean HACKER Text - All Devices */}
        <motion.div
          className="mb-6 md:mb-8 text-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5, duration: 1 }}
        >
          <motion.div
            className="text-3xl md:text-4xl lg:text-5xl font-bold mb-3 text-green-400"
            style={{
              textShadow: '0 0 15px rgba(34, 197, 94, 0.8)',
              fontFamily: 'Consolas, "Courier New", monospace',
              letterSpacing: '0.1em'
            }}
            animate={{
              textShadow: [
                '0 0 15px rgba(34, 197, 94, 0.8)',
                '0 0 25px rgba(34, 197, 94, 1), 0 0 35px rgba(34, 197, 94, 0.6)',
                '0 0 15px rgba(34, 197, 94, 0.8)'
              ]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            HACKER
          </motion.div>
          <motion.div
            className="text-sm md:text-base text-green-300 font-mono"
            animate={{
              opacity: [0.7, 1, 0.7]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            TERMINAL LOADING...
          </motion.div>
        </motion.div>

        {/* Current Phase */}
        <motion.div
          className="text-center mb-6"
          key={currentPhase}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="text-green-400 text-lg sm:text-xl font-mono mb-2">
            {phases[currentPhase]}
          </div>
          <motion.div
            className="flex justify-center space-x-1"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 0.8, repeat: Infinity }}
          >
            {[...Array(3)].map((_, i) => (
              <div key={i} className="w-2 h-2 bg-green-400 rounded-full" />
            ))}
          </motion.div>
        </motion.div>

        {/* Enhanced Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-green-400 text-sm font-mono mb-2">
            <motion.span
              animate={{ opacity: [0.7, 1, 0.7] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              SYSTEM PENETRATION
            </motion.span>
            <motion.span
              className="text-green-300"
              animate={{
                color: progress > 80 ? '#22c55e' : progress > 50 ? '#eab308' : '#ef4444',
                textShadow: progress > 80 ? '0 0 8px rgba(34, 197, 94, 0.8)' : '0 0 8px rgba(239, 68, 68, 0.8)'
              }}
              transition={{ duration: 0.3 }}
            >
              {Math.floor(progress)}%
            </motion.span>
          </div>
          <div className="w-full bg-gray-800/50 h-4 rounded-full overflow-hidden border border-green-400/30 relative">
            <motion.div
              className="h-full rounded-full relative overflow-hidden"
              style={{
                background: `linear-gradient(90deg,
                  ${progress < 30 ? '#ef4444' : progress < 70 ? '#eab308' : '#22c55e'} 0%,
                  ${progress < 30 ? '#f97316' : progress < 70 ? '#22c55e' : '#16a34a'} 100%)`
              }}
              initial={{ width: "0%" }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.1 }}
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                animate={{ x: ['-100%', '100%'] }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
              />
            </motion.div>
          </div>
        </div>

        {/* Enhanced Warning */}
        <motion.div
          className="border border-red-400/50 bg-red-500/10 p-4 rounded text-center relative overflow-hidden"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 3, duration: 0.5 }}
        >
          <motion.div
            className="absolute inset-0 bg-red-500/5"
            animate={{ opacity: [0, 0.3, 0] }}
            transition={{ duration: 0.8, repeat: Infinity }}
          />
          <motion.div
            className="text-red-400 text-sm font-mono relative z-10"
            animate={{
              opacity: [0.7, 1, 0.7],
              textShadow: [
                '0 0 5px rgba(239, 68, 68, 0.5)',
                '0 0 15px rgba(239, 68, 68, 0.8)',
                '0 0 5px rgba(239, 68, 68, 0.5)'
              ]
            }}
            transition={{ duration: 0.8, repeat: Infinity }}
          >
            ⚠️ UNAUTHORIZED ACCESS DETECTED ⚠️
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Enhanced Glitch Effects */}
      <motion.div
        className="absolute inset-0 bg-green-500/5"
        animate={{
          opacity: [0, 0.15, 0],
        }}
        transition={{
          duration: 0.1,
          repeat: Infinity,
          repeatDelay: Math.random() * 4 + 2,
        }}
      />

      {/* Random Glitch Bars */}
      {[...Array(5)].map((_, i) => (
        <motion.div
          key={`glitch-${i}`}
          className="absolute w-full bg-green-400/10"
          style={{
            height: `${Math.random() * 3 + 1}px`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            opacity: [0, 0.8, 0],
            scaleX: [0, 1, 0]
          }}
          transition={{
            duration: 0.1,
            repeat: Infinity,
            repeatDelay: Math.random() * 5 + 3,
            delay: i * 0.2
          }}
        />
      ))}
    </motion.div>
  );
};

export default Loading;
