// SEO Utilities for generating sitemaps, robots.txt, and other SEO files

export interface SitemapEntry {
  url: string;
  lastmod: string;
  changefreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority: number;
  images?: string[];
}

export class SEOFileGenerator {
  private baseUrl: string;

  constructor(baseUrl: string = 'https://nuralbhardwaj.me') {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
  }

  /**
   * Generate XML sitemap
   */
  generateSitemap(entries: SitemapEntry[]): string {
    const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>';
    const urlsetOpen = '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">';
    const urlsetClose = '</urlset>';

    const urls = entries.map(entry => {
      let urlXml = `  <url>
    <loc>${this.baseUrl}${entry.url}</loc>
    <lastmod>${entry.lastmod}</lastmod>
    <changefreq>${entry.changefreq}</changefreq>
    <priority>${entry.priority}</priority>`;

      // Add image entries if present
      if (entry.images && entry.images.length > 0) {
        entry.images.forEach(image => {
          urlXml += `
    <image:image>
      <image:loc>${image}</image:loc>
    </image:image>`;
        });
      }

      urlXml += `
  </url>`;
      return urlXml;
    }).join('\n');

    return `${xmlHeader}\n${urlsetOpen}\n${urls}\n${urlsetClose}`;
  }

  /**
   * Generate robots.txt
   */
  generateRobotsTxt(options: {
    allowAll?: boolean;
    disallowPaths?: string[];
    allowPaths?: string[];
    crawlDelay?: number;
    sitemapUrl?: string;
  } = {}): string {
    const {
      allowAll = true,
      disallowPaths = ['/admin/', '/api/', '/*.json$'],
      allowPaths = ['/assets/', '/images/', '/*.css$', '/*.js$'],
      crawlDelay = 1,
      sitemapUrl = `${this.baseUrl}/sitemap.xml`
    } = options;

    let robotsTxt = 'User-agent: *\n';

    if (allowAll) {
      robotsTxt += 'Allow: /\n';
    }

    // Add disallow rules
    disallowPaths.forEach(path => {
      robotsTxt += `Disallow: ${path}\n`;
    });

    // Add allow rules
    allowPaths.forEach(path => {
      robotsTxt += `Allow: ${path}\n`;
    });

    robotsTxt += `\n# Crawl-delay\nCrawl-delay: ${crawlDelay}\n`;
    robotsTxt += `\n# Sitemaps\nSitemap: ${sitemapUrl}\n`;

    // Add additional search engine specific rules
    robotsTxt += `
# Google specific
User-agent: Googlebot
Allow: /

# Bing specific
User-agent: Bingbot
Allow: /

# Yandex specific
User-agent: YandexBot
Allow: /

# Block bad bots
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /`;

    return robotsTxt;
  }

  /**
   * Generate default sitemap entries for a portfolio website
   */
  generateDefaultSitemapEntries(): SitemapEntry[] {
    const now = new Date().toISOString().split('T')[0];
    
    return [
      {
        url: '/',
        lastmod: now,
        changefreq: 'weekly',
        priority: 1.0,
        images: [`${this.baseUrl}/og-image.jpg`]
      },
      {
        url: '/#about',
        lastmod: now,
        changefreq: 'monthly',
        priority: 0.8
      },
      {
        url: '/#skills',
        lastmod: now,
        changefreq: 'monthly',
        priority: 0.8
      },
      {
        url: '/#projects',
        lastmod: now,
        changefreq: 'weekly',
        priority: 0.9
      },
      {
        url: '/#blog',
        lastmod: now,
        changefreq: 'daily',
        priority: 0.9
      },
      {
        url: '/#resume',
        lastmod: now,
        changefreq: 'monthly',
        priority: 0.7
      },
      {
        url: '/#contact',
        lastmod: now,
        changefreq: 'monthly',
        priority: 0.7
      }
    ];
  }

  /**
   * Generate structured data for a person/developer
   */
  generatePersonStructuredData(data: {
    name: string;
    jobTitle: string;
    url: string;
    email: string;
    telephone?: string;
    address?: {
      locality: string;
      country: string;
    };
    sameAs?: string[];
    worksFor?: string;
    skills?: string[];
    image?: string;
  }): string {
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": data.name,
      "jobTitle": data.jobTitle,
      "url": data.url,
      "email": data.email,
      "image": data.image || `${this.baseUrl}/profile-image.jpg`,
      "sameAs": data.sameAs || [],
      "knowsAbout": data.skills || [],
      "worksFor": data.worksFor ? {
        "@type": "Organization",
        "name": data.worksFor
      } : undefined,
      "address": data.address ? {
        "@type": "PostalAddress",
        "addressLocality": data.address.locality,
        "addressCountry": data.address.country
      } : undefined,
      "telephone": data.telephone
    };

    // Remove undefined values
    Object.keys(structuredData).forEach(key => {
      if (structuredData[key as keyof typeof structuredData] === undefined) {
        delete structuredData[key as keyof typeof structuredData];
      }
    });

    return JSON.stringify(structuredData, null, 2);
  }

  /**
   * Generate website structured data
   */
  generateWebsiteStructuredData(data: {
    name: string;
    url: string;
    description: string;
    author: string;
    inLanguage?: string;
    keywords?: string[];
  }): string {
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": data.name,
      "url": data.url,
      "description": data.description,
      "inLanguage": data.inLanguage || "en-US",
      "keywords": data.keywords?.join(', '),
      "author": {
        "@type": "Person",
        "name": data.author
      },
      "potentialAction": {
        "@type": "SearchAction",
        "target": {
          "@type": "EntryPoint",
          "urlTemplate": `${data.url}/#search?q={search_term_string}`
        },
        "query-input": "required name=search_term_string"
      }
    };

    return JSON.stringify(structuredData, null, 2);
  }

  /**
   * Generate breadcrumb structured data
   */
  generateBreadcrumbStructuredData(breadcrumbs: Array<{
    name: string;
    url: string;
  }>): string {
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": breadcrumbs.map((crumb, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "name": crumb.name,
        "item": crumb.url
      }))
    };

    return JSON.stringify(structuredData, null, 2);
  }

  /**
   * Generate meta tags for better SEO
   */
  generateMetaTags(data: {
    title: string;
    description: string;
    keywords: string[];
    author: string;
    canonical: string;
    ogImage?: string;
    twitterHandle?: string;
  }): string {
    const ogImage = data.ogImage || `${this.baseUrl}/og-image.jpg`;
    
    return `
<!-- Primary Meta Tags -->
<title>${data.title}</title>
<meta name="title" content="${data.title}">
<meta name="description" content="${data.description}">
<meta name="keywords" content="${data.keywords.join(', ')}">
<meta name="author" content="${data.author}">
<meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
<link rel="canonical" href="${data.canonical}">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="website">
<meta property="og:url" content="${data.canonical}">
<meta property="og:title" content="${data.title}">
<meta property="og:description" content="${data.description}">
<meta property="og:image" content="${ogImage}">
<meta property="og:site_name" content="${data.author} Portfolio">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="${data.canonical}">
<meta property="twitter:title" content="${data.title}">
<meta property="twitter:description" content="${data.description}">
<meta property="twitter:image" content="${ogImage}">
${data.twitterHandle ? `<meta property="twitter:creator" content="${data.twitterHandle}">` : ''}

<!-- Additional SEO Meta Tags -->
<meta name="theme-color" content="#8B5CF6">
<meta name="msapplication-TileColor" content="#8B5CF6">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
<meta name="format-detection" content="telephone=no">
<meta name="mobile-web-app-capable" content="yes">
`.trim();
  }

  /**
   * Save generated files to localStorage for download
   */
  saveFilesToStorage(files: { [filename: string]: string }): void {
    Object.entries(files).forEach(([filename, content]) => {
      localStorage.setItem(`seo_file_${filename}`, content);
    });
  }

  /**
   * Download a file with given content
   */
  downloadFile(filename: string, content: string, mimeType: string = 'text/plain'): void {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * Generate and download all SEO files
   */
  generateAndDownloadSEOFiles(seoData: any): void {
    // Generate sitemap
    const sitemapEntries = this.generateDefaultSitemapEntries();
    const sitemap = this.generateSitemap(sitemapEntries);

    // Generate robots.txt
    const robotsTxt = this.generateRobotsTxt();

    // Generate structured data
    const personData = this.generatePersonStructuredData({
      name: seoData.structuredData?.person?.name || 'Nural Bhardwaj',
      jobTitle: seoData.structuredData?.person?.jobTitle || 'Full Stack Developer',
      url: seoData.meta?.canonical || this.baseUrl,
      email: seoData.structuredData?.person?.email || '<EMAIL>',
      telephone: seoData.structuredData?.person?.telephone,
      address: seoData.structuredData?.person?.address,
      sameAs: seoData.structuredData?.person?.sameAs || [],
      skills: seoData.meta?.keywords || []
    });

    const websiteData = this.generateWebsiteStructuredData({
      name: seoData.structuredData?.website?.name || 'Nural Bhardwaj Portfolio',
      url: seoData.meta?.canonical || this.baseUrl,
      description: seoData.meta?.description || 'Professional portfolio website',
      author: seoData.meta?.author || 'Nural Bhardwaj',
      keywords: seoData.meta?.keywords || []
    });

    // Save to localStorage
    this.saveFilesToStorage({
      'sitemap.xml': sitemap,
      'robots.txt': robotsTxt,
      'person-structured-data.json': personData,
      'website-structured-data.json': websiteData
    });

    // Download files
    this.downloadFile('sitemap.xml', sitemap, 'application/xml');
    this.downloadFile('robots.txt', robotsTxt, 'text/plain');
    this.downloadFile('person-structured-data.json', personData, 'application/json');
    this.downloadFile('website-structured-data.json', websiteData, 'application/json');
  }
}

// Export default instance
export const seoFileGenerator = new SEOFileGenerator();
