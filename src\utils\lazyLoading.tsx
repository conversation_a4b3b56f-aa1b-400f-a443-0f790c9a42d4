import React, { Suspense, lazy } from 'react';
import { motion } from 'framer-motion';

// Loading fallback component
const LazyLoadingFallback: React.FC<{ name?: string }> = ({ name = 'Component' }) => (
  <motion.div
    className="flex items-center justify-center p-8"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
  >
    <div className="flex flex-col items-center space-y-4">
      <motion.div
        className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full"
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      />
      <motion.p
        className="text-gray-400 text-sm"
        animate={{ opacity: [0.5, 1, 0.5] }}
        transition={{ duration: 1.5, repeat: Infinity }}
      >
        Loading {name}...
      </motion.p>
    </div>
  </motion.div>
);

// Enhanced lazy loading wrapper with error boundary
export const createLazyComponent = <T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallbackName?: string
) => {
  const LazyComponent = lazy(importFn);
  
  return React.forwardRef<any, React.ComponentProps<T>>((props, ref) => (
    <Suspense fallback={<LazyLoadingFallback name={fallbackName} />}>
      <LazyComponent {...props} ref={ref} />
    </Suspense>
  ));
};

// Preload function for better UX
export const preloadComponent = (importFn: () => Promise<any>) => {
  const componentImport = importFn();
  return componentImport;
};

// Lazy load admin components
export const LazyAdminDashboard = createLazyComponent(
  () => import('../components/admin/AdminDashboard'),
  'Admin Dashboard'
);

export const LazyAdminLogin = createLazyComponent(
  () => import('../components/admin/AdminLogin'),
  'Admin Login'
);

export const LazyProjectManager = createLazyComponent(
  () => import('../components/admin/ProjectManager'),
  'Project Manager'
);

export const LazyBlogManager = createLazyComponent(
  () => import('../components/admin/BlogManager'),
  'Blog Manager'
);

export const LazyAnalyticsManager = createLazyComponent(
  () => import('../components/admin/AnalyticsManager'),
  'Analytics Manager'
);

export const LazySecurityManager = createLazyComponent(
  () => import('../components/admin/SecurityManager'),
  'Security Manager'
);

export const LazyPerformanceManager = createLazyComponent(
  () => import('../components/admin/PerformanceManager'),
  'Performance Manager'
);

export const LazyBackupManager = createLazyComponent(
  () => import('../components/admin/BackupManager'),
  'Backup Manager'
);

export const LazyCloudSyncManager = createLazyComponent(
  () => import('../components/admin/CloudSyncManager'),
  'Cloud Sync Manager'
);

export const LazyNotificationManager = createLazyComponent(
  () => import('../components/admin/NotificationManager'),
  'Notification Manager'
);

export const LazyContactManager = createLazyComponent(
  () => import('../components/admin/ContactManager'),
  'Contact Manager'
);

export const LazyHeroManager = createLazyComponent(
  () => import('../components/admin/HeroManager'),
  'Hero Manager'
);

export const LazyProfileManager = createLazyComponent(
  () => import('../components/admin/ProfileManager'),
  'Profile Manager'
);

export const LazySettingsManager = createLazyComponent(
  () => import('../components/admin/SettingsManager'),
  'Settings Manager'
);

export const LazyThemeManager = createLazyComponent(
  () => import('../components/admin/ThemeManager'),
  'Theme Manager'
);

export const LazySEOManager = createLazyComponent(
  () => import('../components/admin/SEOManager'),
  'SEO Manager'
);

export const LazySEODashboard = createLazyComponent(
  () => import('../components/admin/SEODashboard'),
  'SEO Dashboard'
);

export const LazySecureTokenSetup = createLazyComponent(
  () => import('../components/admin/SecureTokenSetup'),
  'Secure Token Setup'
);

// Lazy load heavy components
export const LazyInteractiveResume = createLazyComponent(
  () => import('../components/InteractiveResume'),
  'Interactive Resume'
);

export const LazyAdvancedSEO = createLazyComponent(
  () => import('../components/AdvancedSEO'),
  'Advanced SEO'
);

export const LazySEOAnalytics = createLazyComponent(
  () => import('../components/SEOAnalytics'),
  'SEO Analytics'
);

export const LazySEODominance = createLazyComponent(
  () => import('../components/SEODominance'),
  'SEO Dominance'
);

export const LazyPWAManager = createLazyComponent(
  () => import('../components/PWAManager'),
  'PWA Manager'
);

// Preload critical components on user interaction
export const preloadAdminComponents = () => {
  preloadComponent(() => import('../components/admin/AdminDashboard'));
  preloadComponent(() => import('../components/admin/ProjectManager'));
  preloadComponent(() => import('../components/admin/BlogManager'));
};

export const preloadHeavyComponents = () => {
  preloadComponent(() => import('../components/InteractiveResume'));
  preloadComponent(() => import('../components/AdvancedSEO'));
  preloadComponent(() => import('../components/SEOAnalytics'));
};

// Hook for intersection-based preloading
export const useIntersectionPreload = (
  ref: React.RefObject<HTMLElement>,
  preloadFn: () => void,
  threshold = 0.1
) => {
  React.useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            preloadFn();
            observer.unobserve(element);
          }
        });
      },
      { threshold }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [ref, preloadFn, threshold]);
};

export default {
  createLazyComponent,
  preloadComponent,
  LazyLoadingFallback,
  preloadAdminComponents,
  preloadHeavyComponents,
  useIntersectionPreload
};
