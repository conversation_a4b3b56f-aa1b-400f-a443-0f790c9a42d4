// SEO File Generator Utility
interface SEOData {
  meta: {
    title: string;
    description: string;
    keywords: string[];
    author: string;
    canonicalUrl: string;
    robots: string;
  };
  openGraph: {
    title: string;
    description: string;
    image: string;
    url: string;
    siteName: string;
    type: string;
    locale: string;
  };
  twitter: {
    card: string;
    title: string;
    description: string;
    image: string;
    creator: string;
    site: string;
  };
  structuredData: {
    person: {
      name: string;
      jobTitle: string;
      description: string;
      url: string;
      email: string;
      telephone: string;
      address: {
        streetAddress: string;
        addressLocality: string;
        addressRegion: string;
        postalCode: string;
        addressCountry: string;
      };
      sameAs: string[];
    };
    website: {
      name: string;
      description: string;
      url: string;
      author: string;
      publisher: string;
      inLanguage: string;
    };
    localBusiness: {
      name: string;
      description: string;
      url: string;
      telephone: string;
      email: string;
      address: {
        streetAddress: string;
        addressLocality: string;
        addressRegion: string;
        postalCode: string;
        addressCountry: string;
      };
      openingHours: string[];
      priceRange: string;
    };
  };
  analytics: {
    googleAnalytics: string;
    googleSearchConsole: string;
    bingWebmaster: string;
    yandexWebmaster: string;
  };
  performance: {
    lazyLoading: boolean;
    imageOptimization: boolean;
    caching: boolean;
    compression: boolean;
  };
}

class SEOFileGenerator {
  generateSitemap(seoData: SEOData): string {
    const baseUrl = seoData.openGraph.url || 'https://nuralbhardwaj.me';
    const currentDate = new Date().toISOString().split('T')[0];

    return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">

  <!-- Homepage - Highest Priority -->
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
    <image:image>
      <image:loc>${baseUrl}/og-home-optimized.jpg</image:loc>
      <image:title>Nural Bhardwaj - Full Stack Developer Portfolio</image:title>
      <image:caption>Professional portfolio of Nural Bhardwaj, Senior Full Stack Developer</image:caption>
    </image:image>
  </url>

  <!-- About Page - High Priority -->
  <url>
    <loc>${baseUrl}/#about</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>
    <image:image>
      <image:loc>${baseUrl}/og-about-optimized.jpg</image:loc>
      <image:title>About Nural Bhardwaj - Senior Full Stack Developer</image:title>
      <image:caption>Learn about Nural Bhardwaj's experience and expertise in full stack development</image:caption>
    </image:image>
  </url>

  <!-- Projects Page - Very High Priority -->
  <url>
    <loc>${baseUrl}/#projects</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.95</priority>
    <image:image>
      <image:loc>${baseUrl}/og-projects-optimized.jpg</image:loc>
      <image:title>50+ Successful Projects Portfolio</image:title>
      <image:caption>Explore 50+ successful web development projects by Nural Bhardwaj</image:caption>
    </image:image>
  </url>

  <!-- Blog Page - High Priority for SEO -->
  <url>
    <loc>${baseUrl}/#blog</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.85</priority>
    <image:image>
      <image:loc>${baseUrl}/og-blog-optimized.jpg</image:loc>
      <image:title>Web Development Blog & Tutorials</image:title>
      <image:caption>Latest insights on React, Node.js, TypeScript and web development</image:caption>
    </image:image>
  </url>

  <!-- Resume Page - Important for Hiring -->
  <url>
    <loc>${baseUrl}/#resume</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
    <image:image>
      <image:loc>${baseUrl}/og-resume-optimized.jpg</image:loc>
      <image:title>Nural Bhardwaj Resume - Senior Full Stack Developer</image:title>
      <image:caption>Professional resume of Nural Bhardwaj with 5+ years experience</image:caption>
    </image:image>
  </url>

  <!-- Contact Page - High Priority for Business -->
  <url>
    <loc>${baseUrl}/#contact</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.9</priority>
    <image:image>
      <image:loc>${baseUrl}/og-contact-optimized.jpg</image:loc>
      <image:title>Hire Nural Bhardwaj - Contact for Projects</image:title>
      <image:caption>Get in touch with Nural Bhardwaj for your next web development project</image:caption>
    </image:image>
  </url>

  <!-- Skills Page -->
  <url>
    <loc>${baseUrl}/#skills</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>

  <!-- Experience Page -->
  <url>
    <loc>${baseUrl}/#experience</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>

  <!-- Services Pages for Better SEO -->
  <url>
    <loc>${baseUrl}/services/full-stack-development</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>

  <url>
    <loc>${baseUrl}/services/react-development</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>

  <url>
    <loc>${baseUrl}/services/ui-ux-design</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>

  <url>
    <loc>${baseUrl}/services/web-development</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>

  <!-- Technology Pages -->
  <url>
    <loc>${baseUrl}/technologies/react</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>

  <url>
    <loc>${baseUrl}/technologies/nodejs</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>

  <url>
    <loc>${baseUrl}/technologies/typescript</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>

</urlset>`;
  }

  generateRobotsTxt(seoData: SEOData): string {
    const baseUrl = seoData.openGraph.url || 'https://nuralbhardwaj.me';

    return `# Robots.txt for ${seoData.structuredData.website.name}
# Generated automatically by Advanced Portfolio CMS
# Optimized for maximum SEO performance

# Allow all search engines to crawl the site
User-agent: *
Allow: /

# Sitemap locations for better indexing
Sitemap: ${baseUrl}/sitemap.xml
Sitemap: ${baseUrl}/sitemap-images.xml
Sitemap: ${baseUrl}/sitemap-videos.xml

# Crawl delay for better server performance
Crawl-delay: 1

# Disallow admin and private areas
Disallow: /admin
Disallow: /admin/*
Disallow: /private/
Disallow: /temp/
Disallow: /.git/
Disallow: /node_modules/
Disallow: /src/
Disallow: /build/
Disallow: /.env
Disallow: /package.json
Disallow: /package-lock.json

# Allow important pages for SEO
Allow: /
Allow: /#about
Allow: /#projects
Allow: /#blog
Allow: /#contact
Allow: /#resume
Allow: /#skills
Allow: /#experience
Allow: /services/
Allow: /technologies/

# Specific rules for major search engines
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Slurp
Allow: /
Crawl-delay: 1

User-agent: DuckDuckBot
Allow: /
Crawl-delay: 1

User-agent: Baiduspider
Allow: /
Crawl-delay: 2

User-agent: YandexBot
Allow: /
Crawl-delay: 1

User-agent: facebookexternalhit
Allow: /

User-agent: Twitterbot
Allow: /

User-agent: LinkedInBot
Allow: /

User-agent: WhatsApp
Allow: /

User-agent: TelegramBot
Allow: /

# Block bad bots and scrapers
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

User-agent: SemrushBot
Disallow: /

User-agent: MajesticSEO
Disallow: /

# Additional sitemap references
Sitemap: ${baseUrl}/sitemap.xml
Sitemap: ${baseUrl}/rss.xml

# Host directive for preferred domain
Host: ${baseUrl.replace('https://', '').replace('http://', '')}

# Cache directive for better performance
Cache-delay: 86400`;
  }

  generateStructuredData(seoData: SEOData): string {
    const structuredData = {
      "@context": "https://schema.org",
      "@graph": [
        {
          "@type": "Person",
          "@id": `${seoData.openGraph.url}#person`,
          "name": seoData.structuredData.person.name,
          "jobTitle": seoData.structuredData.person.jobTitle,
          "description": seoData.structuredData.person.description,
          "url": seoData.structuredData.person.url,
          "email": seoData.structuredData.person.email,
          "telephone": seoData.structuredData.person.telephone,
          "address": {
            "@type": "PostalAddress",
            "streetAddress": seoData.structuredData.person.address.streetAddress,
            "addressLocality": seoData.structuredData.person.address.addressLocality,
            "addressRegion": seoData.structuredData.person.address.addressRegion,
            "postalCode": seoData.structuredData.person.address.postalCode,
            "addressCountry": seoData.structuredData.person.address.addressCountry
          },
          "sameAs": seoData.structuredData.person.sameAs
        },
        {
          "@type": "WebSite",
          "@id": `${seoData.openGraph.url}#website`,
          "name": seoData.structuredData.website.name,
          "description": seoData.structuredData.website.description,
          "url": seoData.structuredData.website.url,
          "author": {
            "@id": `${seoData.openGraph.url}#person`
          },
          "publisher": {
            "@id": `${seoData.openGraph.url}#person`
          },
          "inLanguage": seoData.structuredData.website.inLanguage
        },
        {
          "@type": "LocalBusiness",
          "@id": `${seoData.openGraph.url}#business`,
          "name": seoData.structuredData.localBusiness.name,
          "description": seoData.structuredData.localBusiness.description,
          "url": seoData.structuredData.localBusiness.url,
          "telephone": seoData.structuredData.localBusiness.telephone,
          "email": seoData.structuredData.localBusiness.email,
          "address": {
            "@type": "PostalAddress",
            "streetAddress": seoData.structuredData.localBusiness.address.streetAddress,
            "addressLocality": seoData.structuredData.localBusiness.address.addressLocality,
            "addressRegion": seoData.structuredData.localBusiness.address.addressRegion,
            "postalCode": seoData.structuredData.localBusiness.address.postalCode,
            "addressCountry": seoData.structuredData.localBusiness.address.addressCountry
          },
          "openingHours": seoData.structuredData.localBusiness.openingHours,
          "priceRange": seoData.structuredData.localBusiness.priceRange
        }
      ]
    };

    return JSON.stringify(structuredData, null, 2);
  }

  downloadFile(content: string, filename: string, contentType: string = 'text/plain'): void {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  generateAndDownloadSEOFiles(seoData: SEOData): void {
    try {
      // Generate sitemap
      const sitemap = this.generateSitemap(seoData);
      this.downloadFile(sitemap, 'sitemap.xml', 'application/xml');

      // Generate robots.txt
      const robotsTxt = this.generateRobotsTxt(seoData);
      this.downloadFile(robotsTxt, 'robots.txt', 'text/plain');

      // Generate structured data
      const structuredData = this.generateStructuredData(seoData);
      this.downloadFile(structuredData, 'structured-data.json', 'application/json');

    } catch (error) {
      console.error('Error generating SEO files:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const seoFileGenerator = new SEOFileGenerator();
