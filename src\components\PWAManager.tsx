import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Download,
  Bell,
  X,
  Smartphone,
  Monitor,
  Wifi,
  WifiOff,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import toast from 'react-hot-toast';
import { SuccessToast, ErrorToast, InfoToast } from './CustomToast';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const PWAManager: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [notificationPermission, setNotificationPermission] = useState<NotificationPermission>('default');
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [showNotificationPrompt, setShowNotificationPrompt] = useState(false);
  const [swRegistration, setSwRegistration] = useState<ServiceWorkerRegistration | null>(null);

  useEffect(() => {
    // Check if app is already installed
    const checkInstallStatus = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isInWebAppiOS = (window.navigator as any).standalone === true;
      setIsInstalled(isStandalone || isInWebAppiOS);
    };

    checkInstallStatus();

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      setIsInstallable(true);
      
      // Show install prompt after 30 seconds if not installed
      setTimeout(() => {
        if (!isInstalled) {
          setShowInstallPrompt(true);
        }
      }, 30000);
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setIsInstallable(false);
      setShowInstallPrompt(false);
      
      toast(() => (
        <SuccessToast
          message="Portfolio installed successfully! 🎉"
          icon={<CheckCircle className="w-5 h-5 text-green-400" />}
        />
      ));
    };

    // Online/offline status
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [isInstalled]);

  useEffect(() => {
    // Register service worker
    const registerServiceWorker = async () => {
      if ('serviceWorker' in navigator) {
        try {
          const registration = await navigator.serviceWorker.register('/sw.js');
          setSwRegistration(registration);
          
          console.log('Service Worker registered successfully:', registration);
          
          // Check for updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  toast(() => (
                    <InfoToast
                      message="New version available! Refresh to update."
                      icon={<Download className="w-5 h-5 text-blue-400" />}
                    />
                  ));
                }
              });
            }
          });
          
        } catch (error) {
          console.error('Service Worker registration failed:', error);
        }
      }
    };

    registerServiceWorker();
  }, []);

  useEffect(() => {
    // Check notification permission
    if ('Notification' in window) {
      setNotificationPermission(Notification.permission);
      
      // Show notification prompt after 60 seconds if not granted
      if (Notification.permission === 'default') {
        setTimeout(() => {
          setShowNotificationPrompt(true);
        }, 60000);
      }
    }
  }, []);

  const handleInstallApp = async () => {
    if (!deferredPrompt) return;

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
        setShowInstallPrompt(false);
      } else {
        console.log('User dismissed the install prompt');
      }
      
      setDeferredPrompt(null);
      setIsInstallable(false);
    } catch (error) {
      console.error('Error during app installation:', error);
      toast(() => (
        <ErrorToast
          message="Installation failed. Please try again."
          icon={<AlertCircle className="w-5 h-5 text-red-400" />}
        />
      ));
    }
  };

  const handleEnableNotifications = async () => {
    if (!('Notification' in window)) {
      toast(() => (
        <ErrorToast
          message="Notifications not supported in this browser"
          icon={<AlertCircle className="w-5 h-5 text-red-400" />}
        />
      ));
      return;
    }

    try {
      const permission = await Notification.requestPermission();
      setNotificationPermission(permission);
      
      if (permission === 'granted') {
        setShowNotificationPrompt(false);
        
        // Subscribe to push notifications
        if (swRegistration) {
          await subscribeToPushNotifications();
        }
        
        // Send welcome notification
        new Notification('Welcome to Nural\'s Portfolio! 🎉', {
          body: 'You\'ll now receive updates about new projects and blog posts.',
          icon: '/icons/icon-192x192.png',
          badge: '/icons/icon-72x72.png',
          tag: 'welcome-notification'
        });
        
        toast(() => (
          <SuccessToast
            message="Notifications enabled successfully! 🔔"
            icon={<Bell className="w-5 h-5 text-green-400" />}
          />
        ));
      } else {
        toast(() => (
          <ErrorToast
            message="Notifications permission denied"
            icon={<AlertCircle className="w-5 h-5 text-red-400" />}
          />
        ));
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      toast(() => (
        <ErrorToast
          message="Failed to enable notifications"
          icon={<AlertCircle className="w-5 h-5 text-red-400" />}
        />
      ));
    }
  };

  const subscribeToPushNotifications = async () => {
    if (!swRegistration) return;

    try {
      // Generate VAPID keys for production use
      const vapidPublicKey = 'BEl62iUYgUivxIkv69yViEuiBIa40HI80NM9f4LUjqLDkFXaVUAN0QjQjhqHMFfkFKRXCu3iLAi8bEZQUTiS8N8';
      
      const subscription = await swRegistration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(vapidPublicKey)
      });

      // Send subscription to your server
      console.log('Push subscription:', subscription);
      
      // Store subscription locally for demo purposes
      localStorage.setItem('push-subscription', JSON.stringify(subscription));
      
    } catch (error) {
      console.error('Error subscribing to push notifications:', error);
    }
  };

  const urlBase64ToUint8Array = (base64String: string) => {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  };

  const sendTestNotification = () => {
    if (notificationPermission === 'granted') {
      new Notification('Test Notification 🚀', {
        body: 'This is a test notification from Nural\'s Portfolio!',
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        tag: 'test-notification',
        vibrate: [200, 100, 200],
        actions: [
          { action: 'view', title: 'View Portfolio' },
          { action: 'dismiss', title: 'Dismiss' }
        ]
      });
    }
  };

  return (
    <>
      {/* Online/Offline Indicator - Fixed positioning for mobile */}
      <div className="online-indicator fixed top-20 left-4 z-40 sm:top-4 sm:left-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className={`flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium sm:px-3 sm:py-2 sm:text-sm ${
            isOnline
              ? 'bg-green-500/20 text-green-400 border border-green-500/30'
              : 'bg-red-500/20 text-red-400 border border-red-500/30'
          }`}
        >
          {isOnline ? (
            <Wifi className="w-3 h-3 sm:w-4 sm:h-4" />
          ) : (
            <WifiOff className="w-3 h-3 sm:w-4 sm:h-4" />
          )}
          <span className="hidden sm:inline">{isOnline ? 'Online' : 'Offline'}</span>
          <span className="sm:hidden">{isOnline ? '●' : '○'}</span>
        </motion.div>
      </div>

      {/* Install App Prompt */}
      <AnimatePresence>
        {showInstallPrompt && isInstallable && !isInstalled && (
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 100 }}
            className="fixed bottom-4 right-4 z-50 max-w-sm"
          >
            <div className="bg-gradient-to-br from-purple-900/90 to-blue-900/90 backdrop-blur-sm border border-purple-500/30 rounded-2xl p-6 shadow-2xl">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-purple-600/20 rounded-xl">
                    <Smartphone className="w-6 h-6 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">Install Portfolio App</h3>
                    <p className="text-gray-300 text-sm">Get the full app experience!</p>
                  </div>
                </div>
                <button
                  onClick={() => setShowInstallPrompt(false)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center space-x-2 text-sm text-gray-300">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span>Works offline</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-300">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span>Fast loading</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-300">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span>Push notifications</span>
                </div>
              </div>
              
              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => setShowInstallPrompt(false)}
                  className="flex-1 px-4 py-2 text-gray-300 hover:text-white transition-colors text-sm"
                >
                  Maybe Later
                </button>
                <button
                  onClick={handleInstallApp}
                  className="flex-1 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-xl transition-all duration-200 text-sm font-medium"
                >
                  Install App
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Notification Permission Prompt */}
      <AnimatePresence>
        {showNotificationPrompt && notificationPermission === 'default' && (
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 100 }}
            className="fixed bottom-4 left-4 z-50 max-w-sm"
          >
            <div className="bg-gradient-to-br from-blue-900/90 to-cyan-900/90 backdrop-blur-sm border border-blue-500/30 rounded-2xl p-6 shadow-2xl">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-600/20 rounded-xl">
                    <Bell className="w-6 h-6 text-blue-400" />
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">Stay Updated</h3>
                    <p className="text-gray-300 text-sm">Get notified about new projects!</p>
                  </div>
                </div>
                <button
                  onClick={() => setShowNotificationPrompt(false)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              <p className="text-gray-300 text-sm mb-4">
                Enable notifications to receive updates about new projects, blog posts, and portfolio updates.
              </p>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowNotificationPrompt(false)}
                  className="flex-1 px-4 py-2 text-gray-300 hover:text-white transition-colors text-sm"
                >
                  Not Now
                </button>
                <button
                  onClick={handleEnableNotifications}
                  className="flex-1 px-4 py-2 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white rounded-xl transition-all duration-200 text-sm font-medium"
                >
                  Enable
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Development Tools */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50">
          <div className="flex space-x-2">
            {isInstallable && !isInstalled && (
              <button
                onClick={handleInstallApp}
                className="px-3 py-2 bg-purple-600/20 text-purple-400 rounded-lg hover:bg-purple-600/30 transition-all duration-200 text-sm flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Install</span>
              </button>
            )}
            
            {notificationPermission === 'granted' && (
              <button
                onClick={sendTestNotification}
                className="px-3 py-2 bg-blue-600/20 text-blue-400 rounded-lg hover:bg-blue-600/30 transition-all duration-200 text-sm flex items-center space-x-2"
              >
                <Bell className="w-4 h-4" />
                <span>Test</span>
              </button>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default PWAManager;
