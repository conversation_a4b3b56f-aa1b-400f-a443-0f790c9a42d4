import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Send, Shield, AlertTriangle, CheckCircle, Clock } from 'lucide-react';
import toast from 'react-hot-toast';
import { SuccessToast, ErrorToast, InfoToast } from './CustomToast';
import { securityService } from '../services/securityService';
import { captchaService } from '../services/captchaService';
import emailjs from '@emailjs/browser';

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  subject?: string;
  message?: string;
  captcha?: string;
  rateLimit?: string;
}

const SecureContactForm: React.FC = () => {
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [captchaToken, setCaptchaToken] = useState<string>('');
  const [captchaRequired, setCaptchaRequired] = useState(false);
  const [rateLimitInfo, setRateLimitInfo] = useState<{
    blocked: boolean;
    remainingTime: number;
  }>({ blocked: false, remainingTime: 0 });

  useEffect(() => {
    // Initialize CAPTCHA service
    captchaService.initialize({ provider: 'custom', theme: 'dark' });
    
    // Check rate limit status
    checkRateLimit();
    
    // Set up rate limit check interval
    const interval = setInterval(checkRateLimit, 1000);
    return () => clearInterval(interval);
  }, []);

  const checkRateLimit = () => {
    const identifier = securityService.getClientIdentifier();
    const blocked = securityService.isBlocked(identifier);
    const remainingTime = securityService.getBlockTimeRemaining(identifier);
    
    setRateLimitInfo({ blocked, remainingTime });
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Sanitize and validate inputs
    const sanitizedData = {
      name: securityService.sanitizeInput(formData.name),
      email: securityService.sanitizeInput(formData.email),
      subject: securityService.sanitizeInput(formData.subject),
      message: securityService.sanitizeInput(formData.message)
    };

    // Name validation
    if (!sanitizedData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (sanitizedData.name.length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    } else if (sanitizedData.name.length > 50) {
      newErrors.name = 'Name must be less than 50 characters';
    }

    // Email validation
    if (!sanitizedData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!securityService.validateEmail(sanitizedData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Subject validation
    if (!sanitizedData.subject.trim()) {
      newErrors.subject = 'Subject is required';
    } else if (sanitizedData.subject.length < 5) {
      newErrors.subject = 'Subject must be at least 5 characters';
    } else if (sanitizedData.subject.length > 100) {
      newErrors.subject = 'Subject must be less than 100 characters';
    }

    // Message validation
    if (!sanitizedData.message.trim()) {
      newErrors.message = 'Message is required';
    } else if (sanitizedData.message.length < 10) {
      newErrors.message = 'Message must be at least 10 characters';
    } else if (sanitizedData.message.length > 1000) {
      newErrors.message = 'Message must be less than 1000 characters';
    }

    // Check for suspicious content
    const allContent = `${sanitizedData.name} ${sanitizedData.email} ${sanitizedData.subject} ${sanitizedData.message}`;
    if (securityService.detectSuspiciousContent(allContent)) {
      newErrors.message = 'Message contains suspicious content';
    }

    // CAPTCHA validation
    if (captchaRequired && !captchaToken) {
      newErrors.captcha = 'Please complete the CAPTCHA verification';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof ContactFormData, value: string) => {
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors({ ...errors, [field]: undefined });
    }

    setFormData({ ...formData, [field]: value });
  };

  const handleCaptchaComplete = (token: string) => {
    setCaptchaToken(token);
    if (errors.captcha) {
      setErrors({ ...errors, captcha: undefined });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isSubmitting) return;

    // Check rate limiting
    const identifier = securityService.getClientIdentifier();
    const rateLimitPassed = securityService.checkRateLimit(identifier, 'contact');
    
    if (!rateLimitPassed) {
      const remainingTime = Math.ceil(securityService.getBlockTimeRemaining(identifier) / 1000);
      setErrors({ 
        rateLimit: `Too many requests. Please wait ${remainingTime} seconds before trying again.` 
      });
      
      toast(() => (
        <ErrorToast
          message={`Rate limit exceeded. Please wait ${remainingTime} seconds.`}
          icon={<Clock className="w-5 h-5 text-red-400" />}
        />
      ));
      return;
    }

    // Determine if CAPTCHA is required (after 2 submissions)
    const metrics = securityService.getMetrics();
    if (metrics.totalRequests > 2) {
      setCaptchaRequired(true);
    }

    // Validate form
    if (!validateForm()) {
      toast(() => (
        <ErrorToast
          message="Please fix the errors in the form"
          icon={<AlertTriangle className="w-5 h-5 text-red-400" />}
        />
      ));
      return;
    }

    // Verify CAPTCHA if required
    if (captchaRequired) {
      const captchaValid = await captchaService.verify(captchaToken);
      if (!captchaValid) {
        setErrors({ captcha: 'CAPTCHA verification failed' });
        toast(() => (
          <ErrorToast
            message="CAPTCHA verification failed"
            icon={<Shield className="w-5 h-5 text-red-400" />}
          />
        ));
        return;
      }
    }

    setIsSubmitting(true);

    try {
      // Sanitize form data before sending
      const sanitizedData = {
        name: securityService.sanitizeInput(formData.name),
        email: securityService.sanitizeInput(formData.email),
        subject: securityService.sanitizeInput(formData.subject),
        message: securityService.sanitizeInput(formData.message)
      };

      // Send email using EmailJS
      const templateParams = {
        from_name: sanitizedData.name,
        from_email: sanitizedData.email,
        subject: sanitizedData.subject,
        message: sanitizedData.message,
        to_name: 'Nural Bhardwaj',
        security_token: captchaToken || 'no-captcha',
        timestamp: new Date().toISOString(),
        user_agent: navigator.userAgent.substring(0, 100) // Limit length
      };

      await emailjs.send(
        'service_cs4dp9v',
        'template_bjih7d3',
        templateParams,
        'N0tsQS6nGR8545j5q'
      );

      // Success
      toast(() => (
        <SuccessToast
          message="Message sent successfully! I'll get back to you soon."
          icon={<CheckCircle className="w-5 h-5 text-green-400" />}
        />
      ));

      // Reset form
      setFormData({ name: '', email: '', subject: '', message: '' });
      setCaptchaToken('');
      setCaptchaRequired(false);
      setErrors({});

      // Reset CAPTCHA
      captchaService.reset('contact-captcha');

    } catch (error) {
      console.error('Contact form error:', error);
      
      toast(() => (
        <ErrorToast
          message="Failed to send message. Please try again."
          icon={<AlertTriangle className="w-5 h-5 text-red-400" />}
        />
      ));
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCaptcha = () => {
    if (!captchaRequired) return null;

    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-300">
          Security Verification
        </label>
        <div 
          id="contact-captcha"
          className="min-h-[100px] flex items-center justify-center"
        >
          {/* CAPTCHA will be rendered here */}
          <button
            type="button"
            onClick={() => {
              captchaService.renderWidget('contact-captcha', handleCaptchaComplete);
            }}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Load Security Challenge
          </button>
        </div>
        {errors.captcha && (
          <p className="text-red-400 text-sm">{errors.captcha}</p>
        )}
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="max-w-2xl mx-auto"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Security Status */}
        <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-xl border border-gray-700/50">
          <div className="flex items-center space-x-3">
            <Shield className="w-5 h-5 text-green-400" />
            <span className="text-sm text-gray-300">Secure Form</span>
          </div>
          <div className="flex items-center space-x-2 text-xs text-gray-400">
            <span>Rate Limited</span>
            <span>•</span>
            <span>CAPTCHA Protected</span>
            <span>•</span>
            <span>Input Sanitized</span>
          </div>
        </div>

        {/* Rate Limit Warning */}
        {rateLimitInfo.blocked && (
          <div className="p-4 bg-red-900/20 border border-red-500/30 rounded-xl">
            <div className="flex items-center space-x-3">
              <Clock className="w-5 h-5 text-red-400" />
              <div>
                <p className="text-red-400 font-medium">Rate Limit Exceeded</p>
                <p className="text-red-300 text-sm">
                  Please wait {Math.ceil(rateLimitInfo.remainingTime / 1000)} seconds before submitting again.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Form Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`w-full px-4 py-3 bg-gray-800/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ${
                errors.name ? 'border-red-500' : 'border-gray-600/50'
              }`}
              placeholder="Your full name"
              maxLength={50}
              disabled={isSubmitting || rateLimitInfo.blocked}
            />
            {errors.name && (
              <p className="text-red-400 text-sm mt-1">{errors.name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Email *
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className={`w-full px-4 py-3 bg-gray-800/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ${
                errors.email ? 'border-red-500' : 'border-gray-600/50'
              }`}
              placeholder="<EMAIL>"
              maxLength={254}
              disabled={isSubmitting || rateLimitInfo.blocked}
            />
            {errors.email && (
              <p className="text-red-400 text-sm mt-1">{errors.email}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Subject *
          </label>
          <input
            type="text"
            value={formData.subject}
            onChange={(e) => handleInputChange('subject', e.target.value)}
            className={`w-full px-4 py-3 bg-gray-800/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 ${
              errors.subject ? 'border-red-500' : 'border-gray-600/50'
            }`}
            placeholder="What's this about?"
            maxLength={100}
            disabled={isSubmitting || rateLimitInfo.blocked}
          />
          {errors.subject && (
            <p className="text-red-400 text-sm mt-1">{errors.subject}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Message *
          </label>
          <textarea
            value={formData.message}
            onChange={(e) => handleInputChange('message', e.target.value)}
            rows={6}
            className={`w-full px-4 py-3 bg-gray-800/50 border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none ${
              errors.message ? 'border-red-500' : 'border-gray-600/50'
            }`}
            placeholder="Tell me about your project or question..."
            maxLength={1000}
            disabled={isSubmitting || rateLimitInfo.blocked}
          />
          <div className="flex justify-between items-center mt-1">
            {errors.message ? (
              <p className="text-red-400 text-sm">{errors.message}</p>
            ) : (
              <div />
            )}
            <p className="text-gray-400 text-sm">
              {formData.message.length}/1000
            </p>
          </div>
        </div>

        {/* CAPTCHA */}
        {renderCaptcha()}

        {/* Rate Limit Error */}
        {errors.rateLimit && (
          <div className="p-4 bg-red-900/20 border border-red-500/30 rounded-xl">
            <p className="text-red-400 text-sm">{errors.rateLimit}</p>
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting || rateLimitInfo.blocked}
          className={`w-full px-8 py-4 rounded-xl font-medium transition-all duration-200 flex items-center justify-center space-x-3 ${
            isSubmitting || rateLimitInfo.blocked
              ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
              : 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white shadow-lg hover:shadow-xl'
          }`}
        >
          {isSubmitting ? (
            <>
              <div className="w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
              <span>Sending...</span>
            </>
          ) : (
            <>
              <Send className="w-5 h-5" />
              <span>Send Message</span>
            </>
          )}
        </button>

        {/* Security Notice */}
        <div className="text-center text-xs text-gray-400">
          <p>
            This form is protected by advanced security measures including rate limiting,
            CAPTCHA verification, and input sanitization.
          </p>
        </div>
      </form>
    </motion.div>
  );
};

export default SecureContactForm;
