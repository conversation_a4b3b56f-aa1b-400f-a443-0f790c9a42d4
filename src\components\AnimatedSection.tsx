import React, { useRef, useState } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { useScrollAnimation } from '../hooks/useScrollAnimation';
import {
  fadeInUp,
  fadeInDown,
  fadeInLeft,
  fadeInRight,
  scaleIn,
  slideInUp,
  staggerContainer,
  staggerItem,
  viewportOptions,
} from '../utils/animations';

interface AnimatedSectionProps {
  children: React.ReactNode;
  animation?: 'fadeInUp' | 'fadeInDown' | 'fadeInLeft' | 'fadeInRight' | 'scaleIn' | 'slideInUp' | 'stagger';
  delay?: number;
  className?: string;
  id?: string;
  staggerChildren?: boolean;
  customVariants?: any;
}

const AnimatedSection: React.FC<AnimatedSectionProps> = ({
  children,
  animation = 'fadeInUp',
  delay = 0,
  className = '',
  id,
  staggerChildren = false,
  customVariants,
}) => {
  const { ref, controls } = useScrollAnimation({ delay });

  const getVariants = () => {
    if (customVariants) return customVariants;
    
    switch (animation) {
      case 'fadeInUp':
        return fadeInUp;
      case 'fadeInDown':
        return fadeInDown;
      case 'fadeInLeft':
        return fadeInLeft;
      case 'fadeInRight':
        return fadeInRight;
      case 'scaleIn':
        return scaleIn;
      case 'slideInUp':
        return slideInUp;
      case 'stagger':
        return staggerContainer;
      default:
        return fadeInUp;
    }
  };

  return (
    <motion.section
      ref={ref}
      id={id}
      className={className}
      variants={getVariants()}
      initial="hidden"
      animate={controls}
      viewport={viewportOptions}
    >
      {staggerChildren ? (
        React.Children.map(children, (child, index) => (
          <motion.div key={index} variants={staggerItem}>
            {child}
          </motion.div>
        ))
      ) : (
        children
      )}
    </motion.section>
  );
};

// Specialized animated components
export const AnimatedCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  delay?: number;
  hoverEffect?: boolean;
}> = ({ children, className = '', delay = 0, hoverEffect = true }) => {
  const { ref, controls } = useScrollAnimation({ delay });

  return (
    <motion.div
      ref={ref}
      className={className}
      variants={scaleIn}
      initial="hidden"
      animate={controls}
      viewport={viewportOptions}
      whileHover={hoverEffect ? {
        scale: 1.02,
        y: -8,
        boxShadow: '0 25px 50px rgba(139, 92, 246, 0.3)',
        transition: { duration: 0.3 }
      } : undefined}
    >
      {children}
    </motion.div>
  );
};

export const AnimatedText: React.FC<{
  children: React.ReactNode;
  className?: string;
  delay?: number;
  animation?: 'fadeInUp' | 'fadeInDown' | 'fadeInLeft' | 'fadeInRight';
}> = ({ children, className = '', delay = 0, animation = 'fadeInUp' }) => {
  const { ref, controls } = useScrollAnimation({ delay });

  const getVariants = () => {
    switch (animation) {
      case 'fadeInUp':
        return fadeInUp;
      case 'fadeInDown':
        return fadeInDown;
      case 'fadeInLeft':
        return fadeInLeft;
      case 'fadeInRight':
        return fadeInRight;
      default:
        return fadeInUp;
    }
  };

  return (
    <motion.div
      ref={ref}
      className={className}
      variants={getVariants()}
      initial="hidden"
      animate={controls}
      viewport={viewportOptions}
    >
      {children}
    </motion.div>
  );
};

export const AnimatedList: React.FC<{
  children: React.ReactNode;
  className?: string;
  staggerDelay?: number;
}> = ({ children, className = '', staggerDelay = 0.1 }) => {
  const { ref, controls } = useScrollAnimation();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { duration: 0.6, ease: [0.4, 0, 0.2, 1] },
    },
  };

  return (
    <motion.div
      ref={ref}
      className={className}
      variants={containerVariants}
      initial="hidden"
      animate={controls}
      viewport={viewportOptions}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div key={index} variants={itemVariants}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};

// Parallax wrapper
export const ParallaxSection: React.FC<{
  children: React.ReactNode;
  speed?: number;
  className?: string;
}> = ({ children, speed = 0.5, className = '' }) => {
  const { scrollY } = useScroll();
  const y = useTransform(scrollY, [0, 1000], [0, -speed * 1000]);

  return (
    <motion.div className={className} style={{ y }}>
      {children}
    </motion.div>
  );
};

// Magnetic hover effect component
export const MagneticElement: React.FC<{
  children: React.ReactNode;
  strength?: number;
  className?: string;
}> = ({ children, strength = 0.3, className = '' }) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const ref = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!ref.current) return;
    
    const rect = ref.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    setMousePosition({
      x: (e.clientX - centerX) * strength,
      y: (e.clientY - centerY) * strength,
    });
  };

  const handleMouseLeave = () => {
    setMousePosition({ x: 0, y: 0 });
  };

  return (
    <motion.div
      ref={ref}
      className={className}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      animate={{
        x: mousePosition.x,
        y: mousePosition.y,
      }}
      transition={{
        type: 'spring',
        stiffness: 150,
        damping: 15,
      }}
    >
      {children}
    </motion.div>
  );
};

// Scroll progress indicator
export const ScrollProgress: React.FC = () => {
  const { scrollYProgress } = useScroll();

  return (
    <motion.div
      className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 to-cyan-500 z-50 origin-left"
      style={{ scaleX: scrollYProgress }}
    />
  );
};

export default AnimatedSection;
