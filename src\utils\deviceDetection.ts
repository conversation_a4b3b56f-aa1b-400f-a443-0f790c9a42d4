/**
 * Advanced Device Detection Utility
 * Detects device type, capabilities, and performance characteristics
 */

export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLowEndDevice: boolean;
  hasTouch: boolean;
  screenSize: 'small' | 'medium' | 'large' | 'xlarge';
  connectionSpeed: 'slow' | 'fast' | 'unknown';
  reducedMotion: boolean;
  devicePixelRatio: number;
  maxTextureSize: number;
  supportsWebGL: boolean;
  supportsIntersectionObserver: boolean;
  supportsResizeObserver: boolean;
}

class DeviceDetectionService {
  private deviceInfo: DeviceInfo | null = null;

  /**
   * Get comprehensive device information
   */
  getDeviceInfo(): DeviceInfo {
    if (this.deviceInfo) {
      return this.deviceInfo;
    }

    this.deviceInfo = {
      isMobile: this.isMobile(),
      isTablet: this.isTablet(),
      isDesktop: this.isDesktop(),
      isLowEndDevice: this.isLowEndDevice(),
      hasTouch: this.hasTouch(),
      screenSize: this.getScreenSize(),
      connectionSpeed: this.getConnectionSpeed(),
      reducedMotion: this.prefersReducedMotion(),
      devicePixelRatio: this.getDevicePixelRatio(),
      maxTextureSize: this.getMaxTextureSize(),
      supportsWebGL: this.supportsWebGL(),
      supportsIntersectionObserver: this.supportsIntersectionObserver(),
      supportsResizeObserver: this.supportsResizeObserver(),
    };

    return this.deviceInfo;
  }

  /**
   * Check if device is mobile
   */
  private isMobile(): boolean {
    if (typeof window === 'undefined') return false;

    const userAgent = navigator.userAgent.toLowerCase();
    const mobileKeywords = [
      'android', 'iphone', 'ipod', 'blackberry',
      'windows phone', 'opera mini', 'iemobile'
    ];

    // More specific mobile detection - only use user agent, not screen size
    const isMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword));

    return isMobileUA;
  }

  /**
   * Check if device is tablet
   */
  private isTablet(): boolean {
    if (typeof window === 'undefined') return false;
    
    const userAgent = navigator.userAgent.toLowerCase();
    const tabletKeywords = ['ipad', 'tablet', 'kindle', 'playbook', 'silk'];
    const isTabletUA = tabletKeywords.some(keyword => userAgent.includes(keyword));
    const isTabletScreen = window.innerWidth > 768 && window.innerWidth <= 1024;
    
    return isTabletUA || (isTabletScreen && this.hasTouch());
  }

  /**
   * Check if device is desktop
   */
  private isDesktop(): boolean {
    return !this.isMobile() && !this.isTablet();
  }

  /**
   * Check if device is low-end (performance-wise)
   */
  private isLowEndDevice(): boolean {
    if (typeof window === 'undefined') return false;
    
    // Check hardware concurrency (CPU cores)
    const cores = (navigator as any).hardwareConcurrency || 1;
    
    // Check device memory (if available)
    const memory = (navigator as any).deviceMemory || 1;
    
    // Check connection speed
    const connection = (navigator as any).connection;
    const isSlowConnection = connection && 
      (connection.effectiveType === 'slow-2g' || 
       connection.effectiveType === '2g' || 
       connection.effectiveType === '3g');
    
    // Low-end criteria
    const isLowEnd = cores <= 2 || memory <= 2 || isSlowConnection;
    
    return isLowEnd;
  }

  /**
   * Check if device has touch capability
   */
  private hasTouch(): boolean {
    if (typeof window === 'undefined') return false;
    
    return 'ontouchstart' in window || 
           navigator.maxTouchPoints > 0 || 
           (navigator as any).msMaxTouchPoints > 0;
  }

  /**
   * Check if device has pointer (mouse) capability
   */
  private hasPointer(): boolean {
    if (typeof window === 'undefined') return false;
    
    return window.matchMedia('(pointer: fine)').matches;
  }

  /**
   * Get screen size category
   */
  private getScreenSize(): 'small' | 'medium' | 'large' | 'xlarge' {
    if (typeof window === 'undefined') return 'medium';
    
    const width = window.innerWidth;
    
    if (width < 640) return 'small';
    if (width < 1024) return 'medium';
    if (width < 1536) return 'large';
    return 'xlarge';
  }

  /**
   * Get connection speed
   */
  private getConnectionSpeed(): 'slow' | 'fast' | 'unknown' {
    if (typeof window === 'undefined') return 'unknown';
    
    const connection = (navigator as any).connection;
    if (!connection) return 'unknown';
    
    const effectiveType = connection.effectiveType;
    if (effectiveType === 'slow-2g' || effectiveType === '2g' || effectiveType === '3g') {
      return 'slow';
    }
    
    return 'fast';
  }

  /**
   * Check if user prefers reduced motion
   */
  private prefersReducedMotion(): boolean {
    if (typeof window === 'undefined') return false;
    
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  /**
   * Get device pixel ratio
   */
  private getDevicePixelRatio(): number {
    if (typeof window === 'undefined') return 1;
    
    return window.devicePixelRatio || 1;
  }

  /**
   * Get maximum WebGL texture size
   */
  private getMaxTextureSize(): number {
    if (typeof window === 'undefined') return 0;
    
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) return 0;
      
      return gl.getParameter(gl.MAX_TEXTURE_SIZE);
    } catch {
      return 0;
    }
  }

  /**
   * Check WebGL support
   */
  private supportsWebGL(): boolean {
    if (typeof window === 'undefined') return false;
    
    try {
      const canvas = document.createElement('canvas');
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
    } catch {
      return false;
    }
  }

  /**
   * Check Intersection Observer support
   */
  private supportsIntersectionObserver(): boolean {
    if (typeof window === 'undefined') return false;
    
    return 'IntersectionObserver' in window;
  }

  /**
   * Check Resize Observer support
   */
  private supportsResizeObserver(): boolean {
    if (typeof window === 'undefined') return false;
    
    return 'ResizeObserver' in window;
  }

  /**
   * Should show loading animation based on device
   */
  shouldShowLoadingAnimation(): boolean {
    const device = this.getDeviceInfo();

    // Always show loading animation unless user specifically prefers reduced motion
    if (device.reducedMotion) return false;

    // Show loading animation for all devices by default
    // Users can see the cool loading animation on both mobile and desktop
    return true;
  }

  /**
   * Get optimal animation settings based on device
   */
  getAnimationSettings() {
    const device = this.getDeviceInfo();
    
    return {
      enableComplexAnimations: device.isDesktop && !device.isLowEndDevice && !device.reducedMotion,
      enableScrollAnimations: !device.reducedMotion && device.supportsIntersectionObserver,
      enableParallax: device.isDesktop && !device.isLowEndDevice,
      enableParticles: device.isDesktop && !device.isLowEndDevice,
      animationDuration: device.isMobile ? 0.3 : 0.6,
      staggerDelay: device.isMobile ? 0.05 : 0.1,
      enableBlur: !device.isLowEndDevice,
      enableShadows: !device.isLowEndDevice,
    };
  }

  /**
   * Reset device info (useful for testing)
   */
  reset(): void {
    this.deviceInfo = null;
  }
}

// Export singleton instance
export const deviceDetection = new DeviceDetectionService();

// Export utility functions
export const isMobileDevice = () => deviceDetection.getDeviceInfo().isMobile;
export const isDesktopDevice = () => deviceDetection.getDeviceInfo().isDesktop;
export const shouldShowLoadingAnimation = () => deviceDetection.shouldShowLoadingAnimation();
export const getAnimationSettings = () => deviceDetection.getAnimationSettings();
