import { Award, Code, Coffee, Heart, Palette, <PERSON>, <PERSON>, Zap } from 'lucide-react';
import { useRef } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { useCloudPersonalInfo } from '../hooks/useCloudData';
import { ImprovedScrollAnimation, ImprovedStaggerAnimation } from './ImprovedScrollAnimations';
import SnowflakeParticleSystem from './SnowflakeParticleSystem';

const About = () => {
  const { t } = useLanguage();
  const sectionRef = useRef<HTMLElement>(null);

  // Use cloud data hook
  const { personalInfo } = useCloudPersonalInfo();

  const features = [
    {
      icon: Code,
      title: t('about.feature.architecture.title'),
      description: t('about.feature.architecture.desc'),
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Palette,
      title: t('about.feature.design.title'),
      description: t('about.feature.design.desc'),
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: Rocket,
      title: t('about.feature.performance.title'),
      description: t('about.feature.performance.desc'),
      color: 'from-orange-500 to-red-500'
    },
    {
      icon: Users,
      title: t('about.feature.collaboration.title'),
      description: t('about.feature.collaboration.desc'),
      color: 'from-green-500 to-emerald-500'
    }
  ];

  const stats = [
    { number: t('about.stats.projects'), label: t('about.projects'), icon: Award },
    { number: t('about.stats.experience'), label: t('about.experience'), icon: Coffee },
    { number: t('about.stats.clients'), label: t('about.clients'), icon: Heart },
    { number: t('about.stats.awards'), label: t('about.awards'), icon: Zap }
  ];

  return (
    <section id="about" ref={sectionRef} className="py-32 relative overflow-hidden">
      {/* Ultra Advanced Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
      
      {/* Animated Mesh Background */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            radial-gradient(circle at 25% 25%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 50% 50%, rgba(236, 72, 153, 0.05) 0%, transparent 50%)
          `
        }}></div>
      </div>

      {/* Beautiful Independent Snowflake System for About Section */}
      <SnowflakeParticleSystem
        count={8}
        showTrails={true}
        className="opacity-60"
      />

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header with Improved Scroll Animation */}
        <ImprovedScrollAnimation animation="fadeInUp" delay={0.2} className="text-center mb-20">
          <ImprovedScrollAnimation animation="scaleIn" delay={0.4}>
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 backdrop-blur-sm border border-purple-500/30 rounded-full px-6 py-3 mb-8">
              <Users className="w-5 h-5 text-purple-400" />
              <span className="text-purple-300 font-medium">{t('about.title')}</span>
            </div>
          </ImprovedScrollAnimation>

          <ImprovedScrollAnimation animation="scaleIn" delay={0.6}>
            <h2 className="text-5xl md:text-7xl font-black text-white mb-8 leading-tight">
              <span className="relative inline-block">
                <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-cyan-400 bg-clip-text text-transparent">
                  {t('about.subtitle')}
                </span>
                <div className="absolute -inset-2 bg-gradient-to-r from-purple-600/20 to-cyan-600/20 blur-2xl animate-pulse"></div>
              </span>
            </h2>
          </ImprovedScrollAnimation>

          <ImprovedScrollAnimation animation="fadeInLeft" delay={0.8}>
            <div className="w-32 h-1 bg-gradient-to-r from-purple-500 via-pink-500 to-cyan-500 mx-auto rounded-full mb-8"></div>
          </ImprovedScrollAnimation>

          <ImprovedScrollAnimation animation="fadeInRight" delay={1.0}>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
              {t('about.tagline')}
            </p>
          </ImprovedScrollAnimation>
        </ImprovedScrollAnimation>

        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Left Side - Enhanced Content with Improved Scroll Animation */}
          <ImprovedScrollAnimation animation="fadeInLeft" delay={0.4} className="space-y-8">
            <div className="space-y-6">
              <h3 className="text-3xl md:text-4xl font-bold text-white leading-tight section-title text-spacing-fix">
                <span className="text-spacing-fix">{t('about.heading')}</span>{' '}
                <span className="bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent text-spacing-fix">
                  {t('about.heading.highlight')}
                </span>{' '}
                <span className="text-spacing-fix">{t('about.heading.suffix')}</span>
              </h3>
              
              <div className="space-y-4 text-gray-400 text-lg leading-relaxed text-spacing-fix">
                <p>
                  {t('about.intro')}
                </p>

                <p>
                  {t('about.description')}
                </p>
              </div>
            </div>
            
            {/* Enhanced Tech Stack */}
            <div className="space-y-4">
              <h4 className="text-xl font-bold text-white">Core Technologies</h4>
              <div className="flex flex-wrap gap-3">
                {[
                  { name: 'React', color: 'from-blue-500 to-cyan-500' },
                  { name: 'TypeScript', color: 'from-blue-600 to-blue-800' },
                  { name: 'Node.js', color: 'from-green-500 to-green-700' },
                  { name: 'Python', color: 'from-yellow-500 to-orange-500' },
                  { name: 'AWS', color: 'from-orange-500 to-red-500' },
                  { name: 'Docker', color: 'from-blue-400 to-blue-600' }
                ].map((tech) => (
                  <span
                    key={tech.name}
                    className={`group relative px-4 py-2 bg-gradient-to-r ${tech.color} bg-opacity-20 text-white rounded-xl text-sm font-medium border border-white/10 hover:border-white/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1`}
                  >
                    {tech.name}
                    <div className={`absolute -inset-1 bg-gradient-to-r ${tech.color} opacity-0 group-hover:opacity-20 blur-lg transition-opacity duration-300 rounded-xl`}></div>
                  </span>
                ))}
              </div>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-4 pt-8">
              {stats.map((stat, index) => (
                <div
                  key={stat.label}
                  className="group relative p-6 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm rounded-2xl border border-white/10 hover:border-purple-500/30 transition-all duration-500 transform hover:scale-105"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="p-2 bg-gradient-to-r from-purple-500/20 to-cyan-500/20 rounded-lg">
                      <stat.icon className="w-5 h-5 text-purple-400" />
                    </div>
                    <div className="text-2xl font-black text-white">{stat.number}</div>
                  </div>
                  <div className="text-gray-400 text-sm font-medium">{stat.label}</div>
                  <div className="absolute -inset-1 bg-gradient-to-r from-purple-600/10 to-cyan-600/10 opacity-0 group-hover:opacity-100 blur-lg transition-opacity duration-500 rounded-2xl"></div>
                </div>
              ))}
            </div>
          </ImprovedScrollAnimation>

          {/* Right Side - 3D Interactive Card with Improved Scroll Animation */}
          <ImprovedScrollAnimation animation="fadeInRight" delay={0.6} className="relative">
            <div className="group perspective-1000">
              <div className="relative preserve-3d group-hover:rotate-y-12 transition-transform duration-1000">
                {/* Main Card */}
                <div className="relative w-full h-[500px] bg-gradient-to-br from-purple-900/20 via-gray-900/40 to-cyan-900/20 backdrop-blur-xl rounded-3xl border border-white/10 p-8 transform-gpu shadow-2xl shadow-purple-500/10">
                  {/* Floating Elements */}
                  <div className="absolute top-4 right-4 w-16 h-16 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full animate-pulse"></div>
                  <div className="absolute bottom-4 left-4 w-12 h-12 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-lg transform rotate-45 animate-bounce-slow"></div>
                  
                  <div className="h-full flex flex-col justify-center items-center text-center space-y-8">
                    {/* Profile Picture - Centered Circle */}
                    <div className="relative">
                      <div className="w-32 h-32 rounded-full overflow-hidden shadow-2xl ring-4 ring-purple-400/30 relative mx-auto" style={{ backgroundColor: 'white', transform: 'scale(1.05)' }}>
                        <img
                          src={personalInfo?.avatar || "/Admin.png"}
                          alt={`${personalInfo?.name || 'Nural Bhardwaj'} - ${personalInfo?.title || 'Full Stack Developer and UI/UX Designer'}`}
                          className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                          loading="eager"
                          onLoad={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.opacity = '1';
                            const fallback = target.nextElementSibling as HTMLElement;
                            if (fallback) fallback.style.display = 'none';
                          }}
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const fallback = target.nextElementSibling as HTMLElement;
                            if (fallback) fallback.style.display = 'flex';
                          }}
                          style={{
                            opacity: 0,
                            transition: 'opacity 0.3s ease',
                            objectPosition: 'center center',
                            objectFit: 'cover',
                            transform: 'scale(1.2)'
                          }}
                        />
                        {/* Fallback initials (hidden by default) */}
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-green-500 to-yellow-500 rounded-full flex items-center justify-center text-4xl font-black text-white" style={{ display: 'none' }}>
                          {personalInfo?.name ? personalInfo.name.split(' ').map((n: string) => n[0]).join('') : 'NB'}
                        </div>
                      </div>
                      {/* Completely removed blur effect to prevent covering profile picture */}
                      
                      {/* Status Indicator */}
                      <div className="absolute -bottom-2 -right-2 flex items-center space-x-2 bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                        <span>Available</span>
                      </div>
                    </div>
                    
                    {/* Info */}
                    <div className="space-y-4">
                      <h4 className="text-3xl font-black text-white">{personalInfo?.name || 'Nural Bhardwaj'}</h4>
                      <p className="text-xl text-purple-300 font-semibold">{personalInfo?.title || 'Full Stack Developer'}</p>
                      <div className="flex items-center justify-center space-x-2 text-gray-400">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span className="text-sm">{personalInfo?.location || 'Gurugram, IN'}</span>
                      </div>
                    </div>
                    
                    {/* Enhanced Quote */}
                    <div className="relative">
                      <div className="text-gray-300 text-lg italic font-light leading-relaxed">
                        "{personalInfo?.bio || 'Transforming complex problems into elegant solutions through innovative technology and user-centered design'}"
                      </div>
                      <div className="absolute -top-2 -left-2 text-purple-400 text-4xl opacity-50">"</div>
                      <div className="absolute -bottom-2 -right-2 text-purple-400 text-4xl opacity-50">"</div>
                    </div>

                    {/* Achievement Stats */}
                    <div className="grid grid-cols-3 gap-4 pt-6 border-t border-white/10">
                      <div className="text-center">
                        <div className="text-2xl font-black bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">50+</div>
                        <div className="text-xs text-gray-400">Projects</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-black bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">5+</div>
                        <div className="text-xs text-gray-400">Years Exp</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-black bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">100%</div>
                        <div className="text-xs text-gray-400">Satisfaction</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Clean Shadow Card - positioned behind content */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-900/5 to-cyan-900/5 rounded-3xl transform translate-x-2 translate-y-2 -z-20 border border-white/5"></div>
              </div>
            </div>
          </ImprovedScrollAnimation>
        </div>

        {/* Enhanced Features Grid with Improved Scroll Animation */}
        <ImprovedStaggerAnimation className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature) => (
            <div
              key={feature.title}
              className="group relative p-8 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm rounded-3xl border border-white/10 hover:border-purple-500/30 transition-all duration-300 transform hover:scale-102"
            >
              {/* Icon Container */}
              <div className="relative mb-6">
                <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center group-hover:scale-105 transition-transform duration-300 shadow-lg`}>
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
              </div>
              
              {/* Content */}
              <h4 className="text-xl font-bold text-white mb-4 group-hover:text-purple-300 transition-colors duration-300">
                {feature.title}
              </h4>
              <p className="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors duration-300">
                {feature.description}
              </p>
              
              {/* Hover Effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 to-cyan-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
              
              {/* Corner Accent */}
              <div className="absolute top-4 right-4 w-2 h-2 bg-gradient-to-r from-purple-400 to-cyan-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>
          ))}
        </ImprovedStaggerAnimation>
      </div>
    </section>
  );
};

export default About;