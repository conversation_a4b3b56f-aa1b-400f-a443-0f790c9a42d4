import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      // Optimize JSX runtime
      jsxRuntime: 'automatic'
    })
  ],
  base: '/',
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    // Enhanced build optimizations
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug']
      }
    },
    // Optimize chunk sizes - increased limit for better performance
    chunkSizeWarningLimit: 1500,
    rollupOptions: {
      output: {
        // Advanced manual chunking strategy for optimal loading
        manualChunks: (id) => {
          // Core React libraries - highest priority
          if (id.includes('node_modules/react') || id.includes('node_modules/react-dom')) {
            return 'react-vendor';
          }

          // Animation libraries - separate chunk for better caching
          if (id.includes('node_modules/framer-motion')) {
            return 'animations';
          }

          // Large PDF and document libraries
          if (id.includes('node_modules/jspdf') || id.includes('node_modules/html2canvas')) {
            return 'document-libs';
          }

          // Email and external services
          if (id.includes('node_modules/@emailjs') || id.includes('node_modules/dompurify')) {
            return 'services';
          }

          // UI utilities and icons
          if (id.includes('node_modules/lucide-react') || id.includes('node_modules/react-hot-toast')) {
            return 'ui-utils';
          }

          // Router and navigation
          if (id.includes('node_modules/react-router')) {
            return 'router';
          }

          // Admin panel components - lazy loaded
          if (id.includes('src/components/admin/')) {
            return 'admin-panel';
          }

          // Advanced services - lazy loaded
          if (id.includes('src/services/advanced') || id.includes('src/services/security') || id.includes('src/services/performance')) {
            return 'advanced-services';
          }

          // Memory and optimization services
          if (id.includes('src/services/memory') || id.includes('src/services/cdn') || id.includes('src/services/caching')) {
            return 'optimization-services';
          }

          // Core utilities
          if (id.includes('src/utils/') || id.includes('src/hooks/')) {
            return 'core-utils';
          }

          // All other node_modules
          if (id.includes('node_modules')) {
            return 'vendor';
          }
        },
        // Optimize asset naming for better caching
        chunkFileNames: () => {
          return `js/[name]-[hash].js`;
        },
        assetFileNames: (assetInfo) => {
          const fileName = assetInfo.names?.[0] || 'asset';
          const info = fileName.split('.');
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `images/[name]-[hash][extname]`;
          }
          if (/css/i.test(ext)) {
            return `css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        }
      }
    },
    // Enable CSS code splitting
    cssCodeSplit: true,
    // Optimize asset inlining
    assetsInlineLimit: 4096
  },
  optimizeDeps: {
    // Exclude large libraries from pre-bundling
    exclude: ['lucide-react'],
    // Include commonly used dependencies
    include: ['react', 'react-dom', 'framer-motion']
  },
  // Enhanced server configuration for development
  server: {
    // Optimize HMR
    hmr: {
      overlay: false
    }
  },
  // Preview server optimizations
  preview: {
    port: 4173,
    strictPort: true,
    // Enable compression for preview
    headers: {
      'Cache-Control': 'public, max-age=31536000'
    }
  }
});
