// Content Security Policy (CSP) Service
// Implements XSS protection and secure content loading

export interface CSPDirectives {
  'default-src': string[];
  'script-src': string[];
  'style-src': string[];
  'img-src': string[];
  'font-src': string[];
  'connect-src': string[];
  'media-src': string[];
  'object-src': string[];
  'child-src': string[];
  'frame-src': string[];
  'worker-src': string[];
  'manifest-src': string[];
  'base-uri': string[];
  'form-action': string[];
  'frame-ancestors': string[];
  'upgrade-insecure-requests'?: boolean;
  'block-all-mixed-content'?: boolean;
}

export interface CSPViolation {
  timestamp: number;
  directive: string;
  blockedURI: string;
  documentURI: string;
  violatedDirective: string;
  originalPolicy: string;
  sourceFile?: string;
  lineNumber?: number;
  columnNumber?: number;
}

class CSPService {
  private static instance: CSPService;
  private violations: CSPViolation[] = [];
  private nonces: Map<string, string> = new Map();
  private reportEndpoint: string = '/api/csp-report';

  // Enhanced secure CSP configuration for portfolio
  private cspDirectives: CSPDirectives = {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-inline'", // Required for dynamic imports and React
      "'unsafe-eval'", // Required for development and dynamic loading
      'https://www.google.com',
      'https://www.gstatic.com',
      'https://js.hcaptcha.com',
      'https://challenges.cloudflare.com',
      'https://www.googletagmanager.com',
      'https://www.google-analytics.com',
      'https://cdn.emailjs.com',
      'https://api.emailjs.com'
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'", // Required for styled-components and CSS-in-JS
      'https://fonts.googleapis.com',
      'https://cdn.jsdelivr.net'
    ],
    'img-src': [
      "'self'",
      'data:',
      'blob:',
      'https:',
      'https://www.google.com',
      'https://www.gstatic.com',
      'https://images.unsplash.com',
      'https://via.placeholder.com'
    ],
    'font-src': [
      "'self'",
      'data:',
      'https://fonts.gstatic.com',
      'https://cdn.jsdelivr.net'
    ],
    'connect-src': [
      "'self'",
      'https://api.emailjs.com',
      'https://www.google-analytics.com',
      'https://www.googletagmanager.com',
      'https://hcaptcha.com',
      'https://*.hcaptcha.com',
      'https://challenges.cloudflare.com',
      'https://api.github.com',        // GitHub API for cloud sync
      'https://github.com',            // GitHub main domain
      'https://*.github.com',          // GitHub subdomains
      'https://httpbin.org',           // Network testing API
      'wss://localhost:*',             // For development WebSocket
      'ws://localhost:*'               // For development WebSocket
    ],
    'media-src': [
      "'self'",
      'data:',
      'blob:'
    ],
    'object-src': ["'none'"],
    'child-src': [
      "'self'",
      'https://www.google.com',
      'https://hcaptcha.com',
      'https://*.hcaptcha.com'
    ],
    'frame-src': [
      "'self'",
      'https://www.google.com',
      'https://hcaptcha.com',
      'https://*.hcaptcha.com',
      'https://challenges.cloudflare.com'
    ],
    'worker-src': [
      "'self'",
      'blob:'
    ],
    'manifest-src': ["'self'"],
    'base-uri': ["'self'"],
    'form-action': [
      "'self'",
      'https://api.emailjs.com'
    ],
    'frame-ancestors': ["'none'"],
    'upgrade-insecure-requests': true,
    'block-all-mixed-content': true
  };

  private constructor() {
    this.initializeCSP();
    this.setupViolationReporting();
  }

  public static getInstance(): CSPService {
    if (!CSPService.instance) {
      CSPService.instance = new CSPService();
    }
    return CSPService.instance;
  }

  // Initialize CSP
  private initializeCSP(): void {
    // Apply CSP via meta tag (fallback method)
    this.applyCSPMetaTag();
    
    // Generate nonces for inline scripts
    this.generateNonces();
    
    console.log('🛡️ Content Security Policy initialized');
  }

  // Apply CSP via meta tag
  private applyCSPMetaTag(): void {
    const existingCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (existingCSP) {
      existingCSP.remove();
    }

    const cspMeta = document.createElement('meta');
    cspMeta.httpEquiv = 'Content-Security-Policy';
    cspMeta.content = this.generateCSPString();
    document.head.appendChild(cspMeta);
  }

  // Generate CSP string from directives
  private generateCSPString(): string {
    const directives: string[] = [];

    for (const [directive, values] of Object.entries(this.cspDirectives)) {
      // Skip frame-ancestors for meta tag implementation (not supported)
      if (directive === 'frame-ancestors') {
        continue;
      }

      if (directive === 'upgrade-insecure-requests' && values === true) {
        directives.push('upgrade-insecure-requests');
      } else if (directive === 'block-all-mixed-content' && values === true) {
        directives.push('block-all-mixed-content');
      } else if (Array.isArray(values) && values.length > 0) {
        directives.push(`${directive} ${values.join(' ')}`);
      }
    }

    return directives.join('; ');
  }

  // Generate nonces for inline scripts
  private generateNonces(): void {
    const nonce = this.generateNonce();
    this.nonces.set('script', nonce);
    this.nonces.set('style', nonce);
  }

  // Generate a cryptographically secure nonce
  private generateNonce(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array));
  }

  // Get nonce for script/style
  public getNonce(type: 'script' | 'style'): string {
    return this.nonces.get(type) || '';
  }

  // Add nonce to script element
  public addNonceToScript(script: HTMLScriptElement): void {
    const nonce = this.getNonce('script');
    if (nonce) {
      script.nonce = nonce;
    }
  }

  // Add nonce to style element
  public addNonceToStyle(style: HTMLStyleElement): void {
    const nonce = this.getNonce('style');
    if (nonce) {
      style.nonce = nonce;
    }
  }

  // Setup CSP violation reporting
  private setupViolationReporting(): void {
    document.addEventListener('securitypolicyviolation', (event) => {
      this.handleCSPViolation(event);
    });

    // Also listen for deprecated report-uri violations
    window.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'csp-violation') {
        this.handleLegacyCSPViolation(event.data);
      }
    });
  }

  // Handle CSP violations
  private handleCSPViolation(event: SecurityPolicyViolationEvent): void {
    const violation: CSPViolation = {
      timestamp: Date.now(),
      directive: event.effectiveDirective,
      blockedURI: event.blockedURI,
      documentURI: event.documentURI,
      violatedDirective: event.violatedDirective,
      originalPolicy: event.originalPolicy,
      sourceFile: event.sourceFile,
      lineNumber: event.lineNumber,
      columnNumber: event.columnNumber
    };

    this.violations.push(violation);
    this.logViolation(violation);
    this.reportViolation(violation);

    // Keep only last 100 violations
    if (this.violations.length > 100) {
      this.violations = this.violations.slice(-100);
    }
  }

  // Handle legacy CSP violations
  private handleLegacyCSPViolation(data: any): void {
    const violation: CSPViolation = {
      timestamp: Date.now(),
      directive: data.directive || 'unknown',
      blockedURI: data.blockedURI || 'unknown',
      documentURI: data.documentURI || window.location.href,
      violatedDirective: data.violatedDirective || 'unknown',
      originalPolicy: data.originalPolicy || 'unknown'
    };

    this.violations.push(violation);
    this.logViolation(violation);
  }

  // Log CSP violation
  private logViolation(violation: CSPViolation): void {
    console.warn('🚨 CSP Violation detected:', {
      directive: violation.directive,
      blockedURI: violation.blockedURI,
      sourceFile: violation.sourceFile,
      line: violation.lineNumber,
      column: violation.columnNumber,
      timestamp: new Date(violation.timestamp).toISOString()
    });

    // Send to analytics if available
    if (typeof gtag !== 'undefined') {
      gtag('event', 'csp_violation', {
        event_category: 'Security',
        event_label: violation.directive,
        custom_parameters: {
          blocked_uri: violation.blockedURI,
          violated_directive: violation.violatedDirective
        }
      });
    }
  }

  // Report violation to server (disabled for static hosting)
  private async reportViolation(violation: CSPViolation): Promise<void> {
    // Skip server reporting for static hosting to prevent 405 errors
    // In production with a backend, uncomment the following:
    /*
    try {
      // In production, send to your CSP reporting endpoint
      const response = await fetch(this.reportEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(violation)
      });

      if (!response.ok) {
        console.warn('Failed to report CSP violation to server');
      }
    } catch (error) {
      // Silently fail - don't want CSP reporting to break the app
      console.debug('CSP reporting endpoint not available:', error);
    }
    */
  }

  // Get recent violations
  public getViolations(limit: number = 50): CSPViolation[] {
    return this.violations.slice(-limit);
  }

  // Get violation statistics
  public getViolationStats(): { total: number; byDirective: Record<string, number> } {
    const byDirective: Record<string, number> = {};
    
    this.violations.forEach(violation => {
      byDirective[violation.directive] = (byDirective[violation.directive] || 0) + 1;
    });

    return {
      total: this.violations.length,
      byDirective
    };
  }

  // Update CSP directives
  public updateDirectives(newDirectives: Partial<CSPDirectives>): void {
    this.cspDirectives = { ...this.cspDirectives, ...newDirectives };
    this.applyCSPMetaTag();
    console.log('🛡️ CSP directives updated');
  }

  // Add allowed source to directive
  public addAllowedSource(directive: keyof CSPDirectives, source: string): void {
    if (Array.isArray(this.cspDirectives[directive])) {
      const sources = this.cspDirectives[directive] as string[];
      if (!sources.includes(source)) {
        sources.push(source);
        this.applyCSPMetaTag();
        console.log(`🛡️ Added ${source} to ${directive}`);
      }
    }
  }

  // Remove allowed source from directive
  public removeAllowedSource(directive: keyof CSPDirectives, source: string): void {
    if (Array.isArray(this.cspDirectives[directive])) {
      const sources = this.cspDirectives[directive] as string[];
      const index = sources.indexOf(source);
      if (index > -1) {
        sources.splice(index, 1);
        this.applyCSPMetaTag();
        console.log(`🛡️ Removed ${source} from ${directive}`);
      }
    }
  }

  // Get current CSP configuration
  public getCSPConfig(): CSPDirectives {
    return { ...this.cspDirectives };
  }

  // Validate if a URL is allowed by CSP
  public isURLAllowed(url: string, directive: keyof CSPDirectives): boolean {
    const sources = this.cspDirectives[directive];
    if (!Array.isArray(sources)) return false;

    // Check if URL matches any allowed source
    return sources.some(source => {
      if (source === "'self'") {
        return url.startsWith(window.location.origin);
      }
      if (source === "'none'") {
        return false;
      }
      if (source === 'data:') {
        return url.startsWith('data:');
      }
      if (source === 'blob:') {
        return url.startsWith('blob:');
      }
      if (source === 'https:') {
        return url.startsWith('https:');
      }
      if (source.includes('*')) {
        const pattern = source.replace(/\*/g, '.*');
        return new RegExp(pattern).test(url);
      }
      return url.startsWith(source);
    });
  }

  // Create secure script element
  public createSecureScript(src?: string, content?: string): HTMLScriptElement {
    const script = document.createElement('script');
    
    if (src) {
      if (!this.isURLAllowed(src, 'script-src')) {
        console.warn('🚨 Script source not allowed by CSP:', src);
        return script;
      }
      script.src = src;
    }
    
    if (content) {
      script.textContent = content;
      this.addNonceToScript(script);
    }
    
    return script;
  }

  // Create secure style element
  public createSecureStyle(href?: string, content?: string): HTMLStyleElement | HTMLLinkElement {
    if (href) {
      if (!this.isURLAllowed(href, 'style-src')) {
        console.warn('🚨 Style source not allowed by CSP:', href);
        return document.createElement('style');
      }
      
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;
      return link;
    }
    
    const style = document.createElement('style');
    if (content) {
      style.textContent = content;
      this.addNonceToStyle(style);
    }
    
    return style;
  }

  // Enable development mode (relaxed CSP)
  public enableDevelopmentMode(): void {
    this.cspDirectives['script-src'].push("'unsafe-eval'", "'unsafe-inline'");
    this.cspDirectives['style-src'].push("'unsafe-inline'");
    this.cspDirectives['connect-src'].push('ws://localhost:*', 'wss://localhost:*');
    this.applyCSPMetaTag();
    console.log('🛡️ CSP development mode enabled');
  }

  // Enable production mode (strict CSP)
  public enableProductionMode(): void {
    // Remove unsafe directives
    this.cspDirectives['script-src'] = this.cspDirectives['script-src'].filter(
      src => !src.includes('unsafe')
    );
    this.cspDirectives['style-src'] = this.cspDirectives['style-src'].filter(
      src => src !== "'unsafe-inline'"
    );
    this.cspDirectives['connect-src'] = this.cspDirectives['connect-src'].filter(
      src => !src.includes('localhost')
    );
    this.applyCSPMetaTag();
    console.log('🛡️ CSP production mode enabled');
  }

  // Clear all violations
  public clearViolations(): void {
    this.violations = [];
    console.log('🛡️ CSP violations cleared');
  }

  // Export CSP configuration for server-side implementation
  public exportServerConfig(): string {
    return `
# Content Security Policy Configuration
# Add this to your server configuration

# Nginx example:
add_header Content-Security-Policy "${this.generateCSPString()}";

# Apache example:
Header always set Content-Security-Policy "${this.generateCSPString()}"

# Express.js example:
app.use((req, res, next) => {
  res.setHeader('Content-Security-Policy', '${this.generateCSPString()}');
  next();
});
`;
  }
}

// Export singleton instance
export const cspService = CSPService.getInstance();
