import React, { useState, useEffect } from 'react';
import App from '../App';
import AdminPanel from './AdminPanel';

const Router: React.FC = () => {
  const [currentPath, setCurrentPath] = useState(window.location.pathname);

  useEffect(() => {
    const handlePopState = () => {
      setCurrentPath(window.location.pathname);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  // Simple routing logic for custom domain
  if (currentPath === '/admin' || currentPath.startsWith('/admin/')) {
    return <AdminPanel />;
  }

  return <App />;
};

export default Router;
